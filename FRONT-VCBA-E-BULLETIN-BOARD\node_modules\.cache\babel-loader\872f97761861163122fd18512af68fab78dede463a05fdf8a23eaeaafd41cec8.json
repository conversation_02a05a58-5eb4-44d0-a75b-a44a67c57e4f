{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\tv-control\\\\TVEmergencyBroadcast.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { tvControlService } from '../../../services/tvControlService';\nimport { AlertTriangle, Send, X, Clock } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVEmergencyBroadcast = ({\n  settings\n}) => {\n  _s();\n  const [emergencyMessage, setEmergencyMessage] = useState('');\n  const [isConfirming, setIsConfirming] = useState(false);\n  const handleBroadcast = () => {\n    if (!emergencyMessage.trim()) return;\n    tvControlService.broadcastEmergency(emergencyMessage.trim());\n    setEmergencyMessage('');\n    setIsConfirming(false);\n  };\n  const handleClearEmergency = () => {\n    tvControlService.clearEmergency();\n  };\n  const quickMessages = [\"🚨 EMERGENCY: Please evacuate the building immediately and proceed to the designated assembly area.\", \"⚠️ ALERT: Classes are suspended due to severe weather. Please stay indoors until further notice.\", \"🔥 FIRE DRILL: This is a scheduled fire drill. Please evacuate calmly using the nearest exit.\", \"🌪️ WEATHER ALERT: Severe weather warning in effect. Remain in the building until all clear.\", \"📢 ANNOUNCEMENT: Important assembly in the main auditorium at 2:00 PM. All students and staff required.\", \"🚫 LOCKDOWN: Security lockdown in effect. Remain in current location and await further instructions.\"];\n  const inputStyle = {\n    width: '100%',\n    padding: '1rem',\n    border: '2px solid #ddd',\n    borderRadius: '8px',\n    fontSize: '1rem',\n    resize: 'vertical',\n    minHeight: '120px'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: '1.8rem',\n        fontWeight: '600',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n        size: 24,\n        color: \"#e74c3c\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), \"Emergency Broadcasting\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), settings.emergencyActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f8d7da',\n        border: '2px solid #f5c6cb',\n        borderRadius: '8px',\n        padding: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'flex-start',\n          marginBottom: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#721c24',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this), \"EMERGENCY BROADCAST ACTIVE\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClearEmergency,\n          style: {\n            background: '#dc3545',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '0.9rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(X, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), \"Clear Emergency\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '1rem',\n          borderRadius: '6px',\n          border: '1px solid #f5c6cb'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Current Message:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '0.5rem',\n            fontSize: '1.1rem'\n          },\n          children: settings.emergencyMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '1rem',\n          fontSize: '0.9rem',\n          color: '#721c24',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Clock, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), \"Active since: \", new Date(settings.lastUpdated).toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#fff3cd',\n        border: '2px solid #ffeaa7',\n        borderRadius: '8px',\n        padding: '2rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1rem 0',\n          color: '#856404'\n        },\n        children: \"Broadcast Emergency Message\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: emergencyMessage,\n        onChange: e => setEmergencyMessage(e.target.value),\n        placeholder: \"Enter emergency message to broadcast to all TV displays...\",\n        style: {\n          ...inputStyle,\n          borderColor: emergencyMessage.length > 200 ? '#e74c3c' : '#ddd'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginTop: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.9rem',\n            color: emergencyMessage.length > 200 ? '#e74c3c' : '#6c757d'\n          },\n          children: [emergencyMessage.length, \"/200 characters\", emergencyMessage.length > 200 && ' (Message too long)']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), !isConfirming ? /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsConfirming(true),\n          disabled: !emergencyMessage.trim() || emergencyMessage.length > 200,\n          style: {\n            background: emergencyMessage.trim() && emergencyMessage.length <= 200 ? '#dc3545' : '#6c757d',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem 1.5rem',\n            borderRadius: '6px',\n            cursor: emergencyMessage.trim() && emergencyMessage.length <= 200 ? 'pointer' : 'not-allowed',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1rem',\n            fontWeight: '500'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Send, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), \"Broadcast Emergency\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsConfirming(false),\n            style: {\n              background: '#6c757d',\n              color: 'white',\n              border: 'none',\n              padding: '0.75rem 1rem',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleBroadcast,\n            style: {\n              background: '#dc3545',\n              color: 'white',\n              border: 'none',\n              padding: '0.75rem 1.5rem',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"CONFIRM BROADCAST\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f8f9fa',\n        border: '1px solid #e9ecef',\n        borderRadius: '8px',\n        padding: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50'\n        },\n        children: \"Quick Message Templates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '1rem'\n        },\n        children: quickMessages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            border: '1px solid #dee2e6',\n            borderRadius: '6px',\n            padding: '1rem',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onClick: () => setEmergencyMessage(message),\n          onMouseEnter: e => {\n            e.currentTarget.style.background = '#e3f2fd';\n            e.currentTarget.style.borderColor = '#2196f3';\n          },\n          onMouseLeave: e => {\n            e.currentTarget.style.background = 'white';\n            e.currentTarget.style.borderColor = '#dee2e6';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.95rem',\n              lineHeight: '1.4'\n            },\n            children: message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        background: '#f8d7da',\n        border: '1px solid #f5c6cb',\n        borderRadius: '8px',\n        padding: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 1rem 0',\n          color: '#721c24',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), \"Important Notice\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          margin: 0,\n          paddingLeft: '1.5rem',\n          color: '#721c24'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Emergency broadcasts override all other content on TV displays\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Messages will be displayed prominently with flashing alerts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Only use for genuine emergencies or critical announcements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Emergency broadcasts remain active until manually cleared\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"All connected TV displays will show the message immediately\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(TVEmergencyBroadcast, \"HkgC5ZN/zv86MBOg+72oAvpO5uo=\");\n_c = TVEmergencyBroadcast;\nexport default TVEmergencyBroadcast;\nvar _c;\n$RefreshReg$(_c, \"TVEmergencyBroadcast\");", "map": {"version": 3, "names": ["React", "useState", "tvControlService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Send", "X", "Clock", "jsxDEV", "_jsxDEV", "TVEmergencyBroadcast", "settings", "_s", "emergencyMessage", "setEmergencyMessage", "isConfirming", "setIsConfirming", "handleBroadcast", "trim", "broadcastEmergency", "handleClearEmergency", "clearEmergency", "quickMessages", "inputStyle", "width", "padding", "border", "borderRadius", "fontSize", "resize", "minHeight", "children", "style", "fontWeight", "margin", "color", "display", "alignItems", "gap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "emergencyActive", "background", "marginBottom", "justifyContent", "onClick", "cursor", "marginTop", "Date", "lastUpdated", "toLocaleString", "value", "onChange", "e", "target", "placeholder", "borderColor", "length", "disabled", "map", "message", "index", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "lineHeight", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/tv-control/TVEmergencyBroadcast.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { tvControlService, TVDisplaySettings } from '../../../services/tvControlService';\nimport { AlertTriangle, Send, X, Clock } from 'lucide-react';\n\ninterface TVEmergencyBroadcastProps {\n  settings: TVDisplaySettings;\n}\n\nconst TVEmergencyBroadcast: React.FC<TVEmergencyBroadcastProps> = ({ settings }) => {\n  const [emergencyMessage, setEmergencyMessage] = useState('');\n  const [isConfirming, setIsConfirming] = useState(false);\n\n  const handleBroadcast = () => {\n    if (!emergencyMessage.trim()) return;\n    \n    tvControlService.broadcastEmergency(emergencyMessage.trim());\n    setEmergencyMessage('');\n    setIsConfirming(false);\n  };\n\n  const handleClearEmergency = () => {\n    tvControlService.clearEmergency();\n  };\n\n  const quickMessages = [\n    \"🚨 EMERGENCY: Please evacuate the building immediately and proceed to the designated assembly area.\",\n    \"⚠️ ALERT: Classes are suspended due to severe weather. Please stay indoors until further notice.\",\n    \"🔥 FIRE DRILL: This is a scheduled fire drill. Please evacuate calmly using the nearest exit.\",\n    \"🌪️ WEATHER ALERT: Severe weather warning in effect. Remain in the building until all clear.\",\n    \"📢 ANNOUNCEMENT: Important assembly in the main auditorium at 2:00 PM. All students and staff required.\",\n    \"🚫 LOCKDOWN: Security lockdown in effect. Remain in current location and await further instructions.\"\n  ];\n\n  const inputStyle = {\n    width: '100%',\n    padding: '1rem',\n    border: '2px solid #ddd',\n    borderRadius: '8px',\n    fontSize: '1rem',\n    resize: 'vertical' as const,\n    minHeight: '120px'\n  };\n\n  return (\n    <div>\n      <h2 style={{\n        fontSize: '1.8rem',\n        fontWeight: '600',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      }}>\n        <AlertTriangle size={24} color=\"#e74c3c\" />\n        Emergency Broadcasting\n      </h2>\n\n      {/* Current Emergency Status */}\n      {settings.emergencyActive && (\n        <div style={{\n          background: '#f8d7da',\n          border: '2px solid #f5c6cb',\n          borderRadius: '8px',\n          padding: '1.5rem',\n          marginBottom: '2rem'\n        }}>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '1rem'\n          }}>\n            <h3 style={{\n              margin: 0,\n              color: '#721c24',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <AlertTriangle size={20} />\n              EMERGENCY BROADCAST ACTIVE\n            </h3>\n            \n            <button\n              onClick={handleClearEmergency}\n              style={{\n                background: '#dc3545',\n                color: 'white',\n                border: 'none',\n                padding: '0.5rem 1rem',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.9rem'\n              }}\n            >\n              <X size={16} />\n              Clear Emergency\n            </button>\n          </div>\n          \n          <div style={{\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            border: '1px solid #f5c6cb'\n          }}>\n            <strong>Current Message:</strong>\n            <div style={{ marginTop: '0.5rem', fontSize: '1.1rem' }}>\n              {settings.emergencyMessage}\n            </div>\n          </div>\n          \n          <div style={{\n            marginTop: '1rem',\n            fontSize: '0.9rem',\n            color: '#721c24',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}>\n            <Clock size={16} />\n            Active since: {new Date(settings.lastUpdated).toLocaleString()}\n          </div>\n        </div>\n      )}\n\n      {/* Emergency Message Input */}\n      <div style={{\n        background: '#fff3cd',\n        border: '2px solid #ffeaa7',\n        borderRadius: '8px',\n        padding: '2rem',\n        marginBottom: '2rem'\n      }}>\n        <h3 style={{\n          margin: '0 0 1rem 0',\n          color: '#856404'\n        }}>\n          Broadcast Emergency Message\n        </h3>\n        \n        <textarea\n          value={emergencyMessage}\n          onChange={(e) => setEmergencyMessage(e.target.value)}\n          placeholder=\"Enter emergency message to broadcast to all TV displays...\"\n          style={{\n            ...inputStyle,\n            borderColor: emergencyMessage.length > 200 ? '#e74c3c' : '#ddd'\n          }}\n        />\n        \n        <div style={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginTop: '1rem'\n        }}>\n          <div style={{\n            fontSize: '0.9rem',\n            color: emergencyMessage.length > 200 ? '#e74c3c' : '#6c757d'\n          }}>\n            {emergencyMessage.length}/200 characters\n            {emergencyMessage.length > 200 && ' (Message too long)'}\n          </div>\n          \n          {!isConfirming ? (\n            <button\n              onClick={() => setIsConfirming(true)}\n              disabled={!emergencyMessage.trim() || emergencyMessage.length > 200}\n              style={{\n                background: emergencyMessage.trim() && emergencyMessage.length <= 200 ? '#dc3545' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '6px',\n                cursor: emergencyMessage.trim() && emergencyMessage.length <= 200 ? 'pointer' : 'not-allowed',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '1rem',\n                fontWeight: '500'\n              }}\n            >\n              <Send size={16} />\n              Broadcast Emergency\n            </button>\n          ) : (\n            <div style={{ display: 'flex', gap: '1rem' }}>\n              <button\n                onClick={() => setIsConfirming(false)}\n                style={{\n                  background: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  padding: '0.75rem 1rem',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleBroadcast}\n                style={{\n                  background: '#dc3545',\n                  color: 'white',\n                  border: 'none',\n                  padding: '0.75rem 1.5rem',\n                  borderRadius: '6px',\n                  cursor: 'pointer',\n                  fontWeight: 'bold'\n                }}\n              >\n                CONFIRM BROADCAST\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Quick Message Templates */}\n      <div style={{\n        background: '#f8f9fa',\n        border: '1px solid #e9ecef',\n        borderRadius: '8px',\n        padding: '2rem'\n      }}>\n        <h3 style={{\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50'\n        }}>\n          Quick Message Templates\n        </h3>\n        \n        <div style={{\n          display: 'grid',\n          gap: '1rem'\n        }}>\n          {quickMessages.map((message, index) => (\n            <div\n              key={index}\n              style={{\n                background: 'white',\n                border: '1px solid #dee2e6',\n                borderRadius: '6px',\n                padding: '1rem',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              }}\n              onClick={() => setEmergencyMessage(message)}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.background = '#e3f2fd';\n                e.currentTarget.style.borderColor = '#2196f3';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.background = 'white';\n                e.currentTarget.style.borderColor = '#dee2e6';\n              }}\n            >\n              <div style={{\n                fontSize: '0.95rem',\n                lineHeight: '1.4'\n              }}>\n                {message}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Warning Notice */}\n      <div style={{\n        marginTop: '2rem',\n        background: '#f8d7da',\n        border: '1px solid #f5c6cb',\n        borderRadius: '8px',\n        padding: '1.5rem'\n      }}>\n        <h4 style={{\n          margin: '0 0 1rem 0',\n          color: '#721c24',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <AlertTriangle size={18} />\n          Important Notice\n        </h4>\n        <ul style={{\n          margin: 0,\n          paddingLeft: '1.5rem',\n          color: '#721c24'\n        }}>\n          <li>Emergency broadcasts override all other content on TV displays</li>\n          <li>Messages will be displayed prominently with flashing alerts</li>\n          <li>Only use for genuine emergencies or critical announcements</li>\n          <li>Emergency broadcasts remain active until manually cleared</li>\n          <li>All connected TV displays will show the message immediately</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default TVEmergencyBroadcast;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,gBAAgB,QAA2B,oCAAoC;AACxF,SAASC,aAAa,EAAEC,IAAI,EAAEC,CAAC,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7D,MAAMC,oBAAyD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAClF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACJ,gBAAgB,CAACK,IAAI,CAAC,CAAC,EAAE;IAE9Bf,gBAAgB,CAACgB,kBAAkB,CAACN,gBAAgB,CAACK,IAAI,CAAC,CAAC,CAAC;IAC5DJ,mBAAmB,CAAC,EAAE,CAAC;IACvBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjCjB,gBAAgB,CAACkB,cAAc,CAAC,CAAC;EACnC,CAAC;EAED,MAAMC,aAAa,GAAG,CACpB,qGAAqG,EACrG,kGAAkG,EAClG,+FAA+F,EAC/F,8FAA8F,EAC9F,yGAAyG,EACzG,sGAAsG,CACvG;EAED,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,UAAmB;IAC3BC,SAAS,EAAE;EACb,CAAC;EAED,oBACErB,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAIuB,KAAK,EAAE;QACTJ,QAAQ,EAAE,QAAQ;QAClBK,UAAU,EAAE,KAAK;QACjBC,MAAM,EAAE,YAAY;QACpBC,KAAK,EAAE,SAAS;QAChBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAP,QAAA,gBACAtB,OAAA,CAACL,aAAa;QAACmC,IAAI,EAAE,EAAG;QAACJ,KAAK,EAAC;MAAS;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,0BAE7C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAGJhC,QAAQ,CAACiC,eAAe,iBACvBnC,OAAA;MAAKuB,KAAK,EAAE;QACVa,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBF,OAAO,EAAE,QAAQ;QACjBqB,YAAY,EAAE;MAChB,CAAE;MAAAf,QAAA,gBACAtB,OAAA;QAAKuB,KAAK,EAAE;UACVI,OAAO,EAAE,MAAM;UACfW,cAAc,EAAE,eAAe;UAC/BV,UAAU,EAAE,YAAY;UACxBS,YAAY,EAAE;QAChB,CAAE;QAAAf,QAAA,gBACAtB,OAAA;UAAIuB,KAAK,EAAE;YACTE,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE;UACP,CAAE;UAAAP,QAAA,gBACAtB,OAAA,CAACL,aAAa;YAACmC,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELlC,OAAA;UACEuC,OAAO,EAAE5B,oBAAqB;UAC9BY,KAAK,EAAE;YACLa,UAAU,EAAE,SAAS;YACrBV,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,aAAa;YACtBE,YAAY,EAAE,KAAK;YACnBsB,MAAM,EAAE,SAAS;YACjBb,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbV,QAAQ,EAAE;UACZ,CAAE;UAAAG,QAAA,gBAEFtB,OAAA,CAACH,CAAC;YAACiC,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlC,OAAA;QAAKuB,KAAK,EAAE;UACVa,UAAU,EAAE,OAAO;UACnBpB,OAAO,EAAE,MAAM;UACfE,YAAY,EAAE,KAAK;UACnBD,MAAM,EAAE;QACV,CAAE;QAAAK,QAAA,gBACAtB,OAAA;UAAAsB,QAAA,EAAQ;QAAgB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjClC,OAAA;UAAKuB,KAAK,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEtB,QAAQ,EAAE;UAAS,CAAE;UAAAG,QAAA,EACrDpB,QAAQ,CAACE;QAAgB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA;QAAKuB,KAAK,EAAE;UACVkB,SAAS,EAAE,MAAM;UACjBtB,QAAQ,EAAE,QAAQ;UAClBO,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAP,QAAA,gBACAtB,OAAA,CAACF,KAAK;UAACgC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBACL,EAAC,IAAIQ,IAAI,CAACxC,QAAQ,CAACyC,WAAW,CAAC,CAACC,cAAc,CAAC,CAAC;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlC,OAAA;MAAKuB,KAAK,EAAE;QACVa,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBF,OAAO,EAAE,MAAM;QACfqB,YAAY,EAAE;MAChB,CAAE;MAAAf,QAAA,gBACAtB,OAAA;QAAIuB,KAAK,EAAE;UACTE,MAAM,EAAE,YAAY;UACpBC,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELlC,OAAA;QACE6C,KAAK,EAAEzC,gBAAiB;QACxB0C,QAAQ,EAAGC,CAAC,IAAK1C,mBAAmB,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QACrDI,WAAW,EAAC,4DAA4D;QACxE1B,KAAK,EAAE;UACL,GAAGT,UAAU;UACboC,WAAW,EAAE9C,gBAAgB,CAAC+C,MAAM,GAAG,GAAG,GAAG,SAAS,GAAG;QAC3D;MAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFlC,OAAA;QAAKuB,KAAK,EAAE;UACVI,OAAO,EAAE,MAAM;UACfW,cAAc,EAAE,eAAe;UAC/BV,UAAU,EAAE,QAAQ;UACpBa,SAAS,EAAE;QACb,CAAE;QAAAnB,QAAA,gBACAtB,OAAA;UAAKuB,KAAK,EAAE;YACVJ,QAAQ,EAAE,QAAQ;YAClBO,KAAK,EAAEtB,gBAAgB,CAAC+C,MAAM,GAAG,GAAG,GAAG,SAAS,GAAG;UACrD,CAAE;UAAA7B,QAAA,GACClB,gBAAgB,CAAC+C,MAAM,EAAC,iBACzB,EAAC/C,gBAAgB,CAAC+C,MAAM,GAAG,GAAG,IAAI,qBAAqB;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,EAEL,CAAC5B,YAAY,gBACZN,OAAA;UACEuC,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,IAAI,CAAE;UACrC6C,QAAQ,EAAE,CAAChD,gBAAgB,CAACK,IAAI,CAAC,CAAC,IAAIL,gBAAgB,CAAC+C,MAAM,GAAG,GAAI;UACpE5B,KAAK,EAAE;YACLa,UAAU,EAAEhC,gBAAgB,CAACK,IAAI,CAAC,CAAC,IAAIL,gBAAgB,CAAC+C,MAAM,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS;YAC7FzB,KAAK,EAAE,OAAO;YACdT,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,gBAAgB;YACzBE,YAAY,EAAE,KAAK;YACnBsB,MAAM,EAAEpC,gBAAgB,CAACK,IAAI,CAAC,CAAC,IAAIL,gBAAgB,CAAC+C,MAAM,IAAI,GAAG,GAAG,SAAS,GAAG,aAAa;YAC7FxB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbV,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAF,QAAA,gBAEFtB,OAAA,CAACJ,IAAI;YAACkC,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEpB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETlC,OAAA;UAAKuB,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAP,QAAA,gBAC3CtB,OAAA;YACEuC,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC,KAAK,CAAE;YACtCgB,KAAK,EAAE;cACLa,UAAU,EAAE,SAAS;cACrBV,KAAK,EAAE,OAAO;cACdT,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,cAAc;cACvBE,YAAY,EAAE,KAAK;cACnBsB,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA;YACEuC,OAAO,EAAE/B,eAAgB;YACzBe,KAAK,EAAE;cACLa,UAAU,EAAE,SAAS;cACrBV,KAAK,EAAE,OAAO;cACdT,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,gBAAgB;cACzBE,YAAY,EAAE,KAAK;cACnBsB,MAAM,EAAE,SAAS;cACjBhB,UAAU,EAAE;YACd,CAAE;YAAAF,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAKuB,KAAK,EAAE;QACVa,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBF,OAAO,EAAE;MACX,CAAE;MAAAM,QAAA,gBACAtB,OAAA;QAAIuB,KAAK,EAAE;UACTE,MAAM,EAAE,cAAc;UACtBC,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELlC,OAAA;QAAKuB,KAAK,EAAE;UACVI,OAAO,EAAE,MAAM;UACfE,GAAG,EAAE;QACP,CAAE;QAAAP,QAAA,EACCT,aAAa,CAACwC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAChCvD,OAAA;UAEEuB,KAAK,EAAE;YACLa,UAAU,EAAE,OAAO;YACnBnB,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBF,OAAO,EAAE,MAAM;YACfwB,MAAM,EAAE,SAAS;YACjBgB,UAAU,EAAE;UACd,CAAE;UACFjB,OAAO,EAAEA,CAAA,KAAMlC,mBAAmB,CAACiD,OAAO,CAAE;UAC5CG,YAAY,EAAGV,CAAC,IAAK;YACnBA,CAAC,CAACW,aAAa,CAACnC,KAAK,CAACa,UAAU,GAAG,SAAS;YAC5CW,CAAC,CAACW,aAAa,CAACnC,KAAK,CAAC2B,WAAW,GAAG,SAAS;UAC/C,CAAE;UACFS,YAAY,EAAGZ,CAAC,IAAK;YACnBA,CAAC,CAACW,aAAa,CAACnC,KAAK,CAACa,UAAU,GAAG,OAAO;YAC1CW,CAAC,CAACW,aAAa,CAACnC,KAAK,CAAC2B,WAAW,GAAG,SAAS;UAC/C,CAAE;UAAA5B,QAAA,eAEFtB,OAAA;YAAKuB,KAAK,EAAE;cACVJ,QAAQ,EAAE,SAAS;cACnByC,UAAU,EAAE;YACd,CAAE;YAAAtC,QAAA,EACCgC;UAAO;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GAxBDqB,KAAK;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MAAKuB,KAAK,EAAE;QACVkB,SAAS,EAAE,MAAM;QACjBL,UAAU,EAAE,SAAS;QACrBnB,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBF,OAAO,EAAE;MACX,CAAE;MAAAM,QAAA,gBACAtB,OAAA;QAAIuB,KAAK,EAAE;UACTE,MAAM,EAAE,YAAY;UACpBC,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAP,QAAA,gBACAtB,OAAA,CAACL,aAAa;UAACmC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE7B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLlC,OAAA;QAAIuB,KAAK,EAAE;UACTE,MAAM,EAAE,CAAC;UACToC,WAAW,EAAE,QAAQ;UACrBnC,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,gBACAtB,OAAA;UAAAsB,QAAA,EAAI;QAA8D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvElC,OAAA;UAAAsB,QAAA,EAAI;QAA2D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpElC,OAAA;UAAAsB,QAAA,EAAI;QAA0D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnElC,OAAA;UAAAsB,QAAA,EAAI;QAAyD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClElC,OAAA;UAAAsB,QAAA,EAAI;QAA2D;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA1SIF,oBAAyD;AAAA6D,EAAA,GAAzD7D,oBAAyD;AA4S/D,eAAeA,oBAAoB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}