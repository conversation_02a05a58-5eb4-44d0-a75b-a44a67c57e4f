{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\tv-control\\\\TVDisplaySettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { tvControlService } from '../../../services/tvControlService';\nimport { Save, RotateCcw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVDisplaySettings = ({\n  settings\n}) => {\n  _s();\n  const [localSettings, setLocalSettings] = useState(settings);\n  const [hasChanges, setHasChanges] = useState(false);\n  const handleSettingChange = (key, value) => {\n    const newSettings = {\n      ...localSettings,\n      [key]: value\n    };\n    setLocalSettings(newSettings);\n    setHasChanges(JSON.stringify(newSettings) !== JSON.stringify(settings));\n  };\n  const handleSave = () => {\n    tvControlService.updateSettings(localSettings);\n    setHasChanges(false);\n  };\n  const handleReset = () => {\n    setLocalSettings(settings);\n    setHasChanges(false);\n  };\n  const inputStyle = {\n    width: '100%',\n    padding: '0.75rem',\n    border: '1px solid #ddd',\n    borderRadius: '6px',\n    fontSize: '1rem'\n  };\n  const labelStyle = {\n    display: 'block',\n    marginBottom: '0.5rem',\n    fontWeight: '500',\n    color: '#2c3e50'\n  };\n  const sectionStyle = {\n    marginBottom: '2rem',\n    padding: '1.5rem',\n    background: '#f8f9fa',\n    borderRadius: '8px',\n    border: '1px solid #e9ecef'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          margin: 0,\n          color: '#2c3e50'\n        },\n        children: \"Display Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), hasChanges && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleReset,\n          style: {\n            background: '#6c757d',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem 1.5rem',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this), \"Reset\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          style: {\n            background: '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem 1.5rem',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this), \"Save Changes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: sectionStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50'\n        },\n        children: \"General Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Display Enabled\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: localSettings.isEnabled ? 'true' : 'false',\n            onChange: e => handleSettingChange('isEnabled', e.target.value === 'true'),\n            style: inputStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"true\",\n              children: \"Enabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"false\",\n              children: \"Disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Auto-Play\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: localSettings.autoPlay ? 'true' : 'false',\n            onChange: e => handleSettingChange('autoPlay', e.target.value === 'true'),\n            style: inputStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"true\",\n              children: \"Enabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"false\",\n              children: \"Disabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Slide Interval (seconds)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            min: \"5\",\n            max: \"300\",\n            value: localSettings.slideInterval / 1000,\n            onChange: e => handleSettingChange('slideInterval', parseInt(e.target.value) * 1000),\n            style: inputStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Transition Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: localSettings.transitionType,\n            onChange: e => handleSettingChange('transitionType', e.target.value),\n            style: inputStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"slide\",\n              children: \"Slide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"fade\",\n              children: \"Fade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"none\",\n              children: \"None\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: sectionStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50'\n        },\n        children: \"Content Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Show Announcements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: localSettings.showAnnouncements ? 'true' : 'false',\n            onChange: e => handleSettingChange('showAnnouncements', e.target.value === 'true'),\n            style: inputStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"true\",\n              children: \"Yes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"false\",\n              children: \"No\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Show Calendar Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: localSettings.showCalendarEvents ? 'true' : 'false',\n            onChange: e => handleSettingChange('showCalendarEvents', e.target.value === 'true'),\n            style: inputStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"true\",\n              children: \"Yes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"false\",\n              children: \"No\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Max Announcements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            min: \"1\",\n            max: \"50\",\n            value: localSettings.maxAnnouncements,\n            onChange: e => handleSettingChange('maxAnnouncements', parseInt(e.target.value)),\n            style: inputStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: labelStyle,\n            children: \"Max Calendar Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            min: \"1\",\n            max: \"20\",\n            value: localSettings.maxEvents,\n            onChange: e => handleSettingChange('maxEvents', parseInt(e.target.value)),\n            style: inputStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: sectionStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50'\n        },\n        children: \"Preview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          border: '2px solid #dee2e6',\n          borderRadius: '8px',\n          padding: '2rem',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '1.2rem',\n            fontWeight: '600',\n            marginBottom: '1rem',\n            color: '#2c3e50'\n          },\n          children: \"Current Settings Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '1rem',\n            textAlign: 'left'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), \" \", localSettings.isEnabled ? 'Enabled' : 'Disabled']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Auto-Play:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), \" \", localSettings.autoPlay ? 'On' : 'Off']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Slide Duration:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), \" \", localSettings.slideInterval / 1000, \"s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Transition:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), \" \", localSettings.transitionType]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Announcements:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), \" \", localSettings.showAnnouncements ? `Yes (${localSettings.maxAnnouncements})` : 'No']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Events:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), \" \", localSettings.showCalendarEvents ? `Yes (${localSettings.maxEvents})` : 'No']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#e3f2fd',\n        border: '1px solid #bbdefb',\n        borderRadius: '8px',\n        padding: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 1rem 0',\n          color: '#1976d2'\n        },\n        children: \"\\uD83D\\uDCA1 Tips\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          margin: 0,\n          paddingLeft: '1.5rem',\n          color: '#1565c0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Changes are applied immediately to the TV display\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Recommended slide interval: 10-20 seconds for optimal readability\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Disable auto-play for manual control during presentations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Use fade transitions for a more professional look\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(TVDisplaySettings, \"kn/m2sJxEFUhqtnrliheNBGkEVw=\");\n_c = TVDisplaySettings;\nexport default TVDisplaySettings;\nvar _c;\n$RefreshReg$(_c, \"TVDisplaySettings\");", "map": {"version": 3, "names": ["React", "useState", "tvControlService", "Save", "RotateCcw", "jsxDEV", "_jsxDEV", "TVDisplaySettings", "settings", "_s", "localSettings", "setLocalSettings", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "handleSettingChange", "key", "value", "newSettings", "JSON", "stringify", "handleSave", "updateSettings", "handleReset", "inputStyle", "width", "padding", "border", "borderRadius", "fontSize", "labelStyle", "display", "marginBottom", "fontWeight", "color", "sectionStyle", "background", "children", "style", "justifyContent", "alignItems", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "onClick", "cursor", "size", "gridTemplateColumns", "isEnabled", "onChange", "e", "target", "autoPlay", "type", "min", "max", "slideInterval", "parseInt", "transitionType", "showAnnouncements", "showCalendarEvents", "maxAnnouncements", "maxEvents", "textAlign", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/tv-control/TVDisplaySettings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { tvControlService, TVDisplaySettings as TVSettings } from '../../../services/tvControlService';\nimport { Save, RotateCcw } from 'lucide-react';\n\ninterface TVDisplaySettingsProps {\n  settings: TVSettings;\n}\n\nconst TVDisplaySettings: React.FC<TVDisplaySettingsProps> = ({ settings }) => {\n  const [localSettings, setLocalSettings] = useState<TVSettings>(settings);\n  const [hasChanges, setHasChanges] = useState(false);\n\n  const handleSettingChange = (key: keyof TVSettings, value: any) => {\n    const newSettings = { ...localSettings, [key]: value };\n    setLocalSettings(newSettings);\n    setHasChanges(JSON.stringify(newSettings) !== JSON.stringify(settings));\n  };\n\n  const handleSave = () => {\n    tvControlService.updateSettings(localSettings);\n    setHasChanges(false);\n  };\n\n  const handleReset = () => {\n    setLocalSettings(settings);\n    setHasChanges(false);\n  };\n\n  const inputStyle = {\n    width: '100%',\n    padding: '0.75rem',\n    border: '1px solid #ddd',\n    borderRadius: '6px',\n    fontSize: '1rem'\n  };\n\n  const labelStyle = {\n    display: 'block',\n    marginBottom: '0.5rem',\n    fontWeight: '500',\n    color: '#2c3e50'\n  };\n\n  const sectionStyle = {\n    marginBottom: '2rem',\n    padding: '1.5rem',\n    background: '#f8f9fa',\n    borderRadius: '8px',\n    border: '1px solid #e9ecef'\n  };\n\n  return (\n    <div>\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <h2 style={{\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          margin: 0,\n          color: '#2c3e50'\n        }}>\n          Display Settings\n        </h2>\n\n        {hasChanges && (\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <button\n              onClick={handleReset}\n              style={{\n                background: '#6c757d',\n                color: 'white',\n                border: 'none',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <RotateCcw size={16} />\n              Reset\n            </button>\n            <button\n              onClick={handleSave}\n              style={{\n                background: '#28a745',\n                color: 'white',\n                border: 'none',\n                padding: '0.75rem 1.5rem',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <Save size={16} />\n              Save Changes\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* General Settings */}\n      <div style={sectionStyle}>\n        <h3 style={{ margin: '0 0 1.5rem 0', color: '#2c3e50' }}>General Settings</h3>\n        \n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1.5rem'\n        }}>\n          <div>\n            <label style={labelStyle}>\n              Display Enabled\n            </label>\n            <select\n              value={localSettings.isEnabled ? 'true' : 'false'}\n              onChange={(e) => handleSettingChange('isEnabled', e.target.value === 'true')}\n              style={inputStyle}\n            >\n              <option value=\"true\">Enabled</option>\n              <option value=\"false\">Disabled</option>\n            </select>\n          </div>\n\n          <div>\n            <label style={labelStyle}>\n              Auto-Play\n            </label>\n            <select\n              value={localSettings.autoPlay ? 'true' : 'false'}\n              onChange={(e) => handleSettingChange('autoPlay', e.target.value === 'true')}\n              style={inputStyle}\n            >\n              <option value=\"true\">Enabled</option>\n              <option value=\"false\">Disabled</option>\n            </select>\n          </div>\n\n          <div>\n            <label style={labelStyle}>\n              Slide Interval (seconds)\n            </label>\n            <input\n              type=\"number\"\n              min=\"5\"\n              max=\"300\"\n              value={localSettings.slideInterval / 1000}\n              onChange={(e) => handleSettingChange('slideInterval', parseInt(e.target.value) * 1000)}\n              style={inputStyle}\n            />\n          </div>\n\n          <div>\n            <label style={labelStyle}>\n              Transition Type\n            </label>\n            <select\n              value={localSettings.transitionType}\n              onChange={(e) => handleSettingChange('transitionType', e.target.value)}\n              style={inputStyle}\n            >\n              <option value=\"slide\">Slide</option>\n              <option value=\"fade\">Fade</option>\n              <option value=\"none\">None</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Content Settings */}\n      <div style={sectionStyle}>\n        <h3 style={{ margin: '0 0 1.5rem 0', color: '#2c3e50' }}>Content Settings</h3>\n        \n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1.5rem'\n        }}>\n          <div>\n            <label style={labelStyle}>\n              Show Announcements\n            </label>\n            <select\n              value={localSettings.showAnnouncements ? 'true' : 'false'}\n              onChange={(e) => handleSettingChange('showAnnouncements', e.target.value === 'true')}\n              style={inputStyle}\n            >\n              <option value=\"true\">Yes</option>\n              <option value=\"false\">No</option>\n            </select>\n          </div>\n\n          <div>\n            <label style={labelStyle}>\n              Show Calendar Events\n            </label>\n            <select\n              value={localSettings.showCalendarEvents ? 'true' : 'false'}\n              onChange={(e) => handleSettingChange('showCalendarEvents', e.target.value === 'true')}\n              style={inputStyle}\n            >\n              <option value=\"true\">Yes</option>\n              <option value=\"false\">No</option>\n            </select>\n          </div>\n\n          <div>\n            <label style={labelStyle}>\n              Max Announcements\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              max=\"50\"\n              value={localSettings.maxAnnouncements}\n              onChange={(e) => handleSettingChange('maxAnnouncements', parseInt(e.target.value))}\n              style={inputStyle}\n            />\n          </div>\n\n          <div>\n            <label style={labelStyle}>\n              Max Calendar Events\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              max=\"20\"\n              value={localSettings.maxEvents}\n              onChange={(e) => handleSettingChange('maxEvents', parseInt(e.target.value))}\n              style={inputStyle}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Preview Settings */}\n      <div style={sectionStyle}>\n        <h3 style={{ margin: '0 0 1.5rem 0', color: '#2c3e50' }}>Preview</h3>\n        \n        <div style={{\n          background: 'white',\n          border: '2px solid #dee2e6',\n          borderRadius: '8px',\n          padding: '2rem',\n          textAlign: 'center'\n        }}>\n          <div style={{\n            fontSize: '1.2rem',\n            fontWeight: '600',\n            marginBottom: '1rem',\n            color: '#2c3e50'\n          }}>\n            Current Settings Summary\n          </div>\n          \n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n            gap: '1rem',\n            textAlign: 'left'\n          }}>\n            <div>\n              <strong>Status:</strong> {localSettings.isEnabled ? 'Enabled' : 'Disabled'}\n            </div>\n            <div>\n              <strong>Auto-Play:</strong> {localSettings.autoPlay ? 'On' : 'Off'}\n            </div>\n            <div>\n              <strong>Slide Duration:</strong> {localSettings.slideInterval / 1000}s\n            </div>\n            <div>\n              <strong>Transition:</strong> {localSettings.transitionType}\n            </div>\n            <div>\n              <strong>Announcements:</strong> {localSettings.showAnnouncements ? `Yes (${localSettings.maxAnnouncements})` : 'No'}\n            </div>\n            <div>\n              <strong>Events:</strong> {localSettings.showCalendarEvents ? `Yes (${localSettings.maxEvents})` : 'No'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Help Section */}\n      <div style={{\n        background: '#e3f2fd',\n        border: '1px solid #bbdefb',\n        borderRadius: '8px',\n        padding: '1.5rem'\n      }}>\n        <h4 style={{ margin: '0 0 1rem 0', color: '#1976d2' }}>💡 Tips</h4>\n        <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#1565c0' }}>\n          <li>Changes are applied immediately to the TV display</li>\n          <li>Recommended slide interval: 10-20 seconds for optimal readability</li>\n          <li>Disable auto-play for manual control during presentations</li>\n          <li>Use fade transitions for a more professional look</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default TVDisplaySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,gBAAgB,QAAyC,oCAAoC;AACtG,SAASC,IAAI,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM/C,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAaO,QAAQ,CAAC;EACxE,MAAM,CAACI,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMa,mBAAmB,GAAGA,CAACC,GAAqB,EAAEC,KAAU,KAAK;IACjE,MAAMC,WAAW,GAAG;MAAE,GAAGP,aAAa;MAAE,CAACK,GAAG,GAAGC;IAAM,CAAC;IACtDL,gBAAgB,CAACM,WAAW,CAAC;IAC7BJ,aAAa,CAACK,IAAI,CAACC,SAAS,CAACF,WAAW,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACX,QAAQ,CAAC,CAAC;EACzE,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvBlB,gBAAgB,CAACmB,cAAc,CAACX,aAAa,CAAC;IAC9CG,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBX,gBAAgB,CAACH,QAAQ,CAAC;IAC1BK,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMU,UAAU,GAAG;IACjBC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBC,OAAO,EAAE,OAAO;IAChBC,YAAY,EAAE,QAAQ;IACtBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBH,YAAY,EAAE,MAAM;IACpBN,OAAO,EAAE,QAAQ;IACjBU,UAAU,EAAE,SAAS;IACrBR,YAAY,EAAE,KAAK;IACnBD,MAAM,EAAE;EACV,CAAC;EAED,oBACEpB,OAAA;IAAA8B,QAAA,gBACE9B,OAAA;MAAK+B,KAAK,EAAE;QACVP,OAAO,EAAE,MAAM;QACfQ,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBR,YAAY,EAAE;MAChB,CAAE;MAAAK,QAAA,gBACA9B,OAAA;QAAI+B,KAAK,EAAE;UACTT,QAAQ,EAAE,QAAQ;UAClBI,UAAU,EAAE,KAAK;UACjBQ,MAAM,EAAE,CAAC;UACTP,KAAK,EAAE;QACT,CAAE;QAAAG,QAAA,EAAC;MAEH;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJhC,UAAU,iBACTN,OAAA;QAAK+B,KAAK,EAAE;UAAEP,OAAO,EAAE,MAAM;UAAEe,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC3C9B,OAAA;UACEwC,OAAO,EAAExB,WAAY;UACrBe,KAAK,EAAE;YACLF,UAAU,EAAE,SAAS;YACrBF,KAAK,EAAE,OAAO;YACdP,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,gBAAgB;YACzBE,YAAY,EAAE,KAAK;YACnBoB,MAAM,EAAE,SAAS;YACjBjB,OAAO,EAAE,MAAM;YACfS,UAAU,EAAE,QAAQ;YACpBM,GAAG,EAAE;UACP,CAAE;UAAAT,QAAA,gBAEF9B,OAAA,CAACF,SAAS;YAAC4C,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtC,OAAA;UACEwC,OAAO,EAAE1B,UAAW;UACpBiB,KAAK,EAAE;YACLF,UAAU,EAAE,SAAS;YACrBF,KAAK,EAAE,OAAO;YACdP,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,gBAAgB;YACzBE,YAAY,EAAE,KAAK;YACnBoB,MAAM,EAAE,SAAS;YACjBjB,OAAO,EAAE,MAAM;YACfS,UAAU,EAAE,QAAQ;YACpBM,GAAG,EAAE;UACP,CAAE;UAAAT,QAAA,gBAEF9B,OAAA,CAACH,IAAI;YAAC6C,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtC,OAAA;MAAK+B,KAAK,EAAEH,YAAa;MAAAE,QAAA,gBACvB9B,OAAA;QAAI+B,KAAK,EAAE;UAAEG,MAAM,EAAE,cAAc;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAG,QAAA,EAAC;MAAgB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE9EtC,OAAA;QAAK+B,KAAK,EAAE;UACVP,OAAO,EAAE,MAAM;UACfmB,mBAAmB,EAAE,sCAAsC;UAC3DJ,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,gBACA9B,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEU,KAAK,EAAEN,aAAa,CAACwC,SAAS,GAAG,MAAM,GAAG,OAAQ;YAClDC,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,WAAW,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,KAAK,MAAM,CAAE;YAC7EqB,KAAK,EAAEd,UAAW;YAAAa,QAAA,gBAElB9B,OAAA;cAAQU,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCtC,OAAA;cAAQU,KAAK,EAAC,OAAO;cAAAoB,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEU,KAAK,EAAEN,aAAa,CAAC4C,QAAQ,GAAG,MAAM,GAAG,OAAQ;YACjDH,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,UAAU,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,KAAK,MAAM,CAAE;YAC5EqB,KAAK,EAAEd,UAAW;YAAAa,QAAA,gBAElB9B,OAAA;cAAQU,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCtC,OAAA;cAAQU,KAAK,EAAC,OAAO;cAAAoB,QAAA,EAAC;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEiD,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,KAAK;YACTzC,KAAK,EAAEN,aAAa,CAACgD,aAAa,GAAG,IAAK;YAC1CP,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,eAAe,EAAE6C,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAC,GAAG,IAAI,CAAE;YACvFqB,KAAK,EAAEd;UAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEU,KAAK,EAAEN,aAAa,CAACkD,cAAe;YACpCT,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,gBAAgB,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAE;YACvEqB,KAAK,EAAEd,UAAW;YAAAa,QAAA,gBAElB9B,OAAA;cAAQU,KAAK,EAAC,OAAO;cAAAoB,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCtC,OAAA;cAAQU,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCtC,OAAA;cAAQU,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAK+B,KAAK,EAAEH,YAAa;MAAAE,QAAA,gBACvB9B,OAAA;QAAI+B,KAAK,EAAE;UAAEG,MAAM,EAAE,cAAc;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAG,QAAA,EAAC;MAAgB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE9EtC,OAAA;QAAK+B,KAAK,EAAE;UACVP,OAAO,EAAE,MAAM;UACfmB,mBAAmB,EAAE,sCAAsC;UAC3DJ,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,gBACA9B,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEU,KAAK,EAAEN,aAAa,CAACmD,iBAAiB,GAAG,MAAM,GAAG,OAAQ;YAC1DV,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,mBAAmB,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,KAAK,MAAM,CAAE;YACrFqB,KAAK,EAAEd,UAAW;YAAAa,QAAA,gBAElB9B,OAAA;cAAQU,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCtC,OAAA;cAAQU,KAAK,EAAC,OAAO;cAAAoB,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEU,KAAK,EAAEN,aAAa,CAACoD,kBAAkB,GAAG,MAAM,GAAG,OAAQ;YAC3DX,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,oBAAoB,EAAEsC,CAAC,CAACC,MAAM,CAACrC,KAAK,KAAK,MAAM,CAAE;YACtFqB,KAAK,EAAEd,UAAW;YAAAa,QAAA,gBAElB9B,OAAA;cAAQU,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCtC,OAAA;cAAQU,KAAK,EAAC,OAAO;cAAAoB,QAAA,EAAC;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEiD,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRzC,KAAK,EAAEN,aAAa,CAACqD,gBAAiB;YACtCZ,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,kBAAkB,EAAE6C,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAC,CAAE;YACnFqB,KAAK,EAAEd;UAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENtC,OAAA;UAAA8B,QAAA,gBACE9B,OAAA;YAAO+B,KAAK,EAAER,UAAW;YAAAO,QAAA,EAAC;UAE1B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtC,OAAA;YACEiD,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACRzC,KAAK,EAAEN,aAAa,CAACsD,SAAU;YAC/Bb,QAAQ,EAAGC,CAAC,IAAKtC,mBAAmB,CAAC,WAAW,EAAE6C,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACrC,KAAK,CAAC,CAAE;YAC5EqB,KAAK,EAAEd;UAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAK+B,KAAK,EAAEH,YAAa;MAAAE,QAAA,gBACvB9B,OAAA;QAAI+B,KAAK,EAAE;UAAEG,MAAM,EAAE,cAAc;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAG,QAAA,EAAC;MAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAErEtC,OAAA;QAAK+B,KAAK,EAAE;UACVF,UAAU,EAAE,OAAO;UACnBT,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBF,OAAO,EAAE,MAAM;UACfwC,SAAS,EAAE;QACb,CAAE;QAAA7B,QAAA,gBACA9B,OAAA;UAAK+B,KAAK,EAAE;YACVT,QAAQ,EAAE,QAAQ;YAClBI,UAAU,EAAE,KAAK;YACjBD,YAAY,EAAE,MAAM;YACpBE,KAAK,EAAE;UACT,CAAE;UAAAG,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAENtC,OAAA;UAAK+B,KAAK,EAAE;YACVP,OAAO,EAAE,MAAM;YACfmB,mBAAmB,EAAE,sCAAsC;YAC3DJ,GAAG,EAAE,MAAM;YACXoB,SAAS,EAAE;UACb,CAAE;UAAA7B,QAAA,gBACA9B,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAA8B,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClC,aAAa,CAACwC,SAAS,GAAG,SAAS,GAAG,UAAU;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNtC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAA8B,QAAA,EAAQ;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClC,aAAa,CAAC4C,QAAQ,GAAG,IAAI,GAAG,KAAK;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNtC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAA8B,QAAA,EAAQ;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClC,aAAa,CAACgD,aAAa,GAAG,IAAI,EAAC,GACvE;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAA8B,QAAA,EAAQ;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClC,aAAa,CAACkD,cAAc;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNtC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAA8B,QAAA,EAAQ;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClC,aAAa,CAACmD,iBAAiB,GAAG,QAAQnD,aAAa,CAACqD,gBAAgB,GAAG,GAAG,IAAI;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChH,CAAC,eACNtC,OAAA;YAAA8B,QAAA,gBACE9B,OAAA;cAAA8B,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAClC,aAAa,CAACoD,kBAAkB,GAAG,QAAQpD,aAAa,CAACsD,SAAS,GAAG,GAAG,IAAI;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAK+B,KAAK,EAAE;QACVF,UAAU,EAAE,SAAS;QACrBT,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBF,OAAO,EAAE;MACX,CAAE;MAAAW,QAAA,gBACA9B,OAAA;QAAI+B,KAAK,EAAE;UAAEG,MAAM,EAAE,YAAY;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAG,QAAA,EAAC;MAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEtC,OAAA;QAAI+B,KAAK,EAAE;UAAEG,MAAM,EAAE,CAAC;UAAE0B,WAAW,EAAE,QAAQ;UAAEjC,KAAK,EAAE;QAAU,CAAE;QAAAG,QAAA,gBAChE9B,OAAA;UAAA8B,QAAA,EAAI;QAAiD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DtC,OAAA;UAAA8B,QAAA,EAAI;QAAiE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EtC,OAAA;UAAA8B,QAAA,EAAI;QAAyD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEtC,OAAA;UAAA8B,QAAA,EAAI;QAAiD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA5SIF,iBAAmD;AAAA4D,EAAA,GAAnD5D,iBAAmD;AA8SzD,eAAeA,iBAAiB;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}