import React from 'react';
import { tvControlService, TVDisplaySettings, TVDisplayStatus } from '../../../services/tvControlService';
import { Play, Pause, SkipForward, SkipBack, RefreshCw, Square } from 'lucide-react';

interface TVPlaybackControlsProps {
  settings: TVDisplaySettings;
  status: TVDisplayStatus;
}

const TVPlaybackControls: React.FC<TVPlaybackControlsProps> = ({ settings, status }) => {
  const handlePlay = () => {
    tvControlService.play();
  };

  const handlePause = () => {
    tvControlService.pause();
  };

  const handleNext = () => {
    tvControlService.next();
  };

  const handlePrevious = () => {
    tvControlService.previous();
  };

  const handleRefresh = () => {
    tvControlService.refresh();
  };

  const handleToggleAutoPlay = () => {
    tvControlService.updateSettings({
      autoPlay: !settings.autoPlay
    });
  };

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const controlButtonStyle = {
    background: '#3498db',
    color: 'white',
    border: 'none',
    borderRadius: '50%',
    width: '60px',
    height: '60px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 12px rgba(52, 152, 219, 0.3)'
  };

  const secondaryButtonStyle = {
    background: '#95a5a6',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    padding: '0.75rem 1.5rem',
    cursor: 'pointer',
    fontSize: '0.9rem',
    fontWeight: '500',
    transition: 'all 0.2s ease'
  };

  return (
    <div>
      <h2 style={{
        fontSize: '1.8rem',
        fontWeight: '600',
        margin: '0 0 2rem 0',
        color: '#2c3e50'
      }}>
        Playback Controls
      </h2>

      {/* Current Status Display */}
      <div style={{
        background: '#f8f9fa',
        borderRadius: '12px',
        padding: '2rem',
        marginBottom: '2rem',
        border: '1px solid #e9ecef'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '2rem'
        }}>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>
              Current Status
            </h4>
            <div style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: status.isPlaying ? '#28a745' : '#dc3545'
            }}>
              {status.isPlaying ? '▶️ Playing' : '⏸️ Paused'}
            </div>
          </div>

          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>
              Current Slide
            </h4>
            <div style={{ fontSize: '1.5rem', fontWeight: '600', color: '#2c3e50' }}>
              {status.currentSlide + 1} of {status.totalSlides}
            </div>
          </div>

          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>
              Slide Duration
            </h4>
            <div style={{ fontSize: '1.5rem', fontWeight: '600', color: '#2c3e50' }}>
              {formatTime(settings.slideInterval)}
            </div>
          </div>

          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>
              Auto-Play
            </h4>
            <div style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: settings.autoPlay ? '#28a745' : '#dc3545'
            }}>
              {settings.autoPlay ? '✅ Enabled' : '❌ Disabled'}
            </div>
          </div>
        </div>
      </div>

      {/* Main Control Buttons */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '1.5rem',
        marginBottom: '3rem'
      }}>
        {/* Previous Button */}
        <button
          onClick={handlePrevious}
          style={controlButtonStyle}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#2980b9';
            e.currentTarget.style.transform = 'scale(1.05)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = '#3498db';
            e.currentTarget.style.transform = 'scale(1)';
          }}
          title="Previous Slide"
        >
          <SkipBack size={24} />
        </button>

        {/* Play/Pause Button */}
        <button
          onClick={status.isPlaying ? handlePause : handlePlay}
          style={{
            ...controlButtonStyle,
            width: '80px',
            height: '80px',
            background: status.isPlaying ? '#e74c3c' : '#27ae60'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = status.isPlaying ? '#c0392b' : '#229954';
            e.currentTarget.style.transform = 'scale(1.05)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = status.isPlaying ? '#e74c3c' : '#27ae60';
            e.currentTarget.style.transform = 'scale(1)';
          }}
          title={status.isPlaying ? 'Pause' : 'Play'}
        >
          {status.isPlaying ? <Pause size={32} /> : <Play size={32} />}
        </button>

        {/* Next Button */}
        <button
          onClick={handleNext}
          style={controlButtonStyle}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#2980b9';
            e.currentTarget.style.transform = 'scale(1.05)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = '#3498db';
            e.currentTarget.style.transform = 'scale(1)';
          }}
          title="Next Slide"
        >
          <SkipForward size={24} />
        </button>
      </div>

      {/* Secondary Controls */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '1rem',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={handleRefresh}
          style={secondaryButtonStyle}
          onMouseEnter={(e) => e.currentTarget.style.background = '#7f8c8d'}
          onMouseLeave={(e) => e.currentTarget.style.background = '#95a5a6'}
        >
          <RefreshCw size={16} style={{ marginRight: '0.5rem' }} />
          Refresh Content
        </button>

        <button
          onClick={handleToggleAutoPlay}
          style={{
            ...secondaryButtonStyle,
            background: settings.autoPlay ? '#e74c3c' : '#27ae60'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = settings.autoPlay ? '#c0392b' : '#229954';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = settings.autoPlay ? '#e74c3c' : '#27ae60';
          }}
        >
          {settings.autoPlay ? (
            <>
              <Square size={16} style={{ marginRight: '0.5rem' }} />
              Disable Auto-Play
            </>
          ) : (
            <>
              <Play size={16} style={{ marginRight: '0.5rem' }} />
              Enable Auto-Play
            </>
          )}
        </button>
      </div>

      {/* Quick Actions */}
      <div style={{
        marginTop: '3rem',
        padding: '2rem',
        background: '#f8f9fa',
        borderRadius: '12px',
        border: '1px solid #e9ecef'
      }}>
        <h3 style={{
          fontSize: '1.2rem',
          fontWeight: '600',
          margin: '0 0 1rem 0',
          color: '#2c3e50'
        }}>
          Quick Actions
        </h3>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{
            padding: '1rem',
            background: 'white',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <strong>Keyboard Shortcuts (on TV display):</strong>
            <ul style={{ margin: '0.5rem 0 0 0', paddingLeft: '1.5rem' }}>
              <li>Space: Play/Pause</li>
              <li>← →: Previous/Next slide</li>
              <li>R: Refresh content</li>
            </ul>
          </div>
          <div style={{
            padding: '1rem',
            background: 'white',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <strong>Remote Control:</strong>
            <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.9rem', color: '#6c757d' }}>
              Use this panel to control the TV display remotely from any device connected to the same network.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TVPlaybackControls;
