{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVCalendarEvent.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVCalendarEvent = ({\n  event\n}) => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color - keep original colors\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Keep original red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // TV-friendly description truncation\n  const truncateDescription = (description, maxLength = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n  const isLongDescription = event.description && event.description.length > 200;\n\n  // Get event images\n  const getEventImages = () => {\n    const images = [];\n\n    // Check if event has images (from API response)\n    if (event.images && Array.isArray(event.images)) {\n      event.images.forEach((img, index) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getEventImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex(prev => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: isUpcoming() ? 'linear-gradient(135deg, #fefce8 0%, #ffffff 100%)' : 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: isUpcoming() ? '0 10px 30px rgba(245, 158, 11, 0.2)' : '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming() ? '4px solid #f59e0b' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: 'flex',\n      // Side-by-side layout\n      gap: '2rem',\n      alignItems: 'stretch'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), isUpcoming() && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '2rem',\n        right: '2rem',\n        background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '20px',\n        fontSize: '1.6rem',\n        fontWeight: 'bold',\n        textTransform: 'uppercase',\n        letterSpacing: '1px',\n        boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n        zIndex: 2\n      },\n      children: \"UPCOMING\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        marginBottom: '2rem',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          padding: '1rem',\n          borderRadius: '20px',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)'\n        },\n        children: getEventIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: [event.is_holiday && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.8rem',\n            fontWeight: '700',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 20px rgba(243, 156, 18, 0.3)'\n          },\n          children: \"HOLIDAY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), event.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.8rem',\n            fontWeight: '700',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 20px rgba(231, 76, 60, 0.3)'\n          },\n          children: \"IMPORTANT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: isLongDescription ? '2.2rem' : '2.8rem',\n        fontWeight: '700',\n        margin: '0 0 1rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.3',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n        flexShrink: 0,\n        wordWrap: 'break-word',\n        hyphens: 'auto'\n      },\n      children: event.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        margin: '1rem 0',\n        padding: '1rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '12px',\n        border: '1px solid rgba(231, 76, 60, 0.15)',\n        position: 'relative',\n        zIndex: 1,\n        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.08)',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '2.5rem',\n          background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n          color: 'white',\n          padding: '0.8rem',\n          borderRadius: '12px',\n          boxShadow: `0 4px 10px ${getCategoryColor()}30`\n        },\n        children: \"\\uD83D\\uDCC5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '0.5rem',\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: isLongDescription ? '1.6rem' : '2rem',\n            fontWeight: '700',\n            color: '#2c3e50'\n          },\n          children: formatDateRange()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: isLongDescription ? '1.4rem' : '1.6rem',\n            fontWeight: '600',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n            background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n            padding: '0.4rem 0.8rem',\n            borderRadius: '8px',\n            display: 'inline-block'\n          },\n          children: getDaysUntilEvent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1rem',\n        maxHeight: '300px'\n      },\n      children: images.slice(0, 2).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '12px',\n          overflow: 'hidden',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 9\n    }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-description\",\n      children: truncateDescription(event.description)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: getCategoryColor(),\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            fontWeight: '600',\n            fontSize: '1.6rem'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), event.subcategory_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: event.subcategory_color || '#95a5a6',\n            color: 'white',\n            padding: '0.6rem 1.2rem',\n            borderRadius: '20px',\n            fontSize: '1.4rem'\n          },\n          children: event.subcategory_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), event.is_recurring && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.6rem',\n            color: '#8e44ad'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [event.recurrence_pattern === 'yearly' && 'Yearly', event.recurrence_pattern === 'monthly' && 'Monthly', event.recurrence_pattern === 'weekly' && 'Weekly']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Organized by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: event.created_by_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), (event.reaction_count || event.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [event.reaction_count && event.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 13\n      }, this), event.comment_count && event.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(TVCalendarEvent, \"iwdYV/csWqs0gMEM0R8yiwCHnVs=\");\n_c = TVCalendarEvent;\nexport default TVCalendarEvent;\nvar _c;\n$RefreshReg$(_c, \"TVCalendarEvent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getImageUrl", "jsxDEV", "_jsxDEV", "TVCalendarEvent", "event", "_s", "currentImageIndex", "setCurrentImageIndex", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatDateRange", "startDate", "event_date", "end_date", "endDate", "startFormatted", "endFormatted", "getDaysUntilEvent", "today", "eventDate", "diffTime", "getTime", "diffDays", "Math", "ceil", "abs", "getCategoryColor", "category_color", "getEventIcon", "is_holiday", "is_recurring", "is_alert", "isUpcoming", "truncateDescription", "description", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "isLongDescription", "getEventImages", "images", "Array", "isArray", "for<PERSON>ach", "img", "index", "file_path", "imageUrl", "push", "url", "alt", "title", "interval", "setInterval", "prev", "clearInterval", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "maxHeight", "display", "gap", "alignItems", "children", "top", "right", "width", "height", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "fontWeight", "textTransform", "letterSpacing", "marginBottom", "flexWrap", "lineHeight", "textShadow", "flexShrink", "wordWrap", "hyphens", "flexDirection", "flex", "gridTemplateColumns", "slice", "map", "image", "src", "objectFit", "onError", "e", "currentTarget", "className", "category_name", "subcategory_name", "subcategory_color", "recurrence_pattern", "created_by_name", "opacity", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVCalendarEvent.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVCalendarEventProps {\n  event: CalendarEvent;\n}\n\nconst TVCalendarEvent: React.FC<TVCalendarEventProps> = ({ event }) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    \n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    \n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color - keep original colors\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Keep original red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // TV-friendly description truncation\n  const truncateDescription = (description: string, maxLength: number = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n\n  const isLongDescription = event.description && event.description.length > 200;\n\n  // Get event images\n  const getEventImages = () => {\n    const images: { url: string; alt: string }[] = [];\n\n    // Check if event has images (from API response)\n    if ((event as any).images && Array.isArray((event as any).images)) {\n      (event as any).images.forEach((img: any, index: number) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getEventImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex((prev) => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  return (\n    <div style={{\n      background: isUpcoming()\n        ? 'linear-gradient(135deg, #fefce8 0%, #ffffff 100%)'\n        : 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: isUpcoming()\n        ? '0 10px 30px rgba(245, 158, 11, 0.2)'\n        : '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming()\n        ? '4px solid #f59e0b'\n        : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: 'flex', // Side-by-side layout\n      gap: '2rem',\n      alignItems: 'stretch'\n    }}>\n      {/* Background decoration */}\n      <div style={{\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }} />\n\n      {/* Upcoming event indicator */}\n      {isUpcoming() && (\n        <div style={{\n          position: 'absolute',\n          top: '2rem',\n          right: '2rem',\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '20px',\n          fontSize: '1.6rem',\n          fontWeight: 'bold',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n          zIndex: 2\n        }}>\n          UPCOMING\n        </div>\n      )}\n      {/* Event type indicator */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        marginBottom: '2rem',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        <div style={{\n          fontSize: '4rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          padding: '1rem',\n          borderRadius: '20px',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)'\n        }}>\n          {getEventIcon()}\n        </div>\n\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {event.is_holiday && (\n            <span style={{\n              background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '20px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 8px 20px rgba(243, 156, 18, 0.3)'\n            }}>\n              HOLIDAY\n            </span>\n          )}\n          {event.is_alert && (\n            <span style={{\n              background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '20px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 8px 20px rgba(231, 76, 60, 0.3)'\n            }}>\n              IMPORTANT\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Event title - responsive sizing */}\n      <h2 style={{\n        fontSize: isLongDescription ? '2.2rem' : '2.8rem',\n        fontWeight: '700',\n        margin: '0 0 1rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.3',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n        flexShrink: 0,\n        wordWrap: 'break-word',\n        hyphens: 'auto'\n      }}>\n        {event.title}\n      </h2>\n\n      {/* Event date with countdown - compact */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        margin: '1rem 0',\n        padding: '1rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '12px',\n        border: '1px solid rgba(231, 76, 60, 0.15)',\n        position: 'relative',\n        zIndex: 1,\n        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.08)',\n        flexShrink: 0\n      }}>\n        <div style={{\n          fontSize: '2.5rem',\n          background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n          color: 'white',\n          padding: '0.8rem',\n          borderRadius: '12px',\n          boxShadow: `0 4px 10px ${getCategoryColor()}30`\n        }}>\n          📅\n        </div>\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', flex: 1 }}>\n          <span style={{\n            fontSize: isLongDescription ? '1.6rem' : '2rem',\n            fontWeight: '700',\n            color: '#2c3e50'\n          }}>\n            {formatDateRange()}\n          </span>\n          <span style={{\n            fontSize: isLongDescription ? '1.4rem' : '1.6rem',\n            fontWeight: '600',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n            background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n            padding: '0.4rem 0.8rem',\n            borderRadius: '8px',\n            display: 'inline-block'\n          }}>\n            {getDaysUntilEvent()}\n          </span>\n        </div>\n      </div>\n\n      {/* Event images (placeholder for future implementation) */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem',\n          maxHeight: '300px'\n        }}>\n          {images.slice(0, 2).map((image: any, index: number) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '12px',\n                overflow: 'hidden',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Event description */}\n      {event.description && (\n        <div className=\"tv-event-description\">\n          {truncateDescription(event.description)}\n        </div>\n      )}\n\n      {/* Event metadata */}\n      <div className=\"tv-event-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {event.category_name && (\n            <span \n              style={{\n                background: getCategoryColor(),\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '25px',\n                fontWeight: '600',\n                fontSize: '1.6rem'\n              }}\n            >\n              {event.category_name}\n            </span>\n          )}\n\n          {/* Subcategory */}\n          {event.subcategory_name && (\n            <span style={{\n              background: event.subcategory_color || '#95a5a6',\n              color: 'white',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              fontSize: '1.4rem'\n            }}>\n              {event.subcategory_name}\n            </span>\n          )}\n\n          {/* Recurring indicator */}\n          {event.is_recurring && (\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              gap: '0.5rem',\n              fontSize: '1.6rem',\n              color: '#8e44ad'\n            }}>\n              <span>🔄</span>\n              <span>\n                {event.recurrence_pattern === 'yearly' && 'Yearly'}\n                {event.recurrence_pattern === 'monthly' && 'Monthly'}\n                {event.recurrence_pattern === 'weekly' && 'Weekly'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Created by information */}\n        {event.created_by_name && (\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Organized by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {event.created_by_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(event.reaction_count || event.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {event.reaction_count && event.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{event.reaction_count} reactions</span>\n            </div>\n          )}\n          {event.comment_count && event.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{event.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVCalendarEvent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EAC7D;EACA,MAAMU,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAACP,KAAK,CAACe,UAAU,CAAC;IAE5C,IAAIf,KAAK,CAACgB,QAAQ,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAIV,IAAI,CAACP,KAAK,CAACgB,QAAQ,CAAC;MACxC,MAAME,cAAc,GAAGJ,SAAS,CAACN,kBAAkB,CAAC,OAAO,EAAE;QAC3DG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,MAAMO,YAAY,GAAGF,OAAO,CAACT,kBAAkB,CAAC,OAAO,EAAE;QACvDG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdF,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAO,GAAGQ,cAAc,MAAMC,YAAY,EAAE;IAC9C;IAEA,OAAOf,UAAU,CAACJ,KAAK,CAACe,UAAU,CAAC;EACrC,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACP,KAAK,CAACe,UAAU,CAAC;IAC5C,MAAMQ,QAAQ,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,UAAU;IACrC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAMA,QAAQ,OAAO;IAC9C,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW;IACvC,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,WAAW;EACzC,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI7B,KAAK,CAAC8B,cAAc,EAAE;MACxB,OAAO9B,KAAK,CAAC8B,cAAc;IAC7B;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI/B,KAAK,CAACgC,UAAU,EAAE,OAAO,IAAI;IACjC,IAAIhC,KAAK,CAACiC,YAAY,EAAE,OAAO,IAAI;IACnC,IAAIjC,KAAK,CAACkC,QAAQ,EAAE,OAAO,IAAI;IAC/B,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMd,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACP,KAAK,CAACe,UAAU,CAAC;IAC5C,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3F,OAAOC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAGA,CAACC,WAAmB,EAAEC,SAAiB,GAAG,GAAG,KAAK;IAC5E,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,WAAW;IACvE,OAAOA,WAAW,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EAC3D,CAAC;EAED,MAAMC,iBAAiB,GAAG1C,KAAK,CAACqC,WAAW,IAAIrC,KAAK,CAACqC,WAAW,CAACE,MAAM,GAAG,GAAG;;EAE7E;EACA,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAsC,GAAG,EAAE;;IAEjD;IACA,IAAK5C,KAAK,CAAS4C,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAE9C,KAAK,CAAS4C,MAAM,CAAC,EAAE;MAChE5C,KAAK,CAAS4C,MAAM,CAACG,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAK;QACzD,IAAID,GAAG,CAACE,SAAS,EAAE;UACjB,MAAMC,QAAQ,GAAGvD,WAAW,CAACoD,GAAG,CAACE,SAAS,CAAC;UAC3C,IAAIC,QAAQ,EAAE;YACZP,MAAM,CAACQ,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGtD,KAAK,CAACuD,KAAK,YAAYN,KAAK,GAAG,CAAC;YAC1C,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOL,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,cAAc,CAAC,CAAC;;EAE/B;EACAhD,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMiB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCtD,oBAAoB,CAAEuD,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAId,MAAM,CAACL,MAAM,CAAC;MAC5D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMoB,aAAa,CAACH,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACZ,MAAM,CAACL,MAAM,CAAC,CAAC;EAEnB,oBACEzC,OAAA;IAAK8D,KAAK,EAAE;MACVC,UAAU,EAAE1B,UAAU,CAAC,CAAC,GACpB,mDAAmD,GACnD,mDAAmD;MACvD2B,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE9B,UAAU,CAAC,CAAC,GACnB,qCAAqC,GACrC,gCAAgC;MACpC+B,MAAM,EAAE/B,UAAU,CAAC,CAAC,GAChB,mBAAmB,GACnB,aAAaN,gBAAgB,CAAC,CAAC,EAAE;MACrCsC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,MAAM;MAAE;MACjBC,GAAG,EAAE,MAAM;MACXC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBAEA3E,OAAA;MAAK8D,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBO,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACfhB,UAAU,EAAE,0BAA0BhC,gBAAgB,CAAC,CAAC,OAAOA,gBAAgB,CAAC,CAAC,KAAK;QACtFiC,YAAY,EAAE,KAAK;QACnBgB,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGJ/C,UAAU,CAAC,CAAC,iBACXrC,OAAA;MAAK8D,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBO,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbd,UAAU,EAAE,2CAA2C;QACvDsB,KAAK,EAAE,OAAO;QACdpB,OAAO,EAAE,WAAW;QACpBD,YAAY,EAAE,MAAM;QACpBsB,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,KAAK;QACpBtB,SAAS,EAAE,mCAAmC;QAC9Ca,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,EAAC;IAEH;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDpF,OAAA;MAAK8D,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE,MAAM;QACXiB,YAAY,EAAE,MAAM;QACpBrB,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACA3E,OAAA;QAAK8D,KAAK,EAAE;UACVwB,QAAQ,EAAE,MAAM;UAChBvB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAE,MAAM;UACfD,YAAY,EAAE,MAAM;UACpBG,SAAS,EAAE;QACb,CAAE;QAAAQ,QAAA,EACC1C,YAAY,CAAC;MAAC;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAENpF,OAAA;QAAK8D,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEkB,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,GAC5DzE,KAAK,CAACgC,UAAU,iBACflC,OAAA;UAAM8D,KAAK,EAAE;YACXC,UAAU,EAAE,2CAA2C;YACvDsB,KAAK,EAAE,OAAO;YACdpB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBsB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBtB,SAAS,EAAE;UACb,CAAE;UAAAQ,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAlF,KAAK,CAACkC,QAAQ,iBACbpC,OAAA;UAAM8D,KAAK,EAAE;YACXC,UAAU,EAAE,2CAA2C;YACvDsB,KAAK,EAAE,OAAO;YACdpB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBsB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBtB,SAAS,EAAE;UACb,CAAE;UAAAQ,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA;MAAI8D,KAAK,EAAE;QACTwB,QAAQ,EAAE1C,iBAAiB,GAAG,QAAQ,GAAG,QAAQ;QACjD2C,UAAU,EAAE,KAAK;QACjBrB,MAAM,EAAE,YAAY;QACpBmB,KAAK,EAAE,SAAS;QAChBO,UAAU,EAAE,KAAK;QACjBvB,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE,CAAC;QACTa,UAAU,EAAE,8BAA8B;QAC1CC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE,YAAY;QACtBC,OAAO,EAAE;MACX,CAAE;MAAArB,QAAA,EACCzE,KAAK,CAACuD;IAAK;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGLpF,OAAA;MAAK8D,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBD,GAAG,EAAE,MAAM;QACXP,MAAM,EAAE,QAAQ;QAChBD,OAAO,EAAE,MAAM;QACfF,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE,mCAAmC;QAC3CC,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE,CAAC;QACTb,SAAS,EAAE,gCAAgC;QAC3C2B,UAAU,EAAE;MACd,CAAE;MAAAnB,QAAA,gBACA3E,OAAA;QAAK8D,KAAK,EAAE;UACVwB,QAAQ,EAAE,QAAQ;UAClBvB,UAAU,EAAE,2BAA2BhC,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;UACrFsD,KAAK,EAAE,OAAO;UACdpB,OAAO,EAAE,QAAQ;UACjBD,YAAY,EAAE,MAAM;UACpBG,SAAS,EAAE,cAAcpC,gBAAgB,CAAC,CAAC;QAC7C,CAAE;QAAA4C,QAAA,EAAC;MAEH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNpF,OAAA;QAAK8D,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEyB,aAAa,EAAE,QAAQ;UAAExB,GAAG,EAAE,QAAQ;UAAEyB,IAAI,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC/E3E,OAAA;UAAM8D,KAAK,EAAE;YACXwB,QAAQ,EAAE1C,iBAAiB,GAAG,QAAQ,GAAG,MAAM;YAC/C2C,UAAU,EAAE,KAAK;YACjBF,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,EACC5D,eAAe,CAAC;QAAC;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACPpF,OAAA;UAAM8D,KAAK,EAAE;YACXwB,QAAQ,EAAE1C,iBAAiB,GAAG,QAAQ,GAAG,QAAQ;YACjD2C,UAAU,EAAE,KAAK;YACjBF,KAAK,EAAEhD,UAAU,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YAC3C0B,UAAU,EAAE1B,UAAU,CAAC,CAAC,GAAG,wBAAwB,GAAG,0BAA0B;YAChF4B,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,KAAK;YACnBQ,OAAO,EAAE;UACX,CAAE;UAAAG,QAAA,EACCrD,iBAAiB,CAAC;QAAC;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLtC,MAAM,CAACL,MAAM,GAAG,CAAC,iBAChBzC,OAAA;MAAK8D,KAAK,EAAE;QACV4B,YAAY,EAAE,MAAM;QACpBlB,OAAO,EAAE,MAAM;QACf2B,mBAAmB,EAAErD,MAAM,CAACL,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzFgC,GAAG,EAAE,MAAM;QACXF,SAAS,EAAE;MACb,CAAE;MAAAI,QAAA,EACC7B,MAAM,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAU,EAAEnD,KAAa,kBAChDnD,OAAA;QAEE8D,KAAK,EAAE;UACLE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE;QACb,CAAE;QAAAQ,QAAA,eAEF3E,OAAA;UACEuG,GAAG,EAAED,KAAK,CAAC/C,GAAI;UACfC,GAAG,EAAE8C,KAAK,CAAC9C,GAAI;UACfM,KAAK,EAAE;YACLgB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACfyB,SAAS,EAAE,OAAO;YAClBhC,OAAO,EAAE;UACX,CAAE;UACFiC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACU,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnBGjC,KAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAlF,KAAK,CAACqC,WAAW,iBAChBvC,OAAA;MAAK4G,SAAS,EAAC,sBAAsB;MAAAjC,QAAA,EAClCrC,mBAAmB,CAACpC,KAAK,CAACqC,WAAW;IAAC;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN,eAGDpF,OAAA;MAAK4G,SAAS,EAAC,eAAe;MAAAjC,QAAA,gBAC5B3E,OAAA;QAAK8D,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAO,CAAE;QAAAE,QAAA,GAEhEzE,KAAK,CAAC2G,aAAa,iBAClB7G,OAAA;UACE8D,KAAK,EAAE;YACLC,UAAU,EAAEhC,gBAAgB,CAAC,CAAC;YAC9BsD,KAAK,EAAE,OAAO;YACdpB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBuB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EAEDzE,KAAK,CAAC2G;QAAa;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EAGAlF,KAAK,CAAC4G,gBAAgB,iBACrB9G,OAAA;UAAM8D,KAAK,EAAE;YACXC,UAAU,EAAE7D,KAAK,CAAC6G,iBAAiB,IAAI,SAAS;YAChD1B,KAAK,EAAE,OAAO;YACdpB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBsB,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EACCzE,KAAK,CAAC4G;QAAgB;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP,EAGAlF,KAAK,CAACiC,YAAY,iBACjBnC,OAAA;UAAK8D,KAAK,EAAE;YACVU,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACba,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,gBACA3E,OAAA;YAAA2E,QAAA,EAAM;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfpF,OAAA;YAAA2E,QAAA,GACGzE,KAAK,CAAC8G,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EACjD9G,KAAK,CAAC8G,kBAAkB,KAAK,SAAS,IAAI,SAAS,EACnD9G,KAAK,CAAC8G,kBAAkB,KAAK,QAAQ,IAAI,QAAQ;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLlF,KAAK,CAAC+G,eAAe,iBACpBjH,OAAA;QAAK8D,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,MAAM;UACXa,QAAQ,EAAE;QACZ,CAAE;QAAAX,QAAA,gBACA3E,OAAA;UAAM8D,KAAK,EAAE;YAAEoD,OAAO,EAAE;UAAI,CAAE;UAAAvC,QAAA,EAAC;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDpF,OAAA;UAAM8D,KAAK,EAAE;YAAEyB,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAChCzE,KAAK,CAAC+G;QAAe;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAClF,KAAK,CAACiH,cAAc,IAAIjH,KAAK,CAACkH,aAAa,kBAC3CpH,OAAA;MAAK8D,KAAK,EAAE;QACVuD,SAAS,EAAE,MAAM;QACjBpD,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBQ,OAAO,EAAE,MAAM;QACfC,GAAG,EAAE,MAAM;QACXa,QAAQ,EAAE;MACZ,CAAE;MAAAX,QAAA,GACCzE,KAAK,CAACiH,cAAc,IAAIjH,KAAK,CAACiH,cAAc,GAAG,CAAC,iBAC/CnH,OAAA;QAAK8D,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnE3E,OAAA;UAAA2E,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfpF,OAAA;UAAA2E,QAAA,GAAOzE,KAAK,CAACiH,cAAc,EAAC,YAAU;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACN,EACAlF,KAAK,CAACkH,aAAa,IAAIlH,KAAK,CAACkH,aAAa,GAAG,CAAC,iBAC7CpH,OAAA;QAAK8D,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnE3E,OAAA;UAAA2E,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfpF,OAAA;UAAA2E,QAAA,GAAOzE,KAAK,CAACkH,aAAa,EAAC,WAAS;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjF,EAAA,CAtaIF,eAA+C;AAAAqH,EAAA,GAA/CrH,eAA+C;AAwarD,eAAeA,eAAe;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}