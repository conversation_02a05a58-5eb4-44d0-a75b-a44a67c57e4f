{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const contentRef = useRef(null);\n  const [shouldScroll, setShouldScroll] = useState(false);\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // No truncation needed - using auto-scroll instead\n\n  // Get category color - keep original button colors\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Keep original blue for buttons\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex(prev => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  // Check if content needs scrolling and set up auto-scroll\n  useEffect(() => {\n    const checkScrollNeed = () => {\n      if (contentRef.current) {\n        const element = contentRef.current;\n        const needsScroll = element.scrollHeight > element.clientHeight;\n        setShouldScroll(needsScroll);\n        if (needsScroll) {\n          // Start auto-scroll after 2 seconds\n          const startScrollTimeout = setTimeout(() => {\n            element.style.animation = 'none'; // Reset animation\n            element.offsetHeight; // Trigger reflow\n            element.style.animation = `autoScroll ${Math.max(10, element.scrollHeight / 20)}s linear infinite`;\n          }, 2000);\n          return () => clearTimeout(startScrollTimeout);\n        }\n      }\n    };\n    checkScrollNeed();\n  }, [announcement.content, images.length]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f9fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #e74c3c' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      height: 'auto',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block',\n      // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '15px',\n          marginBottom: '1.5rem',\n          fontSize: '1.8rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 6px 20px rgba(231, 76, 60, 0.4)'\n        },\n        children: announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: images.length > 0 ? '3.2rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#1f2937',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        },\n        children: announcement.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: contentRef,\n        style: {\n          fontSize: images.length > 0 ? '2.2rem' : '2.8rem',\n          lineHeight: '1.6',\n          color: '#374151',\n          background: 'rgba(255, 255, 255, 0.8)',\n          padding: images.length > 0 ? '1.5rem' : '2rem',\n          borderRadius: '15px',\n          border: '2px solid rgba(39, 174, 96, 0.2)',\n          wordWrap: 'break-word',\n          flex: 1,\n          marginBottom: '1.5rem',\n          textAlign: images.length > 0 ? 'left' : 'center',\n          maxHeight: images.length > 0 ? '300px' : '400px',\n          overflow: 'hidden',\n          position: 'relative'\n        },\n        children: announcement.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '20px',\n            fontWeight: '700',\n            fontSize: '1.4rem',\n            boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            alignSelf: 'flex-start'\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Date: \", formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Time: \", formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#27ae60',\n              fontWeight: '600'\n            },\n            children: [\"By: \", announcement.author_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '0 0 40%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        position: 'relative',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%',\n          height: '100%',\n          borderRadius: '0px',\n          // Removed border radius to show full image\n          overflow: 'hidden',\n          boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n          border: '4px solid #3498db',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: images[currentImageIndex].url,\n          alt: images[currentImageIndex].alt,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block',\n            transition: 'opacity 0.5s ease-in-out'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '1rem',\n            right: '1rem',\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '1.2rem',\n            fontWeight: '600'\n          },\n          children: [currentImageIndex + 1, \" / \", images.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '1rem',\n            right: '1rem',\n            background: 'rgba(39, 174, 96, 0.8)',\n            color: 'white',\n            padding: '0.5rem',\n            borderRadius: '50%',\n            fontSize: '0.8rem',\n            fontWeight: 'bold'\n          },\n          children: \"AUTO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 9\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            color: '#e74c3c'\n          },\n          children: \"LIKES:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: announcement.reaction_count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            color: '#3498db'\n          },\n          children: \"COMMENTS:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: announcement.comment_count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(TVAnnouncement, \"93FyZu3pSBPvM6oM22e/oaTBMMc=\");\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getImageUrl", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "_s", "currentImageIndex", "setCurrentImageIndex", "contentRef", "shouldScroll", "setShouldScroll", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_path", "imageUrl", "push", "url", "alt", "title", "image_url", "attachments", "for<PERSON>ach", "attachment", "index", "file_path", "match", "length", "interval", "setInterval", "prev", "clearInterval", "checkScrollNeed", "current", "element", "needsScroll", "scrollHeight", "clientHeight", "startScrollTimeout", "setTimeout", "style", "animation", "offsetHeight", "Math", "max", "clearTimeout", "content", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "height", "maxHeight", "display", "gap", "alignItems", "children", "flex", "flexDirection", "justifyContent", "zIndex", "color", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lineHeight", "textShadow", "wordWrap", "ref", "category_name", "alignSelf", "flexWrap", "created_at", "author_name", "minHeight", "width", "src", "objectFit", "transition", "onError", "e", "currentTarget", "bottom", "right", "top", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const contentRef = useRef<HTMLDivElement>(null);\n  const [shouldScroll, setShouldScroll] = useState(false);\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // No truncation needed - using auto-scroll instead\n\n  // Get category color - keep original button colors\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Keep original blue for buttons\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex((prev) => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  // Check if content needs scrolling and set up auto-scroll\n  useEffect(() => {\n    const checkScrollNeed = () => {\n      if (contentRef.current) {\n        const element = contentRef.current;\n        const needsScroll = element.scrollHeight > element.clientHeight;\n        setShouldScroll(needsScroll);\n\n        if (needsScroll) {\n          // Start auto-scroll after 2 seconds\n          const startScrollTimeout = setTimeout(() => {\n            element.style.animation = 'none'; // Reset animation\n            element.offsetHeight; // Trigger reflow\n            element.style.animation = `autoScroll ${Math.max(10, element.scrollHeight / 20)}s linear infinite`;\n          }, 2000);\n\n          return () => clearTimeout(startScrollTimeout);\n        }\n      }\n    };\n\n    checkScrollNeed();\n  }, [announcement.content, images.length]);\n\n  return (\n    <div style={{\n      background: 'linear-gradient(135deg, #ffffff 0%, #f9fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #e74c3c' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      height: 'auto',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block', // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    }}>\n      {/* Content Section - Full width if no images, 60% if images exist */}\n      <div style={{\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        {/* Alert indicator - keep original red color */}\n        {isUrgent && (\n          <div style={{\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '15px',\n            marginBottom: '1.5rem',\n            fontSize: '1.8rem',\n            fontWeight: 'bold',\n            textAlign: 'center',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 6px 20px rgba(231, 76, 60, 0.4)'\n          }}>\n            {announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED'}\n          </div>\n        )}\n\n        {/* Announcement title - Larger when no images */}\n        <h2 style={{\n          fontSize: images.length > 0 ? '3.2rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#1f2937',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        }}>\n          {announcement.title}\n        </h2>\n\n        {/* Announcement content - Auto-scrolling for long text */}\n        <div\n          ref={contentRef}\n          style={{\n            fontSize: images.length > 0 ? '2.2rem' : '2.8rem',\n            lineHeight: '1.6',\n            color: '#374151',\n            background: 'rgba(255, 255, 255, 0.8)',\n            padding: images.length > 0 ? '1.5rem' : '2rem',\n            borderRadius: '15px',\n            border: '2px solid rgba(39, 174, 96, 0.2)',\n            wordWrap: 'break-word',\n            flex: 1,\n            marginBottom: '1.5rem',\n            textAlign: images.length > 0 ? 'left' : 'center',\n            maxHeight: images.length > 0 ? '300px' : '400px',\n            overflow: 'hidden',\n            position: 'relative'\n          }}\n        >\n          {announcement.content}\n        </div>\n\n        {/* Metadata */}\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <div style={{\n              background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '20px',\n              fontWeight: '700',\n              fontSize: '1.4rem',\n              boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              alignSelf: 'flex-start'\n            }}>\n              {announcement.category_name}\n            </div>\n          )}\n\n          {/* Date, time, and author */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          }}>\n            <span>Date: {formatDate(announcement.created_at)}</span>\n            <span>Time: {formatTime(announcement.created_at)}</span>\n            {announcement.author_name && (\n              <span style={{ color: '#27ae60', fontWeight: '600' }}>\n                By: {announcement.author_name}\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Right Side - Images (40% width) */}\n      {images.length > 0 && (\n        <div style={{\n          flex: '0 0 40%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          position: 'relative',\n          minHeight: '400px'\n        }}>\n          {/* Single Image or Current Image from Carousel */}\n          <div style={{\n            width: '100%',\n            height: '100%',\n            borderRadius: '0px', // Removed border radius to show full image\n            overflow: 'hidden',\n            boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n            border: '4px solid #3498db',\n            position: 'relative'\n          }}>\n            <img\n              src={images[currentImageIndex].url}\n              alt={images[currentImageIndex].alt}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                display: 'block',\n                transition: 'opacity 0.5s ease-in-out'\n              }}\n              onError={(e) => {\n                e.currentTarget.style.display = 'none';\n              }}\n            />\n\n            {/* Image counter for multiple images */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                bottom: '1rem',\n                right: '1rem',\n                background: 'rgba(0, 0, 0, 0.7)',\n                color: 'white',\n                padding: '0.5rem 1rem',\n                borderRadius: '20px',\n                fontSize: '1.2rem',\n                fontWeight: '600'\n              }}>\n                {currentImageIndex + 1} / {images.length}\n              </div>\n            )}\n\n            {/* Auto-rotation indicator */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                top: '1rem',\n                right: '1rem',\n                background: 'rgba(39, 174, 96, 0.8)',\n                color: 'white',\n                padding: '0.5rem',\n                borderRadius: '50%',\n                fontSize: '0.8rem',\n                fontWeight: 'bold'\n              }}>\n                AUTO\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>LIKES:</span>\n              <span>{announcement.reaction_count}</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span style={{ fontWeight: 'bold', color: '#3498db' }}>COMMENTS:</span>\n              <span>{announcement.comment_count}</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAE1D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAMW,UAAU,GAAGT,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA,MAAMc,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,YAAY,CAACqB,cAAc,EAAE;MAC/B,OAAOrB,YAAY,CAACqB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGtB,YAAY,CAACuB,QAAQ,IAAIvB,YAAY,CAACwB,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI1B,YAAY,CAAC2B,UAAU,EAAE;MAC3B,MAAMC,QAAQ,GAAGhC,WAAW,CAACI,YAAY,CAAC2B,UAAU,CAAC;MACrD,IAAIC,QAAQ,EAAE;QACZF,MAAM,CAACG,IAAI,CAAC;UACVC,GAAG,EAAEF,QAAQ;UACbG,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIhC,YAAY,CAACiC,SAAS,IAAI,CAACjC,YAAY,CAAC2B,UAAU,EAAE;MACtDD,MAAM,CAACG,IAAI,CAAC;QACVC,GAAG,EAAE9B,YAAY,CAACiC,SAAS;QAC3BF,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIhC,YAAY,CAACkC,WAAW,EAAE;MAC5BlC,YAAY,CAACkC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,SAAS,IAAIF,UAAU,CAACE,SAAS,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACrF,MAAMX,QAAQ,GAAGhC,WAAW,CAACwC,UAAU,CAACE,SAAS,CAAC;UAClD,IAAIV,QAAQ,EAAE;YACZF,MAAM,CAACG,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK,YAAYK,KAAK,GAAG,CAAC;YACjD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;;EAEtC;EACA/B,SAAS,CAAC,MAAM;IACd,IAAIgC,MAAM,CAACc,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCvC,oBAAoB,CAAEwC,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIjB,MAAM,CAACc,MAAM,CAAC;MAC5D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMI,aAAa,CAACH,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACf,MAAM,CAACc,MAAM,CAAC,CAAC;;EAEnB;EACA9C,SAAS,CAAC,MAAM;IACd,MAAMmD,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIzC,UAAU,CAAC0C,OAAO,EAAE;QACtB,MAAMC,OAAO,GAAG3C,UAAU,CAAC0C,OAAO;QAClC,MAAME,WAAW,GAAGD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY;QAC/D5C,eAAe,CAAC0C,WAAW,CAAC;QAE5B,IAAIA,WAAW,EAAE;UACf;UACA,MAAMG,kBAAkB,GAAGC,UAAU,CAAC,MAAM;YAC1CL,OAAO,CAACM,KAAK,CAACC,SAAS,GAAG,MAAM,CAAC,CAAC;YAClCP,OAAO,CAACQ,YAAY,CAAC,CAAC;YACtBR,OAAO,CAACM,KAAK,CAACC,SAAS,GAAG,cAAcE,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEV,OAAO,CAACE,YAAY,GAAG,EAAE,CAAC,mBAAmB;UACpG,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,MAAMS,YAAY,CAACP,kBAAkB,CAAC;QAC/C;MACF;IACF,CAAC;IAEDN,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC7C,YAAY,CAAC2D,OAAO,EAAEjC,MAAM,CAACc,MAAM,CAAC,CAAC;EAEzC,oBACE1C,OAAA;IAAKuD,KAAK,EAAE;MACVO,UAAU,EAAE,mDAAmD;MAC/DC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAE3C,QAAQ,GAAG,mBAAmB,GAAG,aAAaF,gBAAgB,CAAC,CAAC,EAAE;MAC1E8C,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE5C,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;MAAE;MAC/C+B,GAAG,EAAE7C,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG;MACrCgC,UAAU,EAAE9C,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;IAC9C,CAAE;IAAAiC,QAAA,gBAEA3E,OAAA;MAAKuD,KAAK,EAAE;QACVqB,IAAI,EAAEhD,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG;QACzC8B,OAAO,EAAE,MAAM;QACfK,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,eAAe;QAC/BV,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,GAECnD,QAAQ,iBACPxB,OAAA;QAAKuD,KAAK,EAAE;UACVO,UAAU,EAAE,2CAA2C;UACvDkB,KAAK,EAAE,OAAO;UACdhB,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,MAAM;UACpBkB,YAAY,EAAE,QAAQ;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,QAAQ;UACnBC,aAAa,EAAE,WAAW;UAC1BC,aAAa,EAAE,KAAK;UACpBpB,SAAS,EAAE;QACb,CAAE;QAAAS,QAAA,EACCzE,YAAY,CAACuB,QAAQ,GAAG,iBAAiB,GAAG;MAAQ;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,eAGD1F,OAAA;QAAIuD,KAAK,EAAE;UACT2B,QAAQ,EAAEtD,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAC/CyC,UAAU,EAAE,KAAK;UACjBlB,MAAM,EAAE,cAAc;UACtBe,KAAK,EAAE,SAAS;UAChBW,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,8BAA8B;UAC1CC,QAAQ,EAAE,YAAY;UACtBT,SAAS,EAAExD,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAC1C,CAAE;QAAAiC,QAAA,EACCzE,YAAY,CAACgC;MAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGL1F,OAAA;QACE8F,GAAG,EAAExF,UAAW;QAChBiD,KAAK,EAAE;UACL2B,QAAQ,EAAEtD,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ;UACjDiD,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE,SAAS;UAChBlB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAEpC,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAC9CqB,YAAY,EAAE,MAAM;UACpBI,MAAM,EAAE,kCAAkC;UAC1C0B,QAAQ,EAAE,YAAY;UACtBjB,IAAI,EAAE,CAAC;UACPK,YAAY,EAAE,QAAQ;UACtBG,SAAS,EAAExD,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ;UAChD6B,SAAS,EAAE3C,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,OAAO;UAChD2B,QAAQ,EAAE,QAAQ;UAClBD,QAAQ,EAAE;QACZ,CAAE;QAAAO,QAAA,EAEDzE,YAAY,CAAC2D;MAAO;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAGN1F,OAAA;QAAKuD,KAAK,EAAE;UACViB,OAAO,EAAE,MAAM;UACfK,aAAa,EAAE,QAAQ;UACvBJ,GAAG,EAAE;QACP,CAAE;QAAAE,QAAA,GAECzE,YAAY,CAAC6F,aAAa,iBACzB/F,OAAA;UAAKuD,KAAK,EAAE;YACVO,UAAU,EAAE,2BAA2BxC,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrF0D,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBoB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE,QAAQ;YAClBhB,SAAS,EAAE,cAAc5C,gBAAgB,CAAC,CAAC,IAAI;YAC/C+D,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBU,SAAS,EAAE;UACb,CAAE;UAAArB,QAAA,EACCzE,YAAY,CAAC6F;QAAa;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN,eAGD1F,OAAA;UAAKuD,KAAK,EAAE;YACViB,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACbwB,QAAQ,EAAE,MAAM;YAChBf,QAAQ,EAAE,QAAQ;YAClBF,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,gBACA3E,OAAA;YAAA2E,QAAA,GAAM,QAAM,EAAClE,UAAU,CAACP,YAAY,CAACgG,UAAU,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxD1F,OAAA;YAAA2E,QAAA,GAAM,QAAM,EAACzD,UAAU,CAAChB,YAAY,CAACgG,UAAU,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACvDxF,YAAY,CAACiG,WAAW,iBACvBnG,OAAA;YAAMuD,KAAK,EAAE;cAAEyB,KAAK,EAAE,SAAS;cAAEG,UAAU,EAAE;YAAM,CAAE;YAAAR,QAAA,GAAC,MAChD,EAACzE,YAAY,CAACiG,WAAW;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9D,MAAM,CAACc,MAAM,GAAG,CAAC,iBAChB1C,OAAA;MAAKuD,KAAK,EAAE;QACVqB,IAAI,EAAE,SAAS;QACfJ,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBI,cAAc,EAAE,QAAQ;QACxBV,QAAQ,EAAE,UAAU;QACpBgC,SAAS,EAAE;MACb,CAAE;MAAAzB,QAAA,eAEA3E,OAAA;QAAKuD,KAAK,EAAE;UACV8C,KAAK,EAAE,MAAM;UACb/B,MAAM,EAAE,MAAM;UACdP,YAAY,EAAE,KAAK;UAAE;UACrBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE,mBAAmB;UAC3BC,QAAQ,EAAE;QACZ,CAAE;QAAAO,QAAA,gBACA3E,OAAA;UACEsG,GAAG,EAAE1E,MAAM,CAACxB,iBAAiB,CAAC,CAAC4B,GAAI;UACnCC,GAAG,EAAEL,MAAM,CAACxB,iBAAiB,CAAC,CAAC6B,GAAI;UACnCsB,KAAK,EAAE;YACL8C,KAAK,EAAE,MAAM;YACb/B,MAAM,EAAE,MAAM;YACdiC,SAAS,EAAE,OAAO;YAClB/B,OAAO,EAAE,OAAO;YAChBgC,UAAU,EAAE;UACd,CAAE;UACFC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAACpD,KAAK,CAACiB,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGD9D,MAAM,CAACc,MAAM,GAAG,CAAC,iBAChB1C,OAAA;UAAKuD,KAAK,EAAE;YACVa,QAAQ,EAAE,UAAU;YACpBwC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACb/C,UAAU,EAAE,oBAAoB;YAChCkB,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,aAAa;YACtBD,YAAY,EAAE,MAAM;YACpBmB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,GACCvE,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACwB,MAAM,CAACc,MAAM;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN,EAGA9D,MAAM,CAACc,MAAM,GAAG,CAAC,iBAChB1C,OAAA;UAAKuD,KAAK,EAAE;YACVa,QAAQ,EAAE,UAAU;YACpB0C,GAAG,EAAE,MAAM;YACXD,KAAK,EAAE,MAAM;YACb/C,UAAU,EAAE,wBAAwB;YACpCkB,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,KAAK;YACnBmB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAACxF,YAAY,CAAC6G,cAAc,IAAI7G,YAAY,CAAC8G,aAAa,kBACzDhH,OAAA;MAAKuD,KAAK,EAAE;QACV0D,SAAS,EAAE,MAAM;QACjBjD,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBS,OAAO,EAAE,MAAM;QACfC,GAAG,EAAE,MAAM;QACXS,QAAQ,EAAE;MACZ,CAAE;MAAAP,QAAA,GACCzE,YAAY,CAAC6G,cAAc,IAAI7G,YAAY,CAAC6G,cAAc,GAAG,CAAC,iBAC7D/G,OAAA;QAAKuD,KAAK,EAAE;UAAEiB,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnE3E,OAAA;UAAMuD,KAAK,EAAE;YAAE4B,UAAU,EAAE,MAAM;YAAEH,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,EAAC;QAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpE1F,OAAA;UAAA2E,QAAA,EAAOzE,YAAY,CAAC6G;QAAc;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACN,EACAxF,YAAY,CAAC8G,aAAa,IAAI9G,YAAY,CAAC8G,aAAa,GAAG,CAAC,iBAC3DhH,OAAA;QAAKuD,KAAK,EAAE;UAAEiB,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnE3E,OAAA;UAAMuD,KAAK,EAAE;YAAE4B,UAAU,EAAE,MAAM;YAAEH,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvE1F,OAAA;UAAA2E,QAAA,EAAOzE,YAAY,CAAC8G;QAAa;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvF,EAAA,CAnVIF,cAA6C;AAAAiH,EAAA,GAA7CjH,cAA6C;AAqVnD,eAAeA,cAAc;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}