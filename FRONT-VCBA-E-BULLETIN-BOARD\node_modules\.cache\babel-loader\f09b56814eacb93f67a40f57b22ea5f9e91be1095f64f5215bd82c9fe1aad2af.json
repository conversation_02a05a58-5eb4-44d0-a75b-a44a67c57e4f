{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\";\nimport React from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Smart content truncation for TV display (no scrolling available)\n  const truncateContent = (content, maxLength = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n  const isLongContent = announcement.content.length > 200;\n\n  // Get category color with green/yellow palette\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#27ae60'; // Default green\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f9fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2.5rem',\n      margin: '1.5rem 0',\n      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #f59e0b' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      height: 'auto',\n      maxHeight: '80vh',\n      // TV screen constraint\n      display: 'block' // Simple block layout for TV\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-40px',\n        right: '-40px',\n        width: '150px',\n        height: '150px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, #fbbf2420)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #f59e0b, #d97706)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '15px',\n        marginBottom: '1.5rem',\n        fontSize: '1.8rem',\n        fontWeight: 'bold',\n        textAlign: 'center',\n        textTransform: 'uppercase',\n        letterSpacing: '1px',\n        boxShadow: '0 6px 20px rgba(245, 158, 11, 0.4)',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: announcement.is_alert ? '⚠️ IMPORTANT ALERT' : '📌 PINNED'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: '3.2rem',\n        fontWeight: '700',\n        margin: '0 0 1.5rem 0',\n        color: '#1f2937',\n        lineHeight: '1.2',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n        wordWrap: 'break-word'\n      },\n      children: announcement.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1.5rem',\n        maxHeight: '250px',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [images.slice(0, 2).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '15px',\n          overflow: 'hidden',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)',\n          border: '3px solid #27ae60',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 13\n      }, this)), images.length > 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: 'linear-gradient(135deg, #fbbf24, #f59e0b)',\n          borderRadius: '15px',\n          fontSize: '1.8rem',\n          fontWeight: '700',\n          color: 'white',\n          border: '3px solid #27ae60',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)'\n        },\n        children: [\"+\", images.length - 2, \" more\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: isLongContent ? '1.6rem' : '2rem',\n        lineHeight: '1.6',\n        margin: '1rem 0',\n        color: '#34495e',\n        position: 'relative',\n        zIndex: 1,\n        background: 'rgba(255, 255, 255, 0.9)',\n        padding: isLongContent ? '1rem' : '1.5rem',\n        borderRadius: '12px',\n        border: '1px solid rgba(52, 152, 219, 0.1)',\n        flex: 1,\n        overflow: 'auto',\n        maxHeight: isLongContent ? '40vh' : 'none',\n        wordWrap: 'break-word',\n        hyphens: 'auto'\n      },\n      children: announcement.content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '1rem',\n        padding: '1rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '12px',\n        border: '1px solid rgba(52, 152, 219, 0.1)',\n        position: 'relative',\n        zIndex: 1,\n        flexShrink: 0,\n        flexWrap: 'wrap',\n        gap: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          flexWrap: 'wrap'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '15px',\n            fontWeight: '600',\n            fontSize: '1.2rem',\n            boxShadow: `0 4px 10px ${getCategoryColor()}30`,\n            textTransform: 'uppercase',\n            letterSpacing: '0.5px'\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            background: 'rgba(52, 152, 219, 0.1)',\n            padding: '0.8rem',\n            borderRadius: '8px',\n            fontSize: '1.2rem',\n            color: '#2c3e50'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\uD83D\\uDCC5 \", formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\uD83D\\uDD52 \", formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem',\n          fontSize: '1.2rem',\n          background: 'rgba(46, 204, 113, 0.1)',\n          padding: '0.8rem',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7,\n            fontWeight: '500'\n          },\n          children: \"By:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600',\n            color: '#27ae60'\n          },\n          children: announcement.author_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "getImageUrl", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "truncate<PERSON><PERSON><PERSON>", "content", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "is<PERSON>ong<PERSON><PERSON>nt", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_path", "imageUrl", "push", "url", "alt", "title", "image_url", "attachments", "for<PERSON>ach", "attachment", "index", "file_path", "match", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "height", "maxHeight", "display", "children", "top", "right", "width", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "lineHeight", "textShadow", "wordWrap", "gridTemplateColumns", "gap", "slice", "map", "image", "src", "objectFit", "onError", "e", "currentTarget", "alignItems", "justifyContent", "flex", "hyphens", "marginTop", "flexShrink", "flexWrap", "category_name", "created_at", "author_name", "opacity", "reaction_count", "comment_count", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Smart content truncation for TV display (no scrolling available)\n  const truncateContent = (content: string, maxLength: number = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  const isLongContent = announcement.content.length > 200;\n\n  // Get category color with green/yellow palette\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#27ae60'; // Default green\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  return (\n    <div style={{\n      background: 'linear-gradient(135deg, #ffffff 0%, #f9fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2.5rem',\n      margin: '1.5rem 0',\n      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #f59e0b' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      height: 'auto',\n      maxHeight: '80vh', // TV screen constraint\n      display: 'block' // Simple block layout for TV\n    }}>\n      {/* Background decoration with green/yellow theme */}\n      <div style={{\n        position: 'absolute',\n        top: '-40px',\n        right: '-40px',\n        width: '150px',\n        height: '150px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, #fbbf2420)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }} />\n\n      {/* Alert indicator with yellow theme */}\n      {isUrgent && (\n        <div style={{\n          background: 'linear-gradient(135deg, #f59e0b, #d97706)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '15px',\n          marginBottom: '1.5rem',\n          fontSize: '1.8rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 6px 20px rgba(245, 158, 11, 0.4)',\n          position: 'relative',\n          zIndex: 1\n        }}>\n          {announcement.is_alert ? '⚠️ IMPORTANT ALERT' : '📌 PINNED'}\n        </div>\n      )}\n\n      {/* Announcement title - TV optimized */}\n      <h2 style={{\n        fontSize: '3.2rem',\n        fontWeight: '700',\n        margin: '0 0 1.5rem 0',\n        color: '#1f2937',\n        lineHeight: '1.2',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n        wordWrap: 'break-word'\n      }}>\n        {announcement.title}\n      </h2>\n\n      {/* Announcement images - TV layout */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1.5rem',\n          maxHeight: '250px',\n          position: 'relative',\n          zIndex: 1\n        }}>\n          {images.slice(0, 2).map((image, index) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '15px',\n                overflow: 'hidden',\n                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)',\n                border: '3px solid #27ae60',\n                position: 'relative'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n          {images.length > 2 && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: 'linear-gradient(135deg, #fbbf24, #f59e0b)',\n              borderRadius: '15px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              color: 'white',\n              border: '3px solid #27ae60',\n              boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)'\n            }}>\n              +{images.length - 2} more\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Announcement content - scrollable for long content */}\n      <div style={{\n        fontSize: isLongContent ? '1.6rem' : '2rem',\n        lineHeight: '1.6',\n        margin: '1rem 0',\n        color: '#34495e',\n        position: 'relative',\n        zIndex: 1,\n        background: 'rgba(255, 255, 255, 0.9)',\n        padding: isLongContent ? '1rem' : '1.5rem',\n        borderRadius: '12px',\n        border: '1px solid rgba(52, 152, 219, 0.1)',\n        flex: 1,\n        overflow: 'auto',\n        maxHeight: isLongContent ? '40vh' : 'none',\n        wordWrap: 'break-word',\n        hyphens: 'auto'\n      }}>\n        {announcement.content}\n      </div>\n\n      {/* Announcement metadata - compact */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '1rem',\n        padding: '1rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '12px',\n        border: '1px solid rgba(52, 152, 219, 0.1)',\n        position: 'relative',\n        zIndex: 1,\n        flexShrink: 0,\n        flexWrap: 'wrap',\n        gap: '1rem'\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', flexWrap: 'wrap' }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <div style={{\n              background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n              color: 'white',\n              padding: '0.5rem 1rem',\n              borderRadius: '15px',\n              fontWeight: '600',\n              fontSize: '1.2rem',\n              boxShadow: `0 4px 10px ${getCategoryColor()}30`,\n              textTransform: 'uppercase',\n              letterSpacing: '0.5px'\n            }}>\n              {announcement.category_name}\n            </div>\n          )}\n\n          {/* Date and time */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            background: 'rgba(52, 152, 219, 0.1)',\n            padding: '0.8rem',\n            borderRadius: '8px',\n            fontSize: '1.2rem',\n            color: '#2c3e50'\n          }}>\n            <span>📅 {formatDate(announcement.created_at)}</span>\n            <span>🕒 {formatTime(announcement.created_at)}</span>\n          </div>\n        </div>\n\n        {/* Author information */}\n        {announcement.author_name && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.2rem',\n            background: 'rgba(46, 204, 113, 0.1)',\n            padding: '0.8rem',\n            borderRadius: '8px'\n          }}>\n            <span style={{ opacity: 0.7, fontWeight: '500' }}>By:</span>\n            <span style={{ fontWeight: '600', color: '#27ae60' }}>\n              {announcement.author_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{announcement.reaction_count} reactions</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{announcement.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAC1E;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,OAAe,EAAEC,SAAiB,GAAG,GAAG,KAAK;IACpE,IAAID,OAAO,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,OAAO;IAC/C,OAAOA,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EACvD,CAAC;EAED,MAAMC,aAAa,GAAGpB,YAAY,CAACe,OAAO,CAACE,MAAM,GAAG,GAAG;;EAEvD;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIrB,YAAY,CAACsB,cAAc,EAAE;MAC/B,OAAOtB,YAAY,CAACsB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGvB,YAAY,CAACwB,QAAQ,IAAIxB,YAAY,CAACyB,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI3B,YAAY,CAAC4B,UAAU,EAAE;MAC3B,MAAMC,QAAQ,GAAGjC,WAAW,CAACI,YAAY,CAAC4B,UAAU,CAAC;MACrD,IAAIC,QAAQ,EAAE;QACZF,MAAM,CAACG,IAAI,CAAC;UACVC,GAAG,EAAEF,QAAQ;UACbG,GAAG,EAAE,GAAGhC,YAAY,CAACiC,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIjC,YAAY,CAACkC,SAAS,IAAI,CAAClC,YAAY,CAAC4B,UAAU,EAAE;MACtDD,MAAM,CAACG,IAAI,CAAC;QACVC,GAAG,EAAE/B,YAAY,CAACkC,SAAS;QAC3BF,GAAG,EAAE,GAAGhC,YAAY,CAACiC,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIjC,YAAY,CAACmC,WAAW,EAAE;MAC5BnC,YAAY,CAACmC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,SAAS,IAAIF,UAAU,CAACE,SAAS,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACrF,MAAMX,QAAQ,GAAGjC,WAAW,CAACyC,UAAU,CAACE,SAAS,CAAC;UAClD,IAAIV,QAAQ,EAAE;YACZF,MAAM,CAACG,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGhC,YAAY,CAACiC,KAAK,YAAYK,KAAK,GAAG,CAAC;YACjD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;EAEtC,oBACE5B,OAAA;IAAK2C,KAAK,EAAE;MACVC,UAAU,EAAE,mDAAmD;MAC/DC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAExB,QAAQ,GAAG,mBAAmB,GAAG,aAAaF,gBAAgB,CAAC,CAAC,EAAE;MAC1E2B,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MAAE;MACnBC,OAAO,EAAE,OAAO,CAAC;IACnB,CAAE;IAAAC,QAAA,gBAEAvD,OAAA;MAAK2C,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBM,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE,OAAO;QACdN,MAAM,EAAE,OAAO;QACfR,UAAU,EAAE,0BAA0BrB,gBAAgB,CAAC,CAAC,gBAAgB;QACxEsB,YAAY,EAAE,KAAK;QACnBc,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGJtC,QAAQ,iBACPzB,OAAA;MAAK2C,KAAK,EAAE;QACVC,UAAU,EAAE,2CAA2C;QACvDoB,KAAK,EAAE,OAAO;QACdlB,OAAO,EAAE,WAAW;QACpBD,YAAY,EAAE,MAAM;QACpBoB,YAAY,EAAE,QAAQ;QACtBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,QAAQ;QACnBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,KAAK;QACpBtB,SAAS,EAAE,oCAAoC;QAC/CE,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,EACCrD,YAAY,CAACwB,QAAQ,GAAG,oBAAoB,GAAG;IAAW;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACN,eAGD/D,OAAA;MAAI2C,KAAK,EAAE;QACTuB,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,KAAK;QACjBpB,MAAM,EAAE,cAAc;QACtBiB,KAAK,EAAE,SAAS;QAChBO,UAAU,EAAE,KAAK;QACjBrB,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE,CAAC;QACTa,UAAU,EAAE,8BAA8B;QAC1CC,QAAQ,EAAE;MACZ,CAAE;MAAAlB,QAAA,EACCrD,YAAY,CAACiC;IAAK;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAGJlC,MAAM,CAACV,MAAM,GAAG,CAAC,iBAChBnB,OAAA;MAAK2C,KAAK,EAAE;QACVsB,YAAY,EAAE,MAAM;QACpBX,OAAO,EAAE,MAAM;QACfoB,mBAAmB,EAAE7C,MAAM,CAACV,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzFwD,GAAG,EAAE,QAAQ;QACbtB,SAAS,EAAE,OAAO;QAClBH,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,GACC1B,MAAM,CAAC+C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEtC,KAAK,kBACnCxC,OAAA;QAEE2C,KAAK,EAAE;UACLE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE,mBAAmB;UAC3BC,QAAQ,EAAE;QACZ,CAAE;QAAAK,QAAA,eAEFvD,OAAA;UACE+E,GAAG,EAAED,KAAK,CAAC7C,GAAI;UACfC,GAAG,EAAE4C,KAAK,CAAC5C,GAAI;UACfS,KAAK,EAAE;YACLe,KAAK,EAAE,MAAM;YACbN,MAAM,EAAE,OAAO;YACf4B,SAAS,EAAE,OAAO;YAClB1B,OAAO,EAAE;UACX,CAAE;UACF2B,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACW,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GArBGvB,KAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsBP,CACN,CAAC,EACDlC,MAAM,CAACV,MAAM,GAAG,CAAC,iBAChBnB,OAAA;QAAK2C,KAAK,EAAE;UACVW,OAAO,EAAE,MAAM;UACf8B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBzC,UAAU,EAAE,2CAA2C;UACvDC,YAAY,EAAE,MAAM;UACpBqB,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBH,KAAK,EAAE,OAAO;UACdf,MAAM,EAAE,mBAAmB;UAC3BD,SAAS,EAAE;QACb,CAAE;QAAAO,QAAA,GAAC,GACA,EAAC1B,MAAM,CAACV,MAAM,GAAG,CAAC,EAAC,OACtB;MAAA;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD/D,OAAA;MAAK2C,KAAK,EAAE;QACVuB,QAAQ,EAAE5C,aAAa,GAAG,QAAQ,GAAG,MAAM;QAC3CiD,UAAU,EAAE,KAAK;QACjBxB,MAAM,EAAE,QAAQ;QAChBiB,KAAK,EAAE,SAAS;QAChBd,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE,CAAC;QACTf,UAAU,EAAE,0BAA0B;QACtCE,OAAO,EAAExB,aAAa,GAAG,MAAM,GAAG,QAAQ;QAC1CuB,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE,mCAAmC;QAC3CqC,IAAI,EAAE,CAAC;QACPnC,QAAQ,EAAE,MAAM;QAChBE,SAAS,EAAE/B,aAAa,GAAG,MAAM,GAAG,MAAM;QAC1CmD,QAAQ,EAAE,YAAY;QACtBc,OAAO,EAAE;MACX,CAAE;MAAAhC,QAAA,EACCrD,YAAY,CAACe;IAAO;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGN/D,OAAA;MAAK2C,KAAK,EAAE;QACVW,OAAO,EAAE,MAAM;QACf+B,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBI,SAAS,EAAE,MAAM;QACjB1C,OAAO,EAAE,MAAM;QACfF,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE,mCAAmC;QAC3CC,QAAQ,EAAE,UAAU;QACpBS,MAAM,EAAE,CAAC;QACT8B,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE,MAAM;QAChBf,GAAG,EAAE;MACP,CAAE;MAAApB,QAAA,gBACAvD,OAAA;QAAK2C,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAE8B,UAAU,EAAE,QAAQ;UAAET,GAAG,EAAE,QAAQ;UAAEe,QAAQ,EAAE;QAAO,CAAE;QAAAnC,QAAA,GAEpFrD,YAAY,CAACyF,aAAa,iBACzB3F,OAAA;UAAK2C,KAAK,EAAE;YACVC,UAAU,EAAE,2BAA2BrB,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrFyC,KAAK,EAAE,OAAO;YACdlB,OAAO,EAAE,aAAa;YACtBD,YAAY,EAAE,MAAM;YACpBsB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE,QAAQ;YAClBlB,SAAS,EAAE,cAAczB,gBAAgB,CAAC,CAAC,IAAI;YAC/C8C,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAf,QAAA,EACCrD,YAAY,CAACyF;QAAa;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN,eAGD/D,OAAA;UAAK2C,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACf8B,UAAU,EAAE,QAAQ;YACpBT,GAAG,EAAE,MAAM;YACX/B,UAAU,EAAE,yBAAyB;YACrCE,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,KAAK;YACnBqB,QAAQ,EAAE,QAAQ;YAClBF,KAAK,EAAE;UACT,CAAE;UAAAT,QAAA,gBACAvD,OAAA;YAAAuD,QAAA,GAAM,eAAG,EAACpD,UAAU,CAACD,YAAY,CAAC0F,UAAU,CAAC;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrD/D,OAAA;YAAAuD,QAAA,GAAM,eAAG,EAAC3C,UAAU,CAACV,YAAY,CAAC0F,UAAU,CAAC;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL7D,YAAY,CAAC2F,WAAW,iBACvB7F,OAAA;QAAK2C,KAAK,EAAE;UACVW,OAAO,EAAE,MAAM;UACf8B,UAAU,EAAE,QAAQ;UACpBT,GAAG,EAAE,QAAQ;UACbT,QAAQ,EAAE,QAAQ;UAClBtB,UAAU,EAAE,yBAAyB;UACrCE,OAAO,EAAE,QAAQ;UACjBD,YAAY,EAAE;QAChB,CAAE;QAAAU,QAAA,gBACAvD,OAAA;UAAM2C,KAAK,EAAE;YAAEmD,OAAO,EAAE,GAAG;YAAE3B,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAAG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5D/D,OAAA;UAAM2C,KAAK,EAAE;YAAEwB,UAAU,EAAE,KAAK;YAAEH,KAAK,EAAE;UAAU,CAAE;UAAAT,QAAA,EAClDrD,YAAY,CAAC2F;QAAW;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAC7D,YAAY,CAAC6F,cAAc,IAAI7F,YAAY,CAAC8F,aAAa,kBACzDhG,OAAA;MAAK2C,KAAK,EAAE;QACV6C,SAAS,EAAE,MAAM;QACjB1C,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBS,OAAO,EAAE,MAAM;QACfqB,GAAG,EAAE,MAAM;QACXT,QAAQ,EAAE;MACZ,CAAE;MAAAX,QAAA,GACCrD,YAAY,CAAC6F,cAAc,IAAI7F,YAAY,CAAC6F,cAAc,GAAG,CAAC,iBAC7D/F,OAAA;QAAK2C,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAE8B,UAAU,EAAE,QAAQ;UAAET,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnEvD,OAAA;UAAAuD,QAAA,EAAM;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf/D,OAAA;UAAAuD,QAAA,GAAOrD,YAAY,CAAC6F,cAAc,EAAC,YAAU;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACN,EACA7D,YAAY,CAAC8F,aAAa,IAAI9F,YAAY,CAAC8F,aAAa,GAAG,CAAC,iBAC3DhG,OAAA;QAAK2C,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAE8B,UAAU,EAAE,QAAQ;UAAET,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnEvD,OAAA;UAAAuD,QAAA,EAAM;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf/D,OAAA;UAAAuD,QAAA,GAAOrD,YAAY,CAAC8F,aAAa,EAAC,WAAS;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACkC,EAAA,GA9TIhG,cAA6C;AAgUnD,eAAeA,cAAc;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}