{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\tv\\\\TVDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TVDisplay = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Listen for real-time settings updates via localStorage\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'tv_display_settings') {\n        // Settings changed, reload them\n        const newSettings = tvControlService.getSettings();\n        setSettings(newSettings);\n      } else if (e.key === 'tv_display_settings_updated') {\n        // Settings update signal, reload them\n        const newSettings = tvControlService.getSettings();\n        setSettings(newSettings);\n      }\n    };\n\n    // Listen for storage changes from other tabs/windows\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also check for settings changes periodically (for same-tab updates)\n    const settingsCheckInterval = setInterval(() => {\n      const currentSettings = tvControlService.getSettings();\n      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {\n        setSettings(currentSettings);\n      }\n    }, 1000); // Check every second\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(settingsCheckInterval);\n    };\n  }, [settings]);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach(command => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = command => {\n    var _slideshowRef$current, _slideshowRef$current2;\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if ((_slideshowRef$current = slideshowRef.current) !== null && _slideshowRef$current !== void 0 && _slideshowRef$current.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if ((_slideshowRef$current2 = slideshowRef.current) !== null && _slideshowRef$current2 !== void 0 && _slideshowRef$current2.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 || event.category_id && settings.eventCategories.includes(event.category_id);\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime()).slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides = [];\n\n    // Add announcements\n    if (settings.showAnnouncements && announcements && announcements.length > 0) {\n      const filteredAnnouncements = announcements.filter(announcement => {\n        const matchesCategory = settings.announcementCategories.length === 0 || settings.announcementCategories.includes(announcement.category_id);\n        return matchesCategory;\n      }).slice(0, settings.maxAnnouncements);\n      filteredAnnouncements.forEach((announcement, index) => slides.push(/*#__PURE__*/_jsxDEV(TVAnnouncement, {\n        announcement: announcement\n      }, `announcement-${announcement.announcement_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event, index) => slides.push(/*#__PURE__*/_jsxDEV(TVCalendarEvent, {\n        event: event\n      }, `event-${event.calendar_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)));\n    }\n    return slides;\n  };\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tv-display\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"tv-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"VCBA E-Bulletin Board\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subtitle\",\n        children: \"Villamor College of Business and Arts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-datetime\",\n      children: formatDateTime()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"tv-content\",\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading latest announcements and events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), hasError && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '2rem'\n          },\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Unable to load content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            marginTop: '1rem',\n            opacity: 0.8\n          },\n          children: \"Please check your internet connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this), settings.emergencyActive && settings.emergencyMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(220, 53, 69, 0.95)',\n          color: 'white',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999,\n          animation: 'emergency-flash 2s infinite'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '8rem',\n            marginBottom: '2rem',\n            animation: 'emergency-pulse 1s infinite'\n          },\n          children: \"\\uD83D\\uDEA8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            fontWeight: 'bold',\n            textAlign: 'center',\n            marginBottom: '2rem',\n            textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'\n          },\n          children: \"EMERGENCY ALERT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            textAlign: 'center',\n            lineHeight: '1.4',\n            maxWidth: '80%',\n            background: 'rgba(0, 0, 0, 0.3)',\n            padding: '2rem',\n            borderRadius: '20px'\n          },\n          children: settings.emergencyMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this), !isLoading && !hasError && !settings.emergencyActive && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: slides.length > 0 ? /*#__PURE__*/_jsxDEV(TVSlideshow, {\n          ref: slideshowRef,\n          autoPlayInterval: settings.slideInterval,\n          showProgress: true,\n          isPlaying: isPlaying && settings.autoPlay,\n          onSlideChange: setCurrentSlide,\n          children: slides\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-no-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '6rem',\n              marginBottom: '3rem'\n            },\n            children: \"\\uD83D\\uDCE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No announcements or events to display\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              marginTop: '2rem',\n              opacity: 0.7\n            },\n            children: \"Check back later for updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '1rem',\n        right: '2rem',\n        background: 'rgba(0, 0, 0, 0.6)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '20px',\n        fontSize: '1.4rem',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\uD83D\\uDD04\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Auto-refresh active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      httpEquiv: \"refresh\",\n      content: \"600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(TVDisplay, \"KDYFlrHSu63QwnfhujBG3li3Uf4=\", false, function () {\n  return [useAnnouncements, useCalendar];\n});\n_c = TVDisplay;\nexport default TVDisplay;\nvar _c;\n$RefreshReg$(_c, \"TVDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAnnouncements", "useCalendar", "tvControlService", "TVAnnouncement", "TVCalendarEvent", "TVSlideshow", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TVDisplay", "_s", "currentTime", "setCurrentTime", "Date", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "settings", "setSettings", "getSettings", "isPlaying", "setIsPlaying", "currentSlide", "setCurrentSlide", "slideshowRef", "currentDate", "announcements", "loading", "announcementsLoading", "error", "announcementsError", "refresh", "refreshAnnouncements", "status", "page", "limit", "maxAnnouncements", "sort_by", "sort_order", "events", "eventsLoading", "eventsError", "refreshEvents", "unsubscribe", "onSettingsChange", "handleStorageChange", "e", "key", "newSettings", "window", "addEventListener", "settingsCheckInterval", "setInterval", "currentSettings", "JSON", "stringify", "removeEventListener", "clearInterval", "sendHeartbeat", "localStorage", "setItem", "now", "toString", "updateStatus", "isOnline", "totalSlides", "createSlideContent", "length", "lastRefresh", "toISOString", "heartbeatInterval", "checkCommands", "commands", "getStoredCommands", "for<PERSON>ach", "command", "handleControlCommand", "clearProcessedCommands", "commandInterval", "_slideshowRef$current", "_slideshowRef$current2", "action", "current", "nextSlide", "prevSlide", "prev", "timeInterval", "refreshInterval", "reloadInterval", "location", "reload", "formatDateTime", "options", "weekday", "year", "month", "day", "hour", "minute", "toLocaleDateString", "getUpcomingEvents", "showCalendarEvents", "today", "thirtyDaysFromNow", "setDate", "getDate", "filter", "event", "eventDate", "event_date", "matchesCategory", "eventCategories", "category_id", "includes", "is_active", "sort", "a", "b", "getTime", "slice", "maxEvents", "slides", "showAnnouncements", "filteredAnnouncements", "announcement", "announcementCategories", "index", "push", "announcement_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "upcomingEvents", "calendar_id", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "style", "fontSize", "marginBottom", "marginTop", "opacity", "emergencyActive", "emergencyMessage", "position", "top", "left", "right", "bottom", "background", "color", "display", "flexDirection", "alignItems", "justifyContent", "zIndex", "animation", "fontWeight", "textAlign", "textShadow", "lineHeight", "max<PERSON><PERSON><PERSON>", "padding", "borderRadius", "ref", "autoPlayInterval", "slideInterval", "showProgress", "autoPlay", "onSlideChange", "gap", "httpEquiv", "content", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/tv/TVDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService, TVDisplaySettings, TVControlCommand } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\n\nconst TVDisplay: React.FC = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState<TVDisplaySettings>(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef<any>(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Listen for real-time settings updates via localStorage\n  useEffect(() => {\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'tv_display_settings') {\n        // Settings changed, reload them\n        const newSettings = tvControlService.getSettings();\n        setSettings(newSettings);\n      } else if (e.key === 'tv_display_settings_updated') {\n        // Settings update signal, reload them\n        const newSettings = tvControlService.getSettings();\n        setSettings(newSettings);\n      }\n    };\n\n    // Listen for storage changes from other tabs/windows\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also check for settings changes periodically (for same-tab updates)\n    const settingsCheckInterval = setInterval(() => {\n      const currentSettings = tvControlService.getSettings();\n      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {\n        setSettings(currentSettings);\n      }\n    }, 1000); // Check every second\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(settingsCheckInterval);\n    };\n  }, [settings]);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach((command: TVControlCommand) => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = (command: TVControlCommand) => {\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if (slideshowRef.current?.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if (slideshowRef.current?.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options: Intl.DateTimeFormatOptions = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 ||\n        (event.category_id && settings.eventCategories.includes(event.category_id));\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())\n      .slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides: React.ReactNode[] = [];\n\n    // Add announcements\n    if (settings.showAnnouncements && announcements && announcements.length > 0) {\n      const filteredAnnouncements = announcements.filter(announcement => {\n        const matchesCategory = settings.announcementCategories.length === 0 ||\n          settings.announcementCategories.includes(announcement.category_id);\n        return matchesCategory;\n      }).slice(0, settings.maxAnnouncements);\n\n      filteredAnnouncements.forEach((announcement, index) => (\n        slides.push(\n          <TVAnnouncement\n            key={`announcement-${announcement.announcement_id}-${refreshKey}`}\n            announcement={announcement}\n          />\n        )\n      ));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event, index) => (\n        slides.push(\n          <TVCalendarEvent\n            key={`event-${event.calendar_id}-${refreshKey}`}\n            event={event}\n          />\n        )\n      ));\n    }\n\n    return slides;\n  };\n\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n\n  return (\n    <div className=\"tv-display\">\n      {/* Header with school branding */}\n      <header className=\"tv-header\">\n        <h1>VCBA E-Bulletin Board</h1>\n        <div className=\"subtitle\">Villamor College of Business and Arts</div>\n      </header>\n\n      {/* Current date and time */}\n      <div className=\"tv-datetime\">\n        {formatDateTime()}\n      </div>\n\n      {/* Main content area */}\n      <main className=\"tv-content\">\n        {/* Loading state */}\n        {isLoading && (\n          <div className=\"tv-loading\">\n            <div className=\"tv-loading-spinner\"></div>\n            <div>Loading latest announcements and events...</div>\n          </div>\n        )}\n\n        {/* Error state */}\n        {hasError && !isLoading && (\n          <div className=\"tv-error\">\n            <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>⚠️</div>\n            <div>Unable to load content</div>\n            <div style={{ fontSize: '2rem', marginTop: '1rem', opacity: 0.8 }}>\n              Please check your internet connection\n            </div>\n          </div>\n        )}\n\n        {/* Emergency Message Override */}\n        {settings.emergencyActive && settings.emergencyMessage && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(220, 53, 69, 0.95)',\n            color: 'white',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 9999,\n            animation: 'emergency-flash 2s infinite'\n          }}>\n            <div style={{\n              fontSize: '8rem',\n              marginBottom: '2rem',\n              animation: 'emergency-pulse 1s infinite'\n            }}>\n              🚨\n            </div>\n            <div style={{\n              fontSize: '4rem',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              marginBottom: '2rem',\n              textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'\n            }}>\n              EMERGENCY ALERT\n            </div>\n            <div style={{\n              fontSize: '3rem',\n              textAlign: 'center',\n              lineHeight: '1.4',\n              maxWidth: '80%',\n              background: 'rgba(0, 0, 0, 0.3)',\n              padding: '2rem',\n              borderRadius: '20px'\n            }}>\n              {settings.emergencyMessage}\n            </div>\n          </div>\n        )}\n\n        {/* Content slideshow */}\n        {!isLoading && !hasError && !settings.emergencyActive && (\n          <>\n            {slides.length > 0 ? (\n              <TVSlideshow\n                ref={slideshowRef}\n                autoPlayInterval={settings.slideInterval}\n                showProgress={true}\n                isPlaying={isPlaying && settings.autoPlay}\n                onSlideChange={setCurrentSlide}\n              >\n                {slides}\n              </TVSlideshow>\n            ) : (\n              <div className=\"tv-no-content\">\n                <div style={{ fontSize: '6rem', marginBottom: '3rem' }}>📢</div>\n                <div>No announcements or events to display</div>\n                <div style={{ fontSize: '2rem', marginTop: '2rem', opacity: 0.7 }}>\n                  Check back later for updates\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </main>\n\n      {/* Footer with refresh indicator */}\n      <div style={{\n        position: 'fixed',\n        bottom: '1rem',\n        right: '2rem',\n        background: 'rgba(0, 0, 0, 0.6)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '20px',\n        fontSize: '1.4rem',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      }}>\n        <span>🔄</span>\n        <span>Auto-refresh active</span>\n      </div>\n\n      {/* Meta refresh as backup */}\n      <meta httpEquiv=\"refresh\" content=\"600\" />\n    </div>\n  );\n};\n\nexport default TVDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAA6C,iCAAiC;AACvG,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAoBK,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,YAAY,GAAGzB,MAAM,CAAM,IAAI,CAAC;;EAEtC;EACA,MAAM0B,WAAW,GAAG,IAAIX,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAM;IACJY,aAAa;IACbC,OAAO,EAAEC,oBAAoB;IAC7BC,KAAK,EAAEC,kBAAkB;IACzBC,OAAO,EAAEC;EACX,CAAC,GAAGhC,gBAAgB,CAAC;IACnBiC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAElB,QAAQ,CAACmB,gBAAgB;IAChCC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA,MAAM;IACJC,MAAM;IACNZ,OAAO,EAAEa,aAAa;IACtBX,KAAK,EAAEY,WAAW;IAClBV,OAAO,EAAEW;EACX,CAAC,GAAGzC,WAAW,CAACwB,WAAW,CAAC;;EAE5B;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM6C,WAAW,GAAGzC,gBAAgB,CAAC0C,gBAAgB,CAAC1B,WAAW,CAAC;IAClE,OAAOyB,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAM+C,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACC,GAAG,KAAK,qBAAqB,EAAE;QACnC;QACA,MAAMC,WAAW,GAAG9C,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClDD,WAAW,CAAC8B,WAAW,CAAC;MAC1B,CAAC,MAAM,IAAIF,CAAC,CAACC,GAAG,KAAK,6BAA6B,EAAE;QAClD;QACA,MAAMC,WAAW,GAAG9C,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClDD,WAAW,CAAC8B,WAAW,CAAC;MAC1B;IACF,CAAC;;IAED;IACAC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;;IAEvD;IACA,MAAMM,qBAAqB,GAAGC,WAAW,CAAC,MAAM;MAC9C,MAAMC,eAAe,GAAGnD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;MACtD,IAAImC,IAAI,CAACC,SAAS,CAACF,eAAe,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACtC,QAAQ,CAAC,EAAE;QAChEC,WAAW,CAACmC,eAAe,CAAC;MAC9B;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAM;MACXJ,MAAM,CAACO,mBAAmB,CAAC,SAAS,EAAEX,mBAAmB,CAAC;MAC1DY,aAAa,CAACN,qBAAqB,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAAClC,QAAQ,CAAC,CAAC;;EAEd;EACAnB,SAAS,CAAC,MAAM;IACd,MAAM4D,aAAa,GAAGA,CAAA,KAAM;MAC1BC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAE9C,IAAI,CAAC+C,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MACnE5D,gBAAgB,CAAC6D,YAAY,CAAC;QAC5BC,QAAQ,EAAE,IAAI;QACd5C,SAAS;QACTE,YAAY;QACZ2C,WAAW,EAAEC,kBAAkB,CAAC,CAAC,CAACC,MAAM;QACxCC,WAAW,EAAE,IAAItD,IAAI,CAAC,CAAC,CAACuD,WAAW,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IAEDX,aAAa,CAAC,CAAC;IACf,MAAMY,iBAAiB,GAAGlB,WAAW,CAACM,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE5D,OAAO,MAAMD,aAAa,CAACa,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAClD,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAE7B;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMyE,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,QAAQ,GAAGtE,gBAAgB,CAACuE,iBAAiB,CAAC,CAAC;MACrD,IAAID,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;QACvBK,QAAQ,CAACE,OAAO,CAAEC,OAAyB,IAAK;UAC9CC,oBAAoB,CAACD,OAAO,CAAC;QAC/B,CAAC,CAAC;QACFzE,gBAAgB,CAAC2E,sBAAsB,CAAC,CAAC;MAC3C;IACF,CAAC;IAED,MAAMC,eAAe,GAAG1B,WAAW,CAACmB,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,OAAO,MAAMd,aAAa,CAACqB,eAAe,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMF,oBAAoB,GAAID,OAAyB,IAAK;IAAA,IAAAI,qBAAA,EAAAC,sBAAA;IAC1D,QAAQL,OAAO,CAACM,MAAM;MACpB,KAAK,MAAM;QACT5D,YAAY,CAAC,IAAI,CAAC;QAClB;MACF,KAAK,OAAO;QACVA,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,KAAK,MAAM;QACT,KAAA0D,qBAAA,GAAIvD,YAAY,CAAC0D,OAAO,cAAAH,qBAAA,eAApBA,qBAAA,CAAsBI,SAAS,EAAE;UACnC3D,YAAY,CAAC0D,OAAO,CAACC,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,UAAU;QACb,KAAAH,sBAAA,GAAIxD,YAAY,CAAC0D,OAAO,cAAAF,sBAAA,eAApBA,sBAAA,CAAsBI,SAAS,EAAE;UACnC5D,YAAY,CAAC0D,OAAO,CAACE,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,SAAS;QACZpD,oBAAoB,CAAC,CAAC;QACtBU,aAAa,CAAC,CAAC;QACf1B,aAAa,CAACqE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/B;MACF,KAAK,WAAW;QACd;QACA;IACJ;EACF,CAAC;;EAED;EACAvF,SAAS,CAAC,MAAM;IACd,MAAMwF,YAAY,GAAGlC,WAAW,CAAC,MAAM;MACrCvC,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM2C,aAAa,CAAC6B,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxF,SAAS,CAAC,MAAM;IACd,MAAMyF,eAAe,GAAGnC,WAAW,CAAC,MAAM;MACxCpB,oBAAoB,CAAC,CAAC;MACtBU,aAAa,CAAC,CAAC;MACf1B,aAAa,CAACqE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAM5B,aAAa,CAAC8B,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACvD,oBAAoB,EAAEU,aAAa,CAAC,CAAC;;EAEzC;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM0F,cAAc,GAAGpC,WAAW,CAAC,MAAM;MACvCH,MAAM,CAACwC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMjC,aAAa,CAAC+B,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAmC,GAAG;MAC1CC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD,OAAOtF,WAAW,CAACuF,kBAAkB,CAAC,OAAO,EAAEP,OAAO,CAAC;EACzD,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACnF,QAAQ,CAACoF,kBAAkB,EAAE,OAAO,EAAE;IAE3C,MAAMC,KAAK,GAAG,IAAIxF,IAAI,CAAC,CAAC;IACxB,MAAMyF,iBAAiB,GAAG,IAAIzF,IAAI,CAAC,CAAC;IACpCyF,iBAAiB,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE/C,OAAOlE,MAAM,CAACmE,MAAM,CAACC,KAAK,IAAI;MAC5B,MAAMC,SAAS,GAAG,IAAI9F,IAAI,CAAC6F,KAAK,CAACE,UAAU,CAAC;MAC5C,MAAMC,eAAe,GAAG7F,QAAQ,CAAC8F,eAAe,CAAC5C,MAAM,KAAK,CAAC,IAC1DwC,KAAK,CAACK,WAAW,IAAI/F,QAAQ,CAAC8F,eAAe,CAACE,QAAQ,CAACN,KAAK,CAACK,WAAW,CAAE;MAC7E,OAAOJ,SAAS,IAAIN,KAAK,IAAIM,SAAS,IAAIL,iBAAiB,IAAII,KAAK,CAACO,SAAS,IAAIJ,eAAe;IACnG,CAAC,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvG,IAAI,CAACsG,CAAC,CAACP,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,GAAG,IAAIxG,IAAI,CAACuG,CAAC,CAACR,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CACnFC,KAAK,CAAC,CAAC,EAAEtG,QAAQ,CAACuG,SAAS,CAAC;EACjC,CAAC;;EAED;EACA,MAAMtD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMuD,MAAyB,GAAG,EAAE;;IAEpC;IACA,IAAIxG,QAAQ,CAACyG,iBAAiB,IAAIhG,aAAa,IAAIA,aAAa,CAACyC,MAAM,GAAG,CAAC,EAAE;MAC3E,MAAMwD,qBAAqB,GAAGjG,aAAa,CAACgF,MAAM,CAACkB,YAAY,IAAI;QACjE,MAAMd,eAAe,GAAG7F,QAAQ,CAAC4G,sBAAsB,CAAC1D,MAAM,KAAK,CAAC,IAClElD,QAAQ,CAAC4G,sBAAsB,CAACZ,QAAQ,CAACW,YAAY,CAACZ,WAAW,CAAC;QACpE,OAAOF,eAAe;MACxB,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,EAAEtG,QAAQ,CAACmB,gBAAgB,CAAC;MAEtCuF,qBAAqB,CAACjD,OAAO,CAAC,CAACkD,YAAY,EAAEE,KAAK,KAChDL,MAAM,CAACM,IAAI,cACTxH,OAAA,CAACJ,cAAc;QAEbyH,YAAY,EAAEA;MAAa,GADtB,gBAAgBA,YAAY,CAACI,eAAe,IAAIjH,UAAU,EAAE;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CACH,CACD,CAAC;IACJ;;IAEA;IACA,MAAMC,cAAc,GAAGjC,iBAAiB,CAAC,CAAC;IAC1C,IAAIiC,cAAc,CAAClE,MAAM,GAAG,CAAC,EAAE;MAC7BkE,cAAc,CAAC3D,OAAO,CAAC,CAACiC,KAAK,EAAEmB,KAAK,KAClCL,MAAM,CAACM,IAAI,cACTxH,OAAA,CAACH,eAAe;QAEduG,KAAK,EAAEA;MAAM,GADR,SAASA,KAAK,CAAC2B,WAAW,IAAIvH,UAAU,EAAE;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhD,CACH,CACD,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGvD,kBAAkB,CAAC,CAAC;EACnC,MAAMqE,SAAS,GAAG3G,oBAAoB,IAAIY,aAAa;EACvD,MAAMgG,QAAQ,GAAG1G,kBAAkB,IAAIW,WAAW;EAElD,oBACElC,OAAA;IAAKkI,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzBnI,OAAA;MAAQkI,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3BnI,OAAA;QAAAmI,QAAA,EAAI;MAAqB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B7H,OAAA;QAAKkI,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAqC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGT7H,OAAA;MAAKkI,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB/C,cAAc,CAAC;IAAC;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGN7H,OAAA;MAAMkI,SAAS,EAAC,YAAY;MAAAC,QAAA,GAEzBH,SAAS,iBACRhI,OAAA;QAAKkI,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBnI,OAAA;UAAKkI,SAAS,EAAC;QAAoB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C7H,OAAA;UAAAmI,QAAA,EAAK;QAA0C;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,EAGAI,QAAQ,IAAI,CAACD,SAAS,iBACrBhI,OAAA;QAAKkI,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBnI,OAAA;UAAKoI,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAH,QAAA,EAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChE7H,OAAA;UAAAmI,QAAA,EAAK;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjC7H,OAAA;UAAKoI,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAL,QAAA,EAAC;QAEnE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAnH,QAAQ,CAAC+H,eAAe,IAAI/H,QAAQ,CAACgI,gBAAgB,iBACpD1I,OAAA;QAAKoI,KAAK,EAAE;UACVO,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,yBAAyB;UACrCC,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,gBACAnI,OAAA;UAAKoI,KAAK,EAAE;YACVC,QAAQ,EAAE,MAAM;YAChBC,YAAY,EAAE,MAAM;YACpBiB,SAAS,EAAE;UACb,CAAE;UAAApB,QAAA,EAAC;QAEH;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7H,OAAA;UAAKoI,KAAK,EAAE;YACVC,QAAQ,EAAE,MAAM;YAChBmB,UAAU,EAAE,MAAM;YAClBC,SAAS,EAAE,QAAQ;YACnBnB,YAAY,EAAE,MAAM;YACpBoB,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EAAC;QAEH;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7H,OAAA;UAAKoI,KAAK,EAAE;YACVC,QAAQ,EAAE,MAAM;YAChBoB,SAAS,EAAE,QAAQ;YACnBE,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,KAAK;YACfZ,UAAU,EAAE,oBAAoB;YAChCa,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA3B,QAAA,EACCzH,QAAQ,CAACgI;QAAgB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACG,SAAS,IAAI,CAACC,QAAQ,IAAI,CAACvH,QAAQ,CAAC+H,eAAe,iBACnDzI,OAAA,CAAAE,SAAA;QAAAiI,QAAA,EACGjB,MAAM,CAACtD,MAAM,GAAG,CAAC,gBAChB5D,OAAA,CAACF,WAAW;UACViK,GAAG,EAAE9I,YAAa;UAClB+I,gBAAgB,EAAEtJ,QAAQ,CAACuJ,aAAc;UACzCC,YAAY,EAAE,IAAK;UACnBrJ,SAAS,EAAEA,SAAS,IAAIH,QAAQ,CAACyJ,QAAS;UAC1CC,aAAa,EAAEpJ,eAAgB;UAAAmH,QAAA,EAE9BjB;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAEd7H,OAAA;UAAKkI,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnI,OAAA;YAAKoI,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,EAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChE7H,OAAA;YAAAmI,QAAA,EAAK;UAAqC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD7H,OAAA;YAAKoI,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEE,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAAL,QAAA,EAAC;UAEnE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN,gBACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGP7H,OAAA;MAAKoI,KAAK,EAAE;QACVO,QAAQ,EAAE,OAAO;QACjBI,MAAM,EAAE,MAAM;QACdD,KAAK,EAAE,MAAM;QACbE,UAAU,EAAE,oBAAoB;QAChCC,KAAK,EAAE,OAAO;QACdY,OAAO,EAAE,eAAe;QACxBC,YAAY,EAAE,MAAM;QACpBzB,QAAQ,EAAE,QAAQ;QAClBiB,MAAM,EAAE,IAAI;QACZJ,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBiB,GAAG,EAAE;MACP,CAAE;MAAAlC,QAAA,gBACAnI,OAAA;QAAAmI,QAAA,EAAM;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACf7H,OAAA;QAAAmI,QAAA,EAAM;MAAmB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGN7H,OAAA;MAAMsK,SAAS,EAAC,SAAS;MAACC,OAAO,EAAC;IAAK;MAAA7C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAACzH,EAAA,CA9WID,SAAmB;EAAA,QAiBnBV,gBAAgB,EAchBC,WAAW;AAAA;AAAA8K,EAAA,GA/BXrK,SAAmB;AAgXzB,eAAeA,SAAS;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}