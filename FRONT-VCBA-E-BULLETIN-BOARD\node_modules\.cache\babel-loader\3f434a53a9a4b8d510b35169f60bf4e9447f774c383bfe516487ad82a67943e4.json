{"ast": null, "code": "// TV Display Control Service\n// Manages TV display state, settings, and remote control functionality\n\nclass TVControlService {\n  constructor() {\n    this.settings = void 0;\n    this.status = void 0;\n    this.listeners = [];\n    this.statusListeners = [];\n    // Initialize with default settings\n    this.settings = {\n      isEnabled: true,\n      autoPlay: true,\n      slideInterval: 15000,\n      // 15 seconds\n      showAnnouncements: true,\n      showCalendarEvents: true,\n      maxAnnouncements: 10,\n      maxEvents: 5,\n      announcementCategories: [],\n      eventCategories: [],\n      displayDuration: 15000,\n      transitionType: 'slide',\n      emergencyMessage: '',\n      emergencyActive: false,\n      lastUpdated: new Date().toISOString()\n    };\n    this.status = {\n      isOnline: false,\n      currentSlide: 0,\n      totalSlides: 0,\n      isPlaying: true,\n      lastRefresh: new Date().toISOString(),\n      connectedDevices: 0,\n      uptime: '0m'\n    };\n    this.loadSettings();\n    this.initializeStatusMonitoring();\n  }\n\n  // Settings Management\n  getSettings() {\n    return {\n      ...this.settings\n    };\n  }\n  updateSettings(newSettings) {\n    this.settings = {\n      ...this.settings,\n      ...newSettings,\n      lastUpdated: new Date().toISOString()\n    };\n    this.saveSettings();\n    this.notifyListeners();\n    this.broadcastToDisplays();\n  }\n\n  // Status Management\n  getStatus() {\n    return {\n      ...this.status\n    };\n  }\n  updateStatus(newStatus) {\n    this.status = {\n      ...this.status,\n      ...newStatus\n    };\n    this.notifyStatusListeners();\n  }\n\n  // Control Commands\n  sendCommand(command) {\n    const commandWithTimestamp = {\n      ...command,\n      timestamp: new Date().toISOString()\n    };\n\n    // Store command in localStorage for TV display to pick up\n    const commands = this.getStoredCommands();\n    commands.push(commandWithTimestamp);\n    localStorage.setItem('tv_control_commands', JSON.stringify(commands));\n\n    // Update local status based on command\n    this.handleCommandLocally(commandWithTimestamp);\n  }\n\n  // Playback Controls\n  play() {\n    this.sendCommand({\n      action: 'play',\n      timestamp: new Date().toISOString()\n    });\n    this.updateStatus({\n      isPlaying: true\n    });\n  }\n  pause() {\n    this.sendCommand({\n      action: 'pause',\n      timestamp: new Date().toISOString()\n    });\n    this.updateStatus({\n      isPlaying: false\n    });\n  }\n  next() {\n    this.sendCommand({\n      action: 'next',\n      timestamp: new Date().toISOString()\n    });\n  }\n  previous() {\n    this.sendCommand({\n      action: 'previous',\n      timestamp: new Date().toISOString()\n    });\n  }\n  refresh() {\n    this.sendCommand({\n      action: 'refresh',\n      timestamp: new Date().toISOString()\n    });\n    this.updateStatus({\n      lastRefresh: new Date().toISOString()\n    });\n  }\n\n  // Emergency Broadcasting\n  broadcastEmergency(message) {\n    console.log('Broadcasting emergency:', message);\n    this.updateSettings({\n      emergencyMessage: message,\n      emergencyActive: true\n    });\n    this.sendCommand({\n      action: 'emergency',\n      payload: {\n        message\n      },\n      timestamp: new Date().toISOString()\n    });\n\n    // Multiple signals for immediate update\n    const timestamp = Date.now().toString();\n    localStorage.setItem('tv_emergency_broadcast', timestamp);\n    localStorage.setItem('tv_emergency_active', 'true');\n    localStorage.setItem('tv_emergency_message', message);\n\n    // Force storage event dispatch\n    window.dispatchEvent(new StorageEvent('storage', {\n      key: 'tv_emergency_broadcast',\n      newValue: timestamp,\n      oldValue: null\n    }));\n\n    // Additional signal\n    window.dispatchEvent(new CustomEvent('emergency-broadcast', {\n      detail: {\n        message,\n        active: true\n      }\n    }));\n    console.log('Emergency broadcast signals sent');\n  }\n  clearEmergency() {\n    this.updateSettings({\n      emergencyMessage: '',\n      emergencyActive: false\n    });\n  }\n\n  // Command Management\n  getStoredCommands() {\n    try {\n      const commands = localStorage.getItem('tv_control_commands');\n      return commands ? JSON.parse(commands) : [];\n    } catch {\n      return [];\n    }\n  }\n  clearProcessedCommands() {\n    localStorage.removeItem('tv_control_commands');\n  }\n\n  // Event Listeners\n  onSettingsChange(callback) {\n    this.listeners.push(callback);\n    return () => {\n      this.listeners = this.listeners.filter(l => l !== callback);\n    };\n  }\n  onStatusChange(callback) {\n    this.statusListeners.push(callback);\n    return () => {\n      this.statusListeners = this.statusListeners.filter(l => l !== callback);\n    };\n  }\n\n  // Private Methods\n  loadSettings() {\n    try {\n      const stored = localStorage.getItem('tv_display_settings');\n      if (stored) {\n        this.settings = {\n          ...this.settings,\n          ...JSON.parse(stored)\n        };\n      }\n    } catch (error) {\n      console.warn('Failed to load TV display settings:', error);\n    }\n  }\n  saveSettings() {\n    try {\n      localStorage.setItem('tv_display_settings', JSON.stringify(this.settings));\n    } catch (error) {\n      console.warn('Failed to save TV display settings:', error);\n    }\n  }\n  notifyListeners() {\n    this.listeners.forEach(callback => callback(this.settings));\n  }\n  notifyStatusListeners() {\n    this.statusListeners.forEach(callback => callback(this.status));\n  }\n  broadcastToDisplays() {\n    // Broadcast settings change to all connected displays\n    localStorage.setItem('tv_display_settings_updated', Date.now().toString());\n  }\n  handleCommandLocally(command) {\n    switch (command.action) {\n      case 'play':\n        this.updateStatus({\n          isPlaying: true\n        });\n        break;\n      case 'pause':\n        this.updateStatus({\n          isPlaying: false\n        });\n        break;\n      case 'refresh':\n        this.updateStatus({\n          lastRefresh: new Date().toISOString()\n        });\n        break;\n    }\n  }\n  initializeStatusMonitoring() {\n    // Check TV display status periodically\n    setInterval(() => {\n      this.checkDisplayStatus();\n    }, 5000); // Check every 5 seconds\n  }\n  checkDisplayStatus() {\n    // Check if TV display is responding\n    const lastHeartbeat = localStorage.getItem('tv_display_heartbeat');\n    const isOnline = lastHeartbeat && Date.now() - parseInt(lastHeartbeat) < 30000; // 30 seconds timeout\n\n    this.updateStatus({\n      isOnline: !!isOnline,\n      connectedDevices: isOnline ? 1 : 0\n    });\n  }\n}\n\n// Export singleton instance\nexport const tvControlService = new TVControlService();", "map": {"version": 3, "names": ["TVControlService", "constructor", "settings", "status", "listeners", "statusListeners", "isEnabled", "autoPlay", "slideInterval", "showAnnouncements", "showCalendarEvents", "maxAnnouncements", "maxEvents", "announcementCategories", "eventCategories", "displayDuration", "transitionType", "emergencyMessage", "emergencyActive", "lastUpdated", "Date", "toISOString", "isOnline", "currentSlide", "totalSlides", "isPlaying", "lastRefresh", "connectedDevices", "uptime", "loadSettings", "initializeStatusMonitoring", "getSettings", "updateSettings", "newSettings", "saveSettings", "notifyListeners", "broadcastToDisplays", "getStatus", "updateStatus", "newStatus", "notifyStatusListeners", "sendCommand", "command", "commandWithTimestamp", "timestamp", "commands", "getStoredCommands", "push", "localStorage", "setItem", "JSON", "stringify", "handleCommandLocally", "play", "action", "pause", "next", "previous", "refresh", "broadcastEmergency", "message", "console", "log", "payload", "now", "toString", "window", "dispatchEvent", "StorageEvent", "key", "newValue", "oldValue", "CustomEvent", "detail", "active", "clearEmergency", "getItem", "parse", "clearProcessedCommands", "removeItem", "onSettingsChange", "callback", "filter", "l", "onStatusChange", "stored", "error", "warn", "for<PERSON>ach", "setInterval", "checkDisplayStatus", "lastHeartbeat", "parseInt", "tvControlService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/tvControlService.ts"], "sourcesContent": ["// TV Display Control Service\n// Manages TV display state, settings, and remote control functionality\n\nexport interface TVDisplaySettings {\n  isEnabled: boolean;\n  autoPlay: boolean;\n  slideInterval: number; // in milliseconds\n  showAnnouncements: boolean;\n  showCalendarEvents: boolean;\n  maxAnnouncements: number;\n  maxEvents: number;\n  announcementCategories: number[]; // category IDs to include\n  eventCategories: number[]; // category IDs to include\n  displayDuration: number; // how long to show each slide\n  transitionType: 'fade' | 'slide' | 'none';\n  emergencyMessage?: string;\n  emergencyActive: boolean;\n  lastUpdated: string;\n}\n\nexport interface TVDisplayStatus {\n  isOnline: boolean;\n  currentSlide: number;\n  totalSlides: number;\n  isPlaying: boolean;\n  lastRefresh: string;\n  connectedDevices: number;\n  uptime: string;\n}\n\nexport interface TVControlCommand {\n  action: 'play' | 'pause' | 'next' | 'previous' | 'refresh' | 'emergency' | 'settings';\n  payload?: any;\n  timestamp: string;\n}\n\nclass TVControlService {\n  private settings: TVDisplaySettings;\n  private status: TVDisplayStatus;\n  private listeners: ((settings: TVDisplaySettings) => void)[] = [];\n  private statusListeners: ((status: TVDisplayStatus) => void)[] = [];\n\n  constructor() {\n    // Initialize with default settings\n    this.settings = {\n      isEnabled: true,\n      autoPlay: true,\n      slideInterval: 15000, // 15 seconds\n      showAnnouncements: true,\n      showCalendarEvents: true,\n      maxAnnouncements: 10,\n      maxEvents: 5,\n      announcementCategories: [],\n      eventCategories: [],\n      displayDuration: 15000,\n      transitionType: 'slide',\n      emergencyMessage: '',\n      emergencyActive: false,\n      lastUpdated: new Date().toISOString()\n    };\n\n    this.status = {\n      isOnline: false,\n      currentSlide: 0,\n      totalSlides: 0,\n      isPlaying: true,\n      lastRefresh: new Date().toISOString(),\n      connectedDevices: 0,\n      uptime: '0m'\n    };\n\n    this.loadSettings();\n    this.initializeStatusMonitoring();\n  }\n\n  // Settings Management\n  getSettings(): TVDisplaySettings {\n    return { ...this.settings };\n  }\n\n  updateSettings(newSettings: Partial<TVDisplaySettings>): void {\n    this.settings = {\n      ...this.settings,\n      ...newSettings,\n      lastUpdated: new Date().toISOString()\n    };\n    this.saveSettings();\n    this.notifyListeners();\n    this.broadcastToDisplays();\n  }\n\n  // Status Management\n  getStatus(): TVDisplayStatus {\n    return { ...this.status };\n  }\n\n  updateStatus(newStatus: Partial<TVDisplayStatus>): void {\n    this.status = {\n      ...this.status,\n      ...newStatus\n    };\n    this.notifyStatusListeners();\n  }\n\n  // Control Commands\n  sendCommand(command: TVControlCommand): void {\n    const commandWithTimestamp = {\n      ...command,\n      timestamp: new Date().toISOString()\n    };\n\n    // Store command in localStorage for TV display to pick up\n    const commands = this.getStoredCommands();\n    commands.push(commandWithTimestamp);\n    localStorage.setItem('tv_control_commands', JSON.stringify(commands));\n\n    // Update local status based on command\n    this.handleCommandLocally(commandWithTimestamp);\n  }\n\n  // Playback Controls\n  play(): void {\n    this.sendCommand({ action: 'play', timestamp: new Date().toISOString() });\n    this.updateStatus({ isPlaying: true });\n  }\n\n  pause(): void {\n    this.sendCommand({ action: 'pause', timestamp: new Date().toISOString() });\n    this.updateStatus({ isPlaying: false });\n  }\n\n  next(): void {\n    this.sendCommand({ action: 'next', timestamp: new Date().toISOString() });\n  }\n\n  previous(): void {\n    this.sendCommand({ action: 'previous', timestamp: new Date().toISOString() });\n  }\n\n  refresh(): void {\n    this.sendCommand({ action: 'refresh', timestamp: new Date().toISOString() });\n    this.updateStatus({ lastRefresh: new Date().toISOString() });\n  }\n\n  // Emergency Broadcasting\n  broadcastEmergency(message: string): void {\n    console.log('Broadcasting emergency:', message);\n\n    this.updateSettings({\n      emergencyMessage: message,\n      emergencyActive: true\n    });\n\n    this.sendCommand({\n      action: 'emergency',\n      payload: { message },\n      timestamp: new Date().toISOString()\n    });\n\n    // Multiple signals for immediate update\n    const timestamp = Date.now().toString();\n    localStorage.setItem('tv_emergency_broadcast', timestamp);\n    localStorage.setItem('tv_emergency_active', 'true');\n    localStorage.setItem('tv_emergency_message', message);\n\n    // Force storage event dispatch\n    window.dispatchEvent(new StorageEvent('storage', {\n      key: 'tv_emergency_broadcast',\n      newValue: timestamp,\n      oldValue: null\n    }));\n\n    // Additional signal\n    window.dispatchEvent(new CustomEvent('emergency-broadcast', {\n      detail: { message, active: true }\n    }));\n\n    console.log('Emergency broadcast signals sent');\n  }\n\n  clearEmergency(): void {\n    this.updateSettings({\n      emergencyMessage: '',\n      emergencyActive: false\n    });\n  }\n\n  // Command Management\n  getStoredCommands(): TVControlCommand[] {\n    try {\n      const commands = localStorage.getItem('tv_control_commands');\n      return commands ? JSON.parse(commands) : [];\n    } catch {\n      return [];\n    }\n  }\n\n  clearProcessedCommands(): void {\n    localStorage.removeItem('tv_control_commands');\n  }\n\n  // Event Listeners\n  onSettingsChange(callback: (settings: TVDisplaySettings) => void): () => void {\n    this.listeners.push(callback);\n    return () => {\n      this.listeners = this.listeners.filter(l => l !== callback);\n    };\n  }\n\n  onStatusChange(callback: (status: TVDisplayStatus) => void): () => void {\n    this.statusListeners.push(callback);\n    return () => {\n      this.statusListeners = this.statusListeners.filter(l => l !== callback);\n    };\n  }\n\n  // Private Methods\n  private loadSettings(): void {\n    try {\n      const stored = localStorage.getItem('tv_display_settings');\n      if (stored) {\n        this.settings = { ...this.settings, ...JSON.parse(stored) };\n      }\n    } catch (error) {\n      console.warn('Failed to load TV display settings:', error);\n    }\n  }\n\n  private saveSettings(): void {\n    try {\n      localStorage.setItem('tv_display_settings', JSON.stringify(this.settings));\n    } catch (error) {\n      console.warn('Failed to save TV display settings:', error);\n    }\n  }\n\n  private notifyListeners(): void {\n    this.listeners.forEach(callback => callback(this.settings));\n  }\n\n  private notifyStatusListeners(): void {\n    this.statusListeners.forEach(callback => callback(this.status));\n  }\n\n  private broadcastToDisplays(): void {\n    // Broadcast settings change to all connected displays\n    localStorage.setItem('tv_display_settings_updated', Date.now().toString());\n  }\n\n  private handleCommandLocally(command: TVControlCommand): void {\n    switch (command.action) {\n      case 'play':\n        this.updateStatus({ isPlaying: true });\n        break;\n      case 'pause':\n        this.updateStatus({ isPlaying: false });\n        break;\n      case 'refresh':\n        this.updateStatus({ lastRefresh: new Date().toISOString() });\n        break;\n    }\n  }\n\n  private initializeStatusMonitoring(): void {\n    // Check TV display status periodically\n    setInterval(() => {\n      this.checkDisplayStatus();\n    }, 5000); // Check every 5 seconds\n  }\n\n  private checkDisplayStatus(): void {\n    // Check if TV display is responding\n    const lastHeartbeat = localStorage.getItem('tv_display_heartbeat');\n    const isOnline = lastHeartbeat && \n      (Date.now() - parseInt(lastHeartbeat)) < 30000; // 30 seconds timeout\n\n    this.updateStatus({\n      isOnline: !!isOnline,\n      connectedDevices: isOnline ? 1 : 0\n    });\n  }\n}\n\n// Export singleton instance\nexport const tvControlService = new TVControlService();\n"], "mappings": "AAAA;AACA;;AAmCA,MAAMA,gBAAgB,CAAC;EAMrBC,WAAWA,CAAA,EAAG;IAAA,KALNC,QAAQ;IAAA,KACRC,MAAM;IAAA,KACNC,SAAS,GAA8C,EAAE;IAAA,KACzDC,eAAe,GAA0C,EAAE;IAGjE;IACA,IAAI,CAACH,QAAQ,GAAG;MACdI,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,KAAK;MAAE;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,kBAAkB,EAAE,IAAI;MACxBC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,CAAC;MACZC,sBAAsB,EAAE,EAAE;MAC1BC,eAAe,EAAE,EAAE;MACnBC,eAAe,EAAE,KAAK;MACtBC,cAAc,EAAE,OAAO;MACvBC,gBAAgB,EAAE,EAAE;MACpBC,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC;IAED,IAAI,CAAClB,MAAM,GAAG;MACZmB,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACrCM,gBAAgB,EAAE,CAAC;MACnBC,MAAM,EAAE;IACV,CAAC;IAED,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACnC;;EAEA;EACAC,WAAWA,CAAA,EAAsB;IAC/B,OAAO;MAAE,GAAG,IAAI,CAAC7B;IAAS,CAAC;EAC7B;EAEA8B,cAAcA,CAACC,WAAuC,EAAQ;IAC5D,IAAI,CAAC/B,QAAQ,GAAG;MACd,GAAG,IAAI,CAACA,QAAQ;MAChB,GAAG+B,WAAW;MACdd,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC;IACD,IAAI,CAACa,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACAC,SAASA,CAAA,EAAoB;IAC3B,OAAO;MAAE,GAAG,IAAI,CAAClC;IAAO,CAAC;EAC3B;EAEAmC,YAAYA,CAACC,SAAmC,EAAQ;IACtD,IAAI,CAACpC,MAAM,GAAG;MACZ,GAAG,IAAI,CAACA,MAAM;MACd,GAAGoC;IACL,CAAC;IACD,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B;;EAEA;EACAC,WAAWA,CAACC,OAAyB,EAAQ;IAC3C,MAAMC,oBAAoB,GAAG;MAC3B,GAAGD,OAAO;MACVE,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;;IAED;IACA,MAAMwB,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACzCD,QAAQ,CAACE,IAAI,CAACJ,oBAAoB,CAAC;IACnCK,YAAY,CAACC,OAAO,CAAC,qBAAqB,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAAC,CAAC;;IAErE;IACA,IAAI,CAACO,oBAAoB,CAACT,oBAAoB,CAAC;EACjD;;EAEA;EACAU,IAAIA,CAAA,EAAS;IACX,IAAI,CAACZ,WAAW,CAAC;MAAEa,MAAM,EAAE,MAAM;MAAEV,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IACzE,IAAI,CAACiB,YAAY,CAAC;MAAEb,SAAS,EAAE;IAAK,CAAC,CAAC;EACxC;EAEA8B,KAAKA,CAAA,EAAS;IACZ,IAAI,CAACd,WAAW,CAAC;MAAEa,MAAM,EAAE,OAAO;MAAEV,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IAC1E,IAAI,CAACiB,YAAY,CAAC;MAAEb,SAAS,EAAE;IAAM,CAAC,CAAC;EACzC;EAEA+B,IAAIA,CAAA,EAAS;IACX,IAAI,CAACf,WAAW,CAAC;MAAEa,MAAM,EAAE,MAAM;MAAEV,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;EAC3E;EAEAoC,QAAQA,CAAA,EAAS;IACf,IAAI,CAAChB,WAAW,CAAC;MAAEa,MAAM,EAAE,UAAU;MAAEV,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;EAC/E;EAEAqC,OAAOA,CAAA,EAAS;IACd,IAAI,CAACjB,WAAW,CAAC;MAAEa,MAAM,EAAE,SAAS;MAAEV,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;IAC5E,IAAI,CAACiB,YAAY,CAAC;MAAEZ,WAAW,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAAE,CAAC,CAAC;EAC9D;;EAEA;EACAsC,kBAAkBA,CAACC,OAAe,EAAQ;IACxCC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,OAAO,CAAC;IAE/C,IAAI,CAAC5B,cAAc,CAAC;MAClBf,gBAAgB,EAAE2C,OAAO;MACzB1C,eAAe,EAAE;IACnB,CAAC,CAAC;IAEF,IAAI,CAACuB,WAAW,CAAC;MACfa,MAAM,EAAE,WAAW;MACnBS,OAAO,EAAE;QAAEH;MAAQ,CAAC;MACpBhB,SAAS,EAAE,IAAIxB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;;IAEF;IACA,MAAMuB,SAAS,GAAGxB,IAAI,CAAC4C,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IACvCjB,YAAY,CAACC,OAAO,CAAC,wBAAwB,EAAEL,SAAS,CAAC;IACzDI,YAAY,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;IACnDD,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEW,OAAO,CAAC;;IAErD;IACAM,MAAM,CAACC,aAAa,CAAC,IAAIC,YAAY,CAAC,SAAS,EAAE;MAC/CC,GAAG,EAAE,wBAAwB;MAC7BC,QAAQ,EAAE1B,SAAS;MACnB2B,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAL,MAAM,CAACC,aAAa,CAAC,IAAIK,WAAW,CAAC,qBAAqB,EAAE;MAC1DC,MAAM,EAAE;QAAEb,OAAO;QAAEc,MAAM,EAAE;MAAK;IAClC,CAAC,CAAC,CAAC;IAEHb,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEAa,cAAcA,CAAA,EAAS;IACrB,IAAI,CAAC3C,cAAc,CAAC;MAClBf,gBAAgB,EAAE,EAAE;MACpBC,eAAe,EAAE;IACnB,CAAC,CAAC;EACJ;;EAEA;EACA4B,iBAAiBA,CAAA,EAAuB;IACtC,IAAI;MACF,MAAMD,QAAQ,GAAGG,YAAY,CAAC4B,OAAO,CAAC,qBAAqB,CAAC;MAC5D,OAAO/B,QAAQ,GAAGK,IAAI,CAAC2B,KAAK,CAAChC,QAAQ,CAAC,GAAG,EAAE;IAC7C,CAAC,CAAC,MAAM;MACN,OAAO,EAAE;IACX;EACF;EAEAiC,sBAAsBA,CAAA,EAAS;IAC7B9B,YAAY,CAAC+B,UAAU,CAAC,qBAAqB,CAAC;EAChD;;EAEA;EACAC,gBAAgBA,CAACC,QAA+C,EAAc;IAC5E,IAAI,CAAC7E,SAAS,CAAC2C,IAAI,CAACkC,QAAQ,CAAC;IAC7B,OAAO,MAAM;MACX,IAAI,CAAC7E,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8E,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKF,QAAQ,CAAC;IAC7D,CAAC;EACH;EAEAG,cAAcA,CAACH,QAA2C,EAAc;IACtE,IAAI,CAAC5E,eAAe,CAAC0C,IAAI,CAACkC,QAAQ,CAAC;IACnC,OAAO,MAAM;MACX,IAAI,CAAC5E,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6E,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKF,QAAQ,CAAC;IACzE,CAAC;EACH;;EAEA;EACQpD,YAAYA,CAAA,EAAS;IAC3B,IAAI;MACF,MAAMwD,MAAM,GAAGrC,YAAY,CAAC4B,OAAO,CAAC,qBAAqB,CAAC;MAC1D,IAAIS,MAAM,EAAE;QACV,IAAI,CAACnF,QAAQ,GAAG;UAAE,GAAG,IAAI,CAACA,QAAQ;UAAE,GAAGgD,IAAI,CAAC2B,KAAK,CAACQ,MAAM;QAAE,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdzB,OAAO,CAAC0B,IAAI,CAAC,qCAAqC,EAAED,KAAK,CAAC;IAC5D;EACF;EAEQpD,YAAYA,CAAA,EAAS;IAC3B,IAAI;MACFc,YAAY,CAACC,OAAO,CAAC,qBAAqB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjD,QAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC,OAAOoF,KAAK,EAAE;MACdzB,OAAO,CAAC0B,IAAI,CAAC,qCAAqC,EAAED,KAAK,CAAC;IAC5D;EACF;EAEQnD,eAAeA,CAAA,EAAS;IAC9B,IAAI,CAAC/B,SAAS,CAACoF,OAAO,CAACP,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAAC/E,QAAQ,CAAC,CAAC;EAC7D;EAEQsC,qBAAqBA,CAAA,EAAS;IACpC,IAAI,CAACnC,eAAe,CAACmF,OAAO,CAACP,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAAC9E,MAAM,CAAC,CAAC;EACjE;EAEQiC,mBAAmBA,CAAA,EAAS;IAClC;IACAY,YAAY,CAACC,OAAO,CAAC,6BAA6B,EAAE7B,IAAI,CAAC4C,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;EAC5E;EAEQb,oBAAoBA,CAACV,OAAyB,EAAQ;IAC5D,QAAQA,OAAO,CAACY,MAAM;MACpB,KAAK,MAAM;QACT,IAAI,CAAChB,YAAY,CAAC;UAAEb,SAAS,EAAE;QAAK,CAAC,CAAC;QACtC;MACF,KAAK,OAAO;QACV,IAAI,CAACa,YAAY,CAAC;UAAEb,SAAS,EAAE;QAAM,CAAC,CAAC;QACvC;MACF,KAAK,SAAS;QACZ,IAAI,CAACa,YAAY,CAAC;UAAEZ,WAAW,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QAAE,CAAC,CAAC;QAC5D;IACJ;EACF;EAEQS,0BAA0BA,CAAA,EAAS;IACzC;IACA2D,WAAW,CAAC,MAAM;MAChB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEQA,kBAAkBA,CAAA,EAAS;IACjC;IACA,MAAMC,aAAa,GAAG3C,YAAY,CAAC4B,OAAO,CAAC,sBAAsB,CAAC;IAClE,MAAMtD,QAAQ,GAAGqE,aAAa,IAC3BvE,IAAI,CAAC4C,GAAG,CAAC,CAAC,GAAG4B,QAAQ,CAACD,aAAa,CAAC,GAAI,KAAK,CAAC,CAAC;;IAElD,IAAI,CAACrD,YAAY,CAAC;MAChBhB,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBK,gBAAgB,EAAEL,QAAQ,GAAG,CAAC,GAAG;IACnC,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,OAAO,MAAMuE,gBAAgB,GAAG,IAAI7F,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}