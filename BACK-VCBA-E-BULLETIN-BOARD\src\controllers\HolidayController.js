const { asyncHandler } = require('../middleware/errorHandler');
const holidayService = require('../services/holidayService');
const logger = require('../utils/logger');

class HolidayController {
  // Get holidays for a specific year
  getHolidays = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear(), category, country_code } = req.query;
    
    const filters = {};
    if (category) filters.category = category;
    if (country_code) filters.country_code = country_code;

    const holidays = await holidayService.getHolidaysFromDatabase(parseInt(year), filters);

    res.status(200).json({
      success: true,
      message: 'Holidays retrieved successfully',
      data: {
        holidays,
        year: parseInt(year),
        total: holidays.length
      }
    });
  });

  // Get Philippine holidays for a specific year
  getPhilippineHolidays = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;
    
    const holidays = await holidayService.getPhilippineHolidays(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'Philippine holidays retrieved successfully',
      data: {
        holidays,
        year: parseInt(year),
        total: holidays.length
      }
    });
  });

  // Get international holidays for a specific year
  getInternationalHolidays = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;
    
    const holidays = await holidayService.getInternationalHolidays(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'International holidays retrieved successfully',
      data: {
        holidays,
        year: parseInt(year),
        total: holidays.length
      }
    });
  });

  // Get religious holidays for a specific year
  getReligiousHolidays = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;
    
    const holidays = await holidayService.getReligiousHolidays(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'Religious holidays retrieved successfully',
      data: {
        holidays,
        year: parseInt(year),
        total: holidays.length
      }
    });
  });

  // Get all holidays from API sources (not database)
  getAllHolidaysFromAPI = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;
    
    const holidays = await holidayService.getAllHolidays(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'All holidays retrieved from API sources',
      data: {
        holidays,
        year: parseInt(year),
        total: holidays.length
      }
    });
  });

  // Sync holidays to database (admin only)
  syncHolidays = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear(), force = false } = req.body;
    const adminId = req.user.id;

    // If force is true, delete existing auto-generated holidays first
    if (force) {
      await holidayService.deleteAutoGeneratedHolidays(parseInt(year));
    }

    const syncResults = await holidayService.syncHolidaysToDatabase(parseInt(year), adminId);

    res.status(200).json({
      success: true,
      message: 'Holidays synced successfully',
      data: {
        year: parseInt(year),
        results: syncResults
      }
    });
  });

  // Sync holidays for multiple years (admin only)
  syncMultipleYears = asyncHandler(async (req, res) => {
    const { years, force = false } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(years) || years.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Years array is required and must not be empty'
      });
    }

    const results = {};

    for (const year of years) {
      try {
        // If force is true, delete existing auto-generated holidays first
        if (force) {
          await holidayService.deleteAutoGeneratedHolidays(parseInt(year));
        }

        const syncResults = await holidayService.syncHolidaysToDatabase(parseInt(year), adminId);
        results[year] = syncResults;
      } catch (error) {
        logger.error(`Error syncing holidays for year ${year}:`, error);
        results[year] = {
          error: error.message,
          created: 0,
          updated: 0,
          skipped: 0
        };
      }
    }

    res.status(200).json({
      success: true,
      message: 'Multi-year holiday sync completed',
      data: {
        years,
        results
      }
    });
  });

  // Delete auto-generated holidays for a specific year (admin only)
  deleteAutoGeneratedHolidays = asyncHandler(async (req, res) => {
    const { year } = req.params;
    
    const deletedCount = await holidayService.deleteAutoGeneratedHolidays(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'Auto-generated holidays deleted successfully',
      data: {
        year: parseInt(year),
        deletedCount
      }
    });
  });

  // Get holiday statistics
  getHolidayStats = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;
    
    try {
      const allHolidays = await holidayService.getHolidaysFromDatabase(parseInt(year));
      
      const stats = {
        total: allHolidays.length,
        byCategory: {},
        byCountry: {},
        byType: {},
        autoGenerated: 0,
        manual: 0
      };

      allHolidays.forEach(holiday => {
        // By category
        const category = holiday.holiday_type || 'unknown';
        stats.byCategory[category] = (stats.byCategory[category] || 0) + 1;

        // By country
        const country = holiday.country_code || 'unknown';
        stats.byCountry[country] = (stats.byCountry[country] || 0) + 1;

        // By type (auto-generated vs manual)
        if (holiday.is_auto_generated) {
          stats.autoGenerated++;
        } else {
          stats.manual++;
        }

        // By holiday types (from JSON field)
        try {
          const types = JSON.parse(holiday.holiday_types || '[]');
          types.forEach(type => {
            stats.byType[type] = (stats.byType[type] || 0) + 1;
          });
        } catch (e) {
          // Ignore JSON parse errors
        }
      });

      res.status(200).json({
        success: true,
        message: 'Holiday statistics retrieved successfully',
        data: {
          year: parseInt(year),
          stats
        }
      });
    } catch (error) {
      logger.error('Error getting holiday statistics:', error);
      throw error;
    }
  });

  // Preview holidays before syncing (admin only)
  previewHolidays = asyncHandler(async (req, res) => {
    const { year = new Date().getFullYear() } = req.query;
    
    const holidays = await holidayService.getAllHolidays(parseInt(year));
    
    // Get existing holidays from database for comparison
    const existingHolidays = await holidayService.getHolidaysFromDatabase(parseInt(year));
    const existingHolidayMap = new Map();
    
    existingHolidays.forEach(holiday => {
      const key = `${holiday.event_date}-${holiday.title}`;
      existingHolidayMap.set(key, holiday);
    });

    // Categorize holidays as new, existing, or to be updated
    const preview = {
      new: [],
      existing: [],
      toUpdate: []
    };

    holidays.forEach(holiday => {
      const key = `${holiday.date}-${holiday.name}`;
      const existing = existingHolidayMap.get(key);
      
      if (!existing) {
        preview.new.push(holiday);
      } else if (existing.is_auto_generated) {
        preview.toUpdate.push({
          ...holiday,
          existingId: existing.calendar_id
        });
      } else {
        preview.existing.push({
          ...holiday,
          existingId: existing.calendar_id,
          isManual: true
        });
      }
    });

    res.status(200).json({
      success: true,
      message: 'Holiday preview generated successfully',
      data: {
        year: parseInt(year),
        preview,
        summary: {
          total: holidays.length,
          new: preview.new.length,
          existing: preview.existing.length,
          toUpdate: preview.toUpdate.length
        }
      }
    });
  });
}

module.exports = new HolidayController();
