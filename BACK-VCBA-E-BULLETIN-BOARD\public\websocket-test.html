<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Zaira E-Bulletin Board</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #6a0dad;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #5a0b9a;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Test - Zaira E-Bulletin Board</h1>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>
        
        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        
        <div>
            <h3>Test Messages</h3>
            <input type="text" id="messageInput" placeholder="Enter test message" />
            <button onclick="sendTestNotification()">Send Test Notification</button>
            <br>
            <input type="text" id="announcementTitle" placeholder="Announcement title" />
            <input type="text" id="announcementContent" placeholder="Announcement content" />
            <button onclick="sendTestAnnouncement()">Send Test Announcement</button>
        </div>
        
        <div>
            <h3>Connection Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="/ws/socket.io.js"></script>
    <script>
        let socket = null;
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function connect() {
            try {
                socket = io('http://localhost:3000', {
                    path: '/ws'
                });

                socket.on('connect', () => {
                    log('✅ Connected to WebSocket server');
                    updateStatus(true);
                    
                    // Join user room for testing
                    socket.emit('join-user-room', 'test-user-123');
                    log('📝 Joined user room: test-user-123');
                });

                socket.on('disconnect', () => {
                    log('❌ Disconnected from WebSocket server');
                    updateStatus(false);
                });

                socket.on('connect_error', (error) => {
                    log(`🚫 Connection error: ${error.message}`);
                    updateStatus(false);
                });

                // Listen for announcements
                socket.on('announcement-created', (data) => {
                    log(`📢 New announcement: ${data.title} - ${data.content}`);
                });

                socket.on('announcement-updated', (data) => {
                    log(`📝 Updated announcement: ${data.title}`);
                });

                socket.on('announcement-deleted', (data) => {
                    log(`🗑️ Deleted announcement ID: ${data.id}`);
                });

                // Listen for comments
                socket.on('comment-added', (data) => {
                    log(`💬 New comment on announcement ${data.announcementId}: ${data.content}`);
                });

                // Listen for notifications
                socket.on('notification', (data) => {
                    log(`🔔 Notification: ${data.title} - ${data.message}`);
                });

                socket.on('admin-notification', (data) => {
                    log(`👑 Admin notification: ${data.title} - ${data.message}`);
                });

                // Listen for system status
                socket.on('system-status', (data) => {
                    log(`⚙️ System status: ${data.status} - ${data.message}`);
                });

            } catch (error) {
                log(`🚫 Error connecting: ${error.message}`);
            }
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateStatus(false);
                log('🔌 Manually disconnected');
            }
        }

        function sendTestNotification() {
            const message = document.getElementById('messageInput').value;
            if (!message) {
                alert('Please enter a message');
                return;
            }

            fetch('http://localhost:3000/api/websocket/test-broadcast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message, type: 'test' })
            })
            .then(response => response.json())
            .then(data => {
                log(`📤 Sent test notification: ${message}`);
                document.getElementById('messageInput').value = '';
            })
            .catch(error => {
                log(`🚫 Error sending notification: ${error.message}`);
            });
        }

        function sendTestAnnouncement() {
            const title = document.getElementById('announcementTitle').value;
            const content = document.getElementById('announcementContent').value;
            
            if (!title || !content) {
                alert('Please enter both title and content');
                return;
            }

            fetch('http://localhost:3000/api/websocket/test-announcement', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ title, content, author: 'Test Admin' })
            })
            .then(response => response.json())
            .then(data => {
                log(`📤 Sent test announcement: ${title}`);
                document.getElementById('announcementTitle').value = '';
                document.getElementById('announcementContent').value = '';
            })
            .catch(error => {
                log(`🚫 Error sending announcement: ${error.message}`);
            });
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        // Initialize
        updateStatus(false);
        log('🚀 WebSocket test page loaded');
    </script>
</body>
</html>
