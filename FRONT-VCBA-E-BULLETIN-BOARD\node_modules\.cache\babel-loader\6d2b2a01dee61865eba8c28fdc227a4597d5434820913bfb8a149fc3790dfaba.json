{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\";\nimport React from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Truncate content if too long for TV display\n  const truncateContent = (content, maxLength = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `tv-announcement ${isUrgent ? 'urgent' : ''}`,\n    children: [isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#e74c3c',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '10px',\n        marginBottom: '2rem',\n        fontSize: '2rem',\n        fontWeight: 'bold',\n        textAlign: 'center',\n        textTransform: 'uppercase',\n        letterSpacing: '1px'\n      },\n      children: announcement.is_alert ? '🚨 IMPORTANT ALERT' : '📌 PINNED ANNOUNCEMENT'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"tv-announcement-title\",\n      children: announcement.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1rem',\n        maxHeight: '400px'\n      },\n      children: [images.slice(0, 3).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '12px',\n          overflow: 'hidden',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: images.length === 1 ? '300px' : '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 13\n      }, this)), images.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: 'rgba(0, 0, 0, 0.1)',\n          borderRadius: '12px',\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          color: '#6c757d'\n        },\n        children: [\"+\", images.length - 3, \" more\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-announcement-content\",\n      children: truncateContent(announcement.content)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-announcement-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tv-announcement-category\",\n          style: {\n            backgroundColor: getCategoryColor()\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.6rem',\n              fontWeight: '600'\n            },\n            children: [\"\\uD83D\\uDCC5 \", formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.4rem',\n              opacity: 0.8\n            },\n            children: [\"\\uD83D\\uDD52 \", formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Posted by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: announcement.author_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "getImageUrl", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "truncate<PERSON><PERSON><PERSON>", "content", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_path", "imageUrl", "push", "url", "alt", "title", "image_url", "attachments", "for<PERSON>ach", "attachment", "index", "file_path", "match", "className", "children", "style", "background", "color", "padding", "borderRadius", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gridTemplateColumns", "gap", "maxHeight", "slice", "map", "image", "overflow", "boxShadow", "src", "width", "height", "objectFit", "onError", "e", "currentTarget", "alignItems", "justifyContent", "category_name", "backgroundColor", "flexDirection", "created_at", "opacity", "author_name", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Truncate content if too long for TV display\n  const truncateContent = (content: string, maxLength: number = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  return (\n    <div className={`tv-announcement ${isUrgent ? 'urgent' : ''}`}>\n      {/* Alert indicator for urgent announcements */}\n      {isUrgent && (\n        <div style={{\n          background: '#e74c3c',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '10px',\n          marginBottom: '2rem',\n          fontSize: '2rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '1px'\n        }}>\n          {announcement.is_alert ? '🚨 IMPORTANT ALERT' : '📌 PINNED ANNOUNCEMENT'}\n        </div>\n      )}\n\n      {/* Announcement title */}\n      <h2 className=\"tv-announcement-title\">\n        {announcement.title}\n      </h2>\n\n      {/* Announcement images */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem',\n          maxHeight: '400px'\n        }}>\n          {images.slice(0, 3).map((image, index) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '12px',\n                overflow: 'hidden',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: images.length === 1 ? '300px' : '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n          {images.length > 3 && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: 'rgba(0, 0, 0, 0.1)',\n              borderRadius: '12px',\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: '#6c757d'\n            }}>\n              +{images.length - 3} more\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Announcement content */}\n      <div className=\"tv-announcement-content\">\n        {truncateContent(announcement.content)}\n      </div>\n\n      {/* Announcement metadata */}\n      <div className=\"tv-announcement-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <span \n              className=\"tv-announcement-category\"\n              style={{ backgroundColor: getCategoryColor() }}\n            >\n              {announcement.category_name}\n            </span>\n          )}\n\n          {/* Date and time */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n            <span style={{ fontSize: '1.6rem', fontWeight: '600' }}>\n              📅 {formatDate(announcement.created_at)}\n            </span>\n            <span style={{ fontSize: '1.4rem', opacity: 0.8 }}>\n              🕒 {formatTime(announcement.created_at)}\n            </span>\n          </div>\n        </div>\n\n        {/* Author information */}\n        {announcement.author_name && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Posted by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {announcement.author_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{announcement.reaction_count} reactions</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{announcement.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAC1E;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,OAAe,EAAEC,SAAiB,GAAG,GAAG,KAAK;IACpE,IAAID,OAAO,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,OAAO;IAC/C,OAAOA,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EACvD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,YAAY,CAACqB,cAAc,EAAE;MAC/B,OAAOrB,YAAY,CAACqB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGtB,YAAY,CAACuB,QAAQ,IAAIvB,YAAY,CAACwB,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI1B,YAAY,CAAC2B,UAAU,EAAE;MAC3B,MAAMC,QAAQ,GAAGhC,WAAW,CAACI,YAAY,CAAC2B,UAAU,CAAC;MACrD,IAAIC,QAAQ,EAAE;QACZF,MAAM,CAACG,IAAI,CAAC;UACVC,GAAG,EAAEF,QAAQ;UACbG,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIhC,YAAY,CAACiC,SAAS,IAAI,CAACjC,YAAY,CAAC2B,UAAU,EAAE;MACtDD,MAAM,CAACG,IAAI,CAAC;QACVC,GAAG,EAAE9B,YAAY,CAACiC,SAAS;QAC3BF,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIhC,YAAY,CAACkC,WAAW,EAAE;MAC5BlC,YAAY,CAACkC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,SAAS,IAAIF,UAAU,CAACE,SAAS,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACrF,MAAMX,QAAQ,GAAGhC,WAAW,CAACwC,UAAU,CAACE,SAAS,CAAC;UAClD,IAAIV,QAAQ,EAAE;YACZF,MAAM,CAACG,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK,YAAYK,KAAK,GAAG,CAAC;YACjD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;EAEtC,oBACE3B,OAAA;IAAK0C,SAAS,EAAE,mBAAmBlB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IAAAmB,QAAA,GAE3DnB,QAAQ,iBACPxB,OAAA;MAAK4C,KAAK,EAAE;QACVC,UAAU,EAAE,SAAS;QACrBC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,WAAW;QACpBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,QAAQ;QACnBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE;MACjB,CAAE;MAAAX,QAAA,EACCzC,YAAY,CAACuB,QAAQ,GAAG,oBAAoB,GAAG;IAAwB;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACN,eAGD1D,OAAA;MAAI0C,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAClCzC,YAAY,CAACgC;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAGJ9B,MAAM,CAACT,MAAM,GAAG,CAAC,iBAChBnB,OAAA;MAAK4C,KAAK,EAAE;QACVK,YAAY,EAAE,MAAM;QACpBU,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAEhC,MAAM,CAACT,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzF0C,GAAG,EAAE,MAAM;QACXC,SAAS,EAAE;MACb,CAAE;MAAAnB,QAAA,GACCf,MAAM,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAE1B,KAAK,kBACnCvC,OAAA;QAEE4C,KAAK,EAAE;UACLI,YAAY,EAAE,MAAM;UACpBkB,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE;QACb,CAAE;QAAAxB,QAAA,eAEF3C,OAAA;UACEoE,GAAG,EAAEH,KAAK,CAACjC,GAAI;UACfC,GAAG,EAAEgC,KAAK,CAAChC,GAAI;UACfW,KAAK,EAAE;YACLyB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE1C,MAAM,CAACT,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,OAAO;YAC/CoD,SAAS,EAAE,OAAO;YAClBZ,OAAO,EAAE;UACX,CAAE;UACFa,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACe,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnBGnB,KAAK;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN,CAAC,EACD9B,MAAM,CAACT,MAAM,GAAG,CAAC,iBAChBnB,OAAA;QAAK4C,KAAK,EAAE;UACVe,OAAO,EAAE,MAAM;UACfgB,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxB/B,UAAU,EAAE,oBAAoB;UAChCG,YAAY,EAAE,MAAM;UACpBE,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBL,KAAK,EAAE;QACT,CAAE;QAAAH,QAAA,GAAC,GACA,EAACf,MAAM,CAACT,MAAM,GAAG,CAAC,EAAC,OACtB;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD1D,OAAA;MAAK0C,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EACrC3B,eAAe,CAACd,YAAY,CAACe,OAAO;IAAC;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAGN1D,OAAA;MAAK0C,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC3C,OAAA;QAAK4C,KAAK,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEgB,UAAU,EAAE,QAAQ;UAAEd,GAAG,EAAE;QAAO,CAAE;QAAAlB,QAAA,GAEhEzC,YAAY,CAAC2E,aAAa,iBACzB7E,OAAA;UACE0C,SAAS,EAAC,0BAA0B;UACpCE,KAAK,EAAE;YAAEkC,eAAe,EAAExD,gBAAgB,CAAC;UAAE,CAAE;UAAAqB,QAAA,EAE9CzC,YAAY,CAAC2E;QAAa;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACP,eAGD1D,OAAA;UAAK4C,KAAK,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEoB,aAAa,EAAE,QAAQ;YAAElB,GAAG,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBACtE3C,OAAA;YAAM4C,KAAK,EAAE;cAAEM,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAR,QAAA,GAAC,eACnD,EAACxC,UAAU,CAACD,YAAY,CAAC8E,UAAU,CAAC;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACP1D,OAAA;YAAM4C,KAAK,EAAE;cAAEM,QAAQ,EAAE,QAAQ;cAAE+B,OAAO,EAAE;YAAI,CAAE;YAAAtC,QAAA,GAAC,eAC9C,EAAC/B,UAAU,CAACV,YAAY,CAAC8E,UAAU,CAAC;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxD,YAAY,CAACgF,WAAW,iBACvBlF,OAAA;QAAK4C,KAAK,EAAE;UACVe,OAAO,EAAE,MAAM;UACfgB,UAAU,EAAE,QAAQ;UACpBd,GAAG,EAAE,MAAM;UACXX,QAAQ,EAAE;QACZ,CAAE;QAAAP,QAAA,gBACA3C,OAAA;UAAM4C,KAAK,EAAE;YAAEqC,OAAO,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAAU;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChD1D,OAAA;UAAM4C,KAAK,EAAE;YAAEO,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAChCzC,YAAY,CAACgF;QAAW;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACxD,YAAY,CAACiF,cAAc,IAAIjF,YAAY,CAACkF,aAAa,kBACzDpF,OAAA;MAAK4C,KAAK,EAAE;QACVyC,SAAS,EAAE,MAAM;QACjBtC,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCG,YAAY,EAAE,MAAM;QACpBW,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,MAAM;QACXX,QAAQ,EAAE;MACZ,CAAE;MAAAP,QAAA,GACCzC,YAAY,CAACiF,cAAc,IAAIjF,YAAY,CAACiF,cAAc,GAAG,CAAC,iBAC7DnF,OAAA;QAAK4C,KAAK,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEgB,UAAU,EAAE,QAAQ;UAAEd,GAAG,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBACnE3C,OAAA;UAAA2C,QAAA,EAAM;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf1D,OAAA;UAAA2C,QAAA,GAAOzC,YAAY,CAACiF,cAAc,EAAC,YAAU;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACN,EACAxD,YAAY,CAACkF,aAAa,IAAIlF,YAAY,CAACkF,aAAa,GAAG,CAAC,iBAC3DpF,OAAA;QAAK4C,KAAK,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEgB,UAAU,EAAE,QAAQ;UAAEd,GAAG,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBACnE3C,OAAA;UAAA2C,QAAA,EAAM;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf1D,OAAA;UAAA2C,QAAA,GAAOzC,YAAY,CAACkF,aAAa,EAAC,WAAS;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC4B,EAAA,GApOIrF,cAA6C;AAsOnD,eAAeA,cAAc;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}