{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\tv-control\\\\TVContentManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { tvControlService } from '../../../services/tvControlService';\nimport { useAnnouncements } from '../../../hooks/useAnnouncements';\nimport { useCalendar } from '../../../hooks/useCalendar';\nimport { Eye, EyeOff, Filter, RefreshCw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVContentManager = ({\n  settings\n}) => {\n  _s();\n  const [selectedCategories, setSelectedCategories] = useState(settings.announcementCategories);\n  const [selectedEventCategories, setSelectedEventCategories] = useState(settings.eventCategories);\n\n  // Fetch current content\n  const {\n    announcements,\n    loading: announcementsLoading,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 20,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, true);\n  const {\n    events,\n    loading: eventsLoading,\n    refresh: refreshEvents\n  } = useCalendar(new Date());\n  const handleCategoryToggle = (categoryId, isEvent = false) => {\n    if (isEvent) {\n      const newCategories = selectedEventCategories.includes(categoryId) ? selectedEventCategories.filter(id => id !== categoryId) : [...selectedEventCategories, categoryId];\n      setSelectedEventCategories(newCategories);\n      tvControlService.updateSettings({\n        eventCategories: newCategories\n      });\n    } else {\n      const newCategories = selectedCategories.includes(categoryId) ? selectedCategories.filter(id => id !== categoryId) : [...selectedCategories, categoryId];\n      setSelectedCategories(newCategories);\n      tvControlService.updateSettings({\n        announcementCategories: newCategories\n      });\n    }\n  };\n  const handleRefreshAll = () => {\n    refreshAnnouncements();\n    refreshEvents();\n    tvControlService.refresh();\n  };\n\n  // Get unique categories from announcements\n  const announcementCategories = Array.from(new Set(announcements.map(a => ({\n    id: a.category_id,\n    name: a.category_name,\n    color: a.category_color\n  })))).filter(cat => cat.name);\n\n  // Get unique categories from events\n  const eventCategories = Array.from(new Set(events.map(e => ({\n    id: e.category_id,\n    name: e.category_name,\n    color: e.category_color\n  })))).filter(cat => cat.name && cat.id);\n\n  // Filter content based on settings\n  const filteredAnnouncements = announcements.filter(announcement => {\n    if (!settings.showAnnouncements) return false;\n    if (selectedCategories.length === 0) return true;\n    return selectedCategories.includes(announcement.category_id);\n  }).slice(0, settings.maxAnnouncements);\n  const filteredEvents = events.filter(event => {\n    if (!settings.showCalendarEvents) return false;\n    if (selectedEventCategories.length === 0) return true;\n    return event.category_id && selectedEventCategories.includes(event.category_id);\n  }).slice(0, settings.maxEvents);\n  const cardStyle = {\n    background: 'white',\n    border: '1px solid #e9ecef',\n    borderRadius: '8px',\n    padding: '1rem',\n    marginBottom: '1rem'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          margin: 0,\n          color: '#2c3e50'\n        },\n        children: \"Content Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRefreshAll,\n        style: {\n          background: '#17a2b8',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem 1.5rem',\n          borderRadius: '6px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), \"Refresh Content\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f8f9fa',\n        borderRadius: '8px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        border: '1px solid #e9ecef'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 1rem 0',\n          color: '#2c3e50'\n        },\n        children: \"Currently Displayed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#3498db'\n            },\n            children: filteredAnnouncements.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6c757d'\n            },\n            children: \"Announcements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#e74c3c'\n            },\n            children: filteredEvents.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6c757d'\n            },\n            children: \"Calendar Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              fontWeight: 'bold',\n              color: '#28a745'\n            },\n            children: filteredAnnouncements.length + filteredEvents.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6c757d'\n            },\n            children: \"Total Slides\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: '1fr 1fr',\n        gap: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            fontSize: '1.4rem',\n            fontWeight: '600',\n            margin: '0 0 1rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), \"Announcement Categories\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), announcementCategories.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...cardStyle,\n            textAlign: 'center',\n            color: '#6c757d'\n          },\n          children: \"No announcement categories found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this) : announcementCategories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: cardStyle,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '16px',\n                  height: '16px',\n                  borderRadius: '50%',\n                  background: category.color || '#3498db'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryToggle(category.id),\n              style: {\n                background: selectedCategories.includes(category.id) ? '#28a745' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                padding: '0.5rem',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: selectedCategories.includes(category.id) ? /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 65\n              }, this) : /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 85\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 17\n          }, this)\n        }, category.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            fontSize: '1.4rem',\n            fontWeight: '600',\n            margin: '0 0 1rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), \"Event Categories\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), eventCategories.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...cardStyle,\n            textAlign: 'center',\n            color: '#6c757d'\n          },\n          children: \"No event categories found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this) : eventCategories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: cardStyle,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '16px',\n                  height: '16px',\n                  borderRadius: '50%',\n                  background: category.color || '#e74c3c'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: category.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCategoryToggle(category.id, true),\n              style: {\n                background: selectedEventCategories.includes(category.id) ? '#28a745' : '#6c757d',\n                color: 'white',\n                border: 'none',\n                padding: '0.5rem',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: selectedEventCategories.includes(category.id) ? /*#__PURE__*/_jsxDEV(Eye, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 71\n              }, this) : /*#__PURE__*/_jsxDEV(EyeOff, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 91\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 17\n          }, this)\n        }, category.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        background: '#e8f5e8',\n        border: '1px solid #c3e6c3',\n        borderRadius: '8px',\n        padding: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 1rem 0',\n          color: '#155724'\n        },\n        children: \"\\uD83D\\uDCA1 Content Filtering\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          margin: 0,\n          paddingLeft: '1.5rem',\n          color: '#155724'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Toggle categories on/off to control what appears on the TV display\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"When no categories are selected, all content is shown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Changes apply immediately to the live display\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Use this to focus on specific types of announcements or events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(TVContentManager, \"6jDrAvle0oncc+puia+y1OXZwtM=\", false, function () {\n  return [useAnnouncements, useCalendar];\n});\n_c = TVContentManager;\nexport default TVContentManager;\nvar _c;\n$RefreshReg$(_c, \"TVContentManager\");", "map": {"version": 3, "names": ["React", "useState", "tvControlService", "useAnnouncements", "useCalendar", "Eye", "Eye<PERSON>ff", "Filter", "RefreshCw", "jsxDEV", "_jsxDEV", "TVContentManager", "settings", "_s", "selectedCategories", "setSelectedCategories", "announcementCategories", "selectedEventCategories", "setSelectedEventCategories", "eventCategories", "announcements", "loading", "announcementsLoading", "refresh", "refreshAnnouncements", "status", "page", "limit", "sort_by", "sort_order", "events", "eventsLoading", "refreshEvents", "Date", "handleCategoryToggle", "categoryId", "isEvent", "newCategories", "includes", "filter", "id", "updateSettings", "handleRefreshAll", "Array", "from", "Set", "map", "a", "category_id", "name", "category_name", "color", "category_color", "cat", "e", "filteredAnnouncements", "announcement", "showAnnouncements", "length", "slice", "maxAnnouncements", "filteredEvents", "event", "showCalendarEvents", "maxEvents", "cardStyle", "background", "border", "borderRadius", "padding", "marginBottom", "children", "style", "display", "justifyContent", "alignItems", "fontSize", "fontWeight", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "cursor", "gap", "size", "gridTemplateColumns", "textAlign", "category", "width", "height", "marginTop", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/tv-control/TVContentManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { tvControlService, TVDisplaySettings } from '../../../services/tvControlService';\nimport { useAnnouncements } from '../../../hooks/useAnnouncements';\nimport { useCalendar } from '../../../hooks/useCalendar';\nimport { Eye, EyeOff, Filter, RefreshCw } from 'lucide-react';\n\ninterface TVContentManagerProps {\n  settings: TVDisplaySettings;\n}\n\nconst TVContentManager: React.FC<TVContentManagerProps> = ({ settings }) => {\n  const [selectedCategories, setSelectedCategories] = useState<number[]>(settings.announcementCategories);\n  const [selectedEventCategories, setSelectedEventCategories] = useState<number[]>(settings.eventCategories);\n\n  // Fetch current content\n  const {\n    announcements,\n    loading: announcementsLoading,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: 20,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, true);\n\n  const {\n    events,\n    loading: eventsLoading,\n    refresh: refreshEvents\n  } = useCalendar(new Date());\n\n  const handleCategoryToggle = (categoryId: number, isEvent: boolean = false) => {\n    if (isEvent) {\n      const newCategories = selectedEventCategories.includes(categoryId)\n        ? selectedEventCategories.filter(id => id !== categoryId)\n        : [...selectedEventCategories, categoryId];\n      \n      setSelectedEventCategories(newCategories);\n      tvControlService.updateSettings({\n        eventCategories: newCategories\n      });\n    } else {\n      const newCategories = selectedCategories.includes(categoryId)\n        ? selectedCategories.filter(id => id !== categoryId)\n        : [...selectedCategories, categoryId];\n      \n      setSelectedCategories(newCategories);\n      tvControlService.updateSettings({\n        announcementCategories: newCategories\n      });\n    }\n  };\n\n  const handleRefreshAll = () => {\n    refreshAnnouncements();\n    refreshEvents();\n    tvControlService.refresh();\n  };\n\n  // Get unique categories from announcements\n  const announcementCategories = Array.from(\n    new Set(announcements.map(a => ({ id: a.category_id, name: a.category_name, color: a.category_color })))\n  ).filter(cat => cat.name);\n\n  // Get unique categories from events\n  const eventCategories = Array.from(\n    new Set(events.map(e => ({ id: e.category_id, name: e.category_name, color: e.category_color })))\n  ).filter(cat => cat.name && cat.id);\n\n  // Filter content based on settings\n  const filteredAnnouncements = announcements.filter(announcement => {\n    if (!settings.showAnnouncements) return false;\n    if (selectedCategories.length === 0) return true;\n    return selectedCategories.includes(announcement.category_id);\n  }).slice(0, settings.maxAnnouncements);\n\n  const filteredEvents = events.filter(event => {\n    if (!settings.showCalendarEvents) return false;\n    if (selectedEventCategories.length === 0) return true;\n    return event.category_id && selectedEventCategories.includes(event.category_id);\n  }).slice(0, settings.maxEvents);\n\n  const cardStyle = {\n    background: 'white',\n    border: '1px solid #e9ecef',\n    borderRadius: '8px',\n    padding: '1rem',\n    marginBottom: '1rem'\n  };\n\n  return (\n    <div>\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <h2 style={{\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          margin: 0,\n          color: '#2c3e50'\n        }}>\n          Content Management\n        </h2>\n\n        <button\n          onClick={handleRefreshAll}\n          style={{\n            background: '#17a2b8',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem 1.5rem',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}\n        >\n          <RefreshCw size={16} />\n          Refresh Content\n        </button>\n      </div>\n\n      {/* Content Summary */}\n      <div style={{\n        background: '#f8f9fa',\n        borderRadius: '8px',\n        padding: '1.5rem',\n        marginBottom: '2rem',\n        border: '1px solid #e9ecef'\n      }}>\n        <h3 style={{ margin: '0 0 1rem 0', color: '#2c3e50' }}>Currently Displayed</h3>\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '1rem'\n        }}>\n          <div style={{\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            textAlign: 'center'\n          }}>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>\n              {filteredAnnouncements.length}\n            </div>\n            <div style={{ color: '#6c757d' }}>Announcements</div>\n          </div>\n          <div style={{\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            textAlign: 'center'\n          }}>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#e74c3c' }}>\n              {filteredEvents.length}\n            </div>\n            <div style={{ color: '#6c757d' }}>Calendar Events</div>\n          </div>\n          <div style={{\n            background: 'white',\n            padding: '1rem',\n            borderRadius: '6px',\n            textAlign: 'center'\n          }}>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#28a745' }}>\n              {filteredAnnouncements.length + filteredEvents.length}\n            </div>\n            <div style={{ color: '#6c757d' }}>Total Slides</div>\n          </div>\n        </div>\n      </div>\n\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: '1fr 1fr',\n        gap: '2rem'\n      }}>\n        {/* Announcement Categories */}\n        <div>\n          <h3 style={{\n            fontSize: '1.4rem',\n            fontWeight: '600',\n            margin: '0 0 1rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}>\n            <Filter size={20} />\n            Announcement Categories\n          </h3>\n\n          {announcementCategories.length === 0 ? (\n            <div style={{\n              ...cardStyle,\n              textAlign: 'center',\n              color: '#6c757d'\n            }}>\n              No announcement categories found\n            </div>\n          ) : (\n            announcementCategories.map((category) => (\n              <div key={category.id} style={cardStyle}>\n                <div style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                }}>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <div\n                      style={{\n                        width: '16px',\n                        height: '16px',\n                        borderRadius: '50%',\n                        background: category.color || '#3498db'\n                      }}\n                    />\n                    <span style={{ fontWeight: '500' }}>{category.name}</span>\n                  </div>\n                  \n                  <button\n                    onClick={() => handleCategoryToggle(category.id)}\n                    style={{\n                      background: selectedCategories.includes(category.id) ? '#28a745' : '#6c757d',\n                      color: 'white',\n                      border: 'none',\n                      padding: '0.5rem',\n                      borderRadius: '4px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center'\n                    }}\n                  >\n                    {selectedCategories.includes(category.id) ? <Eye size={16} /> : <EyeOff size={16} />}\n                  </button>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n\n        {/* Event Categories */}\n        <div>\n          <h3 style={{\n            fontSize: '1.4rem',\n            fontWeight: '600',\n            margin: '0 0 1rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}>\n            <Filter size={20} />\n            Event Categories\n          </h3>\n\n          {eventCategories.length === 0 ? (\n            <div style={{\n              ...cardStyle,\n              textAlign: 'center',\n              color: '#6c757d'\n            }}>\n              No event categories found\n            </div>\n          ) : (\n            eventCategories.map((category) => (\n              <div key={category.id} style={cardStyle}>\n                <div style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                }}>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <div\n                      style={{\n                        width: '16px',\n                        height: '16px',\n                        borderRadius: '50%',\n                        background: category.color || '#e74c3c'\n                      }}\n                    />\n                    <span style={{ fontWeight: '500' }}>{category.name}</span>\n                  </div>\n                  \n                  <button\n                    onClick={() => handleCategoryToggle(category.id!, true)}\n                    style={{\n                      background: selectedEventCategories.includes(category.id!) ? '#28a745' : '#6c757d',\n                      color: 'white',\n                      border: 'none',\n                      padding: '0.5rem',\n                      borderRadius: '4px',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center'\n                    }}\n                  >\n                    {selectedEventCategories.includes(category.id!) ? <Eye size={16} /> : <EyeOff size={16} />}\n                  </button>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n\n      {/* Help */}\n      <div style={{\n        marginTop: '2rem',\n        background: '#e8f5e8',\n        border: '1px solid #c3e6c3',\n        borderRadius: '8px',\n        padding: '1.5rem'\n      }}>\n        <h4 style={{ margin: '0 0 1rem 0', color: '#155724' }}>💡 Content Filtering</h4>\n        <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#155724' }}>\n          <li>Toggle categories on/off to control what appears on the TV display</li>\n          <li>When no categories are selected, all content is shown</li>\n          <li>Changes apply immediately to the live display</li>\n          <li>Use this to focus on specific types of announcements or events</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default TVContentManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,gBAAgB,QAA2B,oCAAoC;AACxF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM9D,MAAMC,gBAAiD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAWW,QAAQ,CAACI,sBAAsB,CAAC;EACvG,MAAM,CAACC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjB,QAAQ,CAAWW,QAAQ,CAACO,eAAe,CAAC;;EAE1G;EACA,MAAM;IACJC,aAAa;IACbC,OAAO,EAAEC,oBAAoB;IAC7BC,OAAO,EAAEC;EACX,CAAC,GAAGrB,gBAAgB,CAAC;IACnBsB,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAE,IAAI,CAAC;EAER,MAAM;IACJC,MAAM;IACNT,OAAO,EAAEU,aAAa;IACtBR,OAAO,EAAES;EACX,CAAC,GAAG5B,WAAW,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAAC;EAE3B,MAAMC,oBAAoB,GAAGA,CAACC,UAAkB,EAAEC,OAAgB,GAAG,KAAK,KAAK;IAC7E,IAAIA,OAAO,EAAE;MACX,MAAMC,aAAa,GAAGpB,uBAAuB,CAACqB,QAAQ,CAACH,UAAU,CAAC,GAC9DlB,uBAAuB,CAACsB,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKL,UAAU,CAAC,GACvD,CAAC,GAAGlB,uBAAuB,EAAEkB,UAAU,CAAC;MAE5CjB,0BAA0B,CAACmB,aAAa,CAAC;MACzCnC,gBAAgB,CAACuC,cAAc,CAAC;QAC9BtB,eAAe,EAAEkB;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMA,aAAa,GAAGvB,kBAAkB,CAACwB,QAAQ,CAACH,UAAU,CAAC,GACzDrB,kBAAkB,CAACyB,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKL,UAAU,CAAC,GAClD,CAAC,GAAGrB,kBAAkB,EAAEqB,UAAU,CAAC;MAEvCpB,qBAAqB,CAACsB,aAAa,CAAC;MACpCnC,gBAAgB,CAACuC,cAAc,CAAC;QAC9BzB,sBAAsB,EAAEqB;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlB,oBAAoB,CAAC,CAAC;IACtBQ,aAAa,CAAC,CAAC;IACf9B,gBAAgB,CAACqB,OAAO,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMP,sBAAsB,GAAG2B,KAAK,CAACC,IAAI,CACvC,IAAIC,GAAG,CAACzB,aAAa,CAAC0B,GAAG,CAACC,CAAC,KAAK;IAAEP,EAAE,EAAEO,CAAC,CAACC,WAAW;IAAEC,IAAI,EAAEF,CAAC,CAACG,aAAa;IAAEC,KAAK,EAAEJ,CAAC,CAACK;EAAe,CAAC,CAAC,CAAC,CACzG,CAAC,CAACb,MAAM,CAACc,GAAG,IAAIA,GAAG,CAACJ,IAAI,CAAC;;EAEzB;EACA,MAAM9B,eAAe,GAAGwB,KAAK,CAACC,IAAI,CAChC,IAAIC,GAAG,CAACf,MAAM,CAACgB,GAAG,CAACQ,CAAC,KAAK;IAAEd,EAAE,EAAEc,CAAC,CAACN,WAAW;IAAEC,IAAI,EAAEK,CAAC,CAACJ,aAAa;IAAEC,KAAK,EAAEG,CAAC,CAACF;EAAe,CAAC,CAAC,CAAC,CAClG,CAAC,CAACb,MAAM,CAACc,GAAG,IAAIA,GAAG,CAACJ,IAAI,IAAII,GAAG,CAACb,EAAE,CAAC;;EAEnC;EACA,MAAMe,qBAAqB,GAAGnC,aAAa,CAACmB,MAAM,CAACiB,YAAY,IAAI;IACjE,IAAI,CAAC5C,QAAQ,CAAC6C,iBAAiB,EAAE,OAAO,KAAK;IAC7C,IAAI3C,kBAAkB,CAAC4C,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAChD,OAAO5C,kBAAkB,CAACwB,QAAQ,CAACkB,YAAY,CAACR,WAAW,CAAC;EAC9D,CAAC,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE/C,QAAQ,CAACgD,gBAAgB,CAAC;EAEtC,MAAMC,cAAc,GAAG/B,MAAM,CAACS,MAAM,CAACuB,KAAK,IAAI;IAC5C,IAAI,CAAClD,QAAQ,CAACmD,kBAAkB,EAAE,OAAO,KAAK;IAC9C,IAAI9C,uBAAuB,CAACyC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IACrD,OAAOI,KAAK,CAACd,WAAW,IAAI/B,uBAAuB,CAACqB,QAAQ,CAACwB,KAAK,CAACd,WAAW,CAAC;EACjF,CAAC,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE/C,QAAQ,CAACoD,SAAS,CAAC;EAE/B,MAAMC,SAAS,GAAG;IAChBC,UAAU,EAAE,OAAO;IACnBC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,MAAM;IACfC,YAAY,EAAE;EAChB,CAAC;EAED,oBACE5D,OAAA;IAAA6D,QAAA,gBACE7D,OAAA;MAAK8D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBL,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,gBACA7D,OAAA;QAAI8D,KAAK,EAAE;UACTI,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,CAAC;UACT3B,KAAK,EAAE;QACT,CAAE;QAAAoB,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELxE,OAAA;QACEyE,OAAO,EAAEzC,gBAAiB;QAC1B8B,KAAK,EAAE;UACLN,UAAU,EAAE,SAAS;UACrBf,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,MAAM;UACdE,OAAO,EAAE,gBAAgB;UACzBD,YAAY,EAAE,KAAK;UACnBgB,MAAM,EAAE,SAAS;UACjBX,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBU,GAAG,EAAE;QACP,CAAE;QAAAd,QAAA,gBAEF7D,OAAA,CAACF,SAAS;UAAC8E,IAAI,EAAE;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNxE,OAAA;MAAK8D,KAAK,EAAE;QACVN,UAAU,EAAE,SAAS;QACrBE,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,QAAQ;QACjBC,YAAY,EAAE,MAAM;QACpBH,MAAM,EAAE;MACV,CAAE;MAAAI,QAAA,gBACA7D,OAAA;QAAI8D,KAAK,EAAE;UAAEM,MAAM,EAAE,YAAY;UAAE3B,KAAK,EAAE;QAAU,CAAE;QAAAoB,QAAA,EAAC;MAAmB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/ExE,OAAA;QAAK8D,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfc,mBAAmB,EAAE,sCAAsC;UAC3DF,GAAG,EAAE;QACP,CAAE;QAAAd,QAAA,gBACA7D,OAAA;UAAK8D,KAAK,EAAE;YACVN,UAAU,EAAE,OAAO;YACnBG,OAAO,EAAE,MAAM;YACfD,YAAY,EAAE,KAAK;YACnBoB,SAAS,EAAE;UACb,CAAE;UAAAjB,QAAA,gBACA7D,OAAA;YAAK8D,KAAK,EAAE;cAAEI,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAE1B,KAAK,EAAE;YAAU,CAAE;YAAAoB,QAAA,EACpEhB,qBAAqB,CAACG;UAAM;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACNxE,OAAA;YAAK8D,KAAK,EAAE;cAAErB,KAAK,EAAE;YAAU,CAAE;YAAAoB,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNxE,OAAA;UAAK8D,KAAK,EAAE;YACVN,UAAU,EAAE,OAAO;YACnBG,OAAO,EAAE,MAAM;YACfD,YAAY,EAAE,KAAK;YACnBoB,SAAS,EAAE;UACb,CAAE;UAAAjB,QAAA,gBACA7D,OAAA;YAAK8D,KAAK,EAAE;cAAEI,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAE1B,KAAK,EAAE;YAAU,CAAE;YAAAoB,QAAA,EACpEV,cAAc,CAACH;UAAM;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNxE,OAAA;YAAK8D,KAAK,EAAE;cAAErB,KAAK,EAAE;YAAU,CAAE;YAAAoB,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNxE,OAAA;UAAK8D,KAAK,EAAE;YACVN,UAAU,EAAE,OAAO;YACnBG,OAAO,EAAE,MAAM;YACfD,YAAY,EAAE,KAAK;YACnBoB,SAAS,EAAE;UACb,CAAE;UAAAjB,QAAA,gBACA7D,OAAA;YAAK8D,KAAK,EAAE;cAAEI,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAE1B,KAAK,EAAE;YAAU,CAAE;YAAAoB,QAAA,EACpEhB,qBAAqB,CAACG,MAAM,GAAGG,cAAc,CAACH;UAAM;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNxE,OAAA;YAAK8D,KAAK,EAAE;cAAErB,KAAK,EAAE;YAAU,CAAE;YAAAoB,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxE,OAAA;MAAK8D,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfc,mBAAmB,EAAE,SAAS;QAC9BF,GAAG,EAAE;MACP,CAAE;MAAAd,QAAA,gBAEA7D,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAI8D,KAAK,EAAE;YACTI,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,MAAM,EAAE,YAAY;YACpB3B,KAAK,EAAE,SAAS;YAChBsB,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBU,GAAG,EAAE;UACP,CAAE;UAAAd,QAAA,gBACA7D,OAAA,CAACH,MAAM;YAAC+E,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJlE,sBAAsB,CAAC0C,MAAM,KAAK,CAAC,gBAClChD,OAAA;UAAK8D,KAAK,EAAE;YACV,GAAGP,SAAS;YACZuB,SAAS,EAAE,QAAQ;YACnBrC,KAAK,EAAE;UACT,CAAE;UAAAoB,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAENlE,sBAAsB,CAAC8B,GAAG,CAAE2C,QAAQ,iBAClC/E,OAAA;UAAuB8D,KAAK,EAAEP,SAAU;UAAAM,QAAA,eACtC7D,OAAA;YAAK8D,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE;YACd,CAAE;YAAAJ,QAAA,gBACA7D,OAAA;cAAK8D,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEU,GAAG,EAAE;cAAS,CAAE;cAAAd,QAAA,gBACnE7D,OAAA;gBACE8D,KAAK,EAAE;kBACLkB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdvB,YAAY,EAAE,KAAK;kBACnBF,UAAU,EAAEuB,QAAQ,CAACtC,KAAK,IAAI;gBAChC;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxE,OAAA;gBAAM8D,KAAK,EAAE;kBAAEK,UAAU,EAAE;gBAAM,CAAE;gBAAAN,QAAA,EAAEkB,QAAQ,CAACxC;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENxE,OAAA;cACEyE,OAAO,EAAEA,CAAA,KAAMjD,oBAAoB,CAACuD,QAAQ,CAACjD,EAAE,CAAE;cACjDgC,KAAK,EAAE;gBACLN,UAAU,EAAEpD,kBAAkB,CAACwB,QAAQ,CAACmD,QAAQ,CAACjD,EAAE,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5EW,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdE,OAAO,EAAE,QAAQ;gBACjBD,YAAY,EAAE,KAAK;gBACnBgB,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,EAEDzD,kBAAkB,CAACwB,QAAQ,CAACmD,QAAQ,CAACjD,EAAE,CAAC,gBAAG9B,OAAA,CAACL,GAAG;gBAACiF,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACJ,MAAM;gBAACgF,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GAjCEO,QAAQ,CAACjD,EAAE;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkChB,CACN,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxE,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAI8D,KAAK,EAAE;YACTI,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,MAAM,EAAE,YAAY;YACpB3B,KAAK,EAAE,SAAS;YAChBsB,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBU,GAAG,EAAE;UACP,CAAE;UAAAd,QAAA,gBACA7D,OAAA,CAACH,MAAM;YAAC+E,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJ/D,eAAe,CAACuC,MAAM,KAAK,CAAC,gBAC3BhD,OAAA;UAAK8D,KAAK,EAAE;YACV,GAAGP,SAAS;YACZuB,SAAS,EAAE,QAAQ;YACnBrC,KAAK,EAAE;UACT,CAAE;UAAAoB,QAAA,EAAC;QAEH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAEN/D,eAAe,CAAC2B,GAAG,CAAE2C,QAAQ,iBAC3B/E,OAAA;UAAuB8D,KAAK,EAAEP,SAAU;UAAAM,QAAA,eACtC7D,OAAA;YAAK8D,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE;YACd,CAAE;YAAAJ,QAAA,gBACA7D,OAAA;cAAK8D,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEU,GAAG,EAAE;cAAS,CAAE;cAAAd,QAAA,gBACnE7D,OAAA;gBACE8D,KAAK,EAAE;kBACLkB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdvB,YAAY,EAAE,KAAK;kBACnBF,UAAU,EAAEuB,QAAQ,CAACtC,KAAK,IAAI;gBAChC;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxE,OAAA;gBAAM8D,KAAK,EAAE;kBAAEK,UAAU,EAAE;gBAAM,CAAE;gBAAAN,QAAA,EAAEkB,QAAQ,CAACxC;cAAI;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENxE,OAAA;cACEyE,OAAO,EAAEA,CAAA,KAAMjD,oBAAoB,CAACuD,QAAQ,CAACjD,EAAE,EAAG,IAAI,CAAE;cACxDgC,KAAK,EAAE;gBACLN,UAAU,EAAEjD,uBAAuB,CAACqB,QAAQ,CAACmD,QAAQ,CAACjD,EAAG,CAAC,GAAG,SAAS,GAAG,SAAS;gBAClFW,KAAK,EAAE,OAAO;gBACdgB,MAAM,EAAE,MAAM;gBACdE,OAAO,EAAE,QAAQ;gBACjBD,YAAY,EAAE,KAAK;gBACnBgB,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,EAEDtD,uBAAuB,CAACqB,QAAQ,CAACmD,QAAQ,CAACjD,EAAG,CAAC,gBAAG9B,OAAA,CAACL,GAAG;gBAACiF,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxE,OAAA,CAACJ,MAAM;gBAACgF,IAAI,EAAE;cAAG;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GAjCEO,QAAQ,CAACjD,EAAE;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkChB,CACN,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAK8D,KAAK,EAAE;QACVoB,SAAS,EAAE,MAAM;QACjB1B,UAAU,EAAE,SAAS;QACrBC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE;MACX,CAAE;MAAAE,QAAA,gBACA7D,OAAA;QAAI8D,KAAK,EAAE;UAAEM,MAAM,EAAE,YAAY;UAAE3B,KAAK,EAAE;QAAU,CAAE;QAAAoB,QAAA,EAAC;MAAoB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChFxE,OAAA;QAAI8D,KAAK,EAAE;UAAEM,MAAM,EAAE,CAAC;UAAEe,WAAW,EAAE,QAAQ;UAAE1C,KAAK,EAAE;QAAU,CAAE;QAAAoB,QAAA,gBAChE7D,OAAA;UAAA6D,QAAA,EAAI;QAAkE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ExE,OAAA;UAAA6D,QAAA,EAAI;QAAqD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DxE,OAAA;UAAA6D,QAAA,EAAI;QAA6C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDxE,OAAA;UAAA6D,QAAA,EAAI;QAA8D;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrE,EAAA,CAhUIF,gBAAiD;EAAA,QASjDR,gBAAgB,EAYhBC,WAAW;AAAA;AAAA0F,EAAA,GArBXnF,gBAAiD;AAkUvD,eAAeA,gBAAgB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}