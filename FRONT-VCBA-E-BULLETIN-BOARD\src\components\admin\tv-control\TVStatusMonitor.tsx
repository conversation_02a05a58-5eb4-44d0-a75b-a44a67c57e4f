import React, { useState, useEffect } from 'react';
import { TVDisplayStatus, TVDisplaySettings } from '../../../services/tvControlService';
import { Monitor, Wifi, WifiOff, Clock, BarChart3, RefreshCw, ExternalLink } from 'lucide-react';

interface TVStatusMonitorProps {
  status: TVDisplayStatus;
  settings: TVDisplaySettings;
}

const TVStatusMonitor: React.FC<TVStatusMonitorProps> = ({ status, settings }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatUptime = (uptime: string) => {
    // Simple uptime formatting - in a real app, this would be calculated properly
    return uptime || '0m';
  };

  const formatLastRefresh = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  const openTVDisplay = () => {
    window.open('/tv-display', '_blank', 'fullscreen=yes');
  };

  const statusCardStyle = {
    background: 'white',
    border: '1px solid #e9ecef',
    borderRadius: '12px',
    padding: '1.5rem',
    textAlign: 'center' as const
  };

  const metricCardStyle = {
    background: '#f8f9fa',
    border: '1px solid #e9ecef',
    borderRadius: '8px',
    padding: '1rem'
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <h2 style={{
          fontSize: '1.8rem',
          fontWeight: '600',
          margin: 0,
          color: '#2c3e50',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <BarChart3 size={24} />
          Status Monitor
        </h2>

        <button
          onClick={openTVDisplay}
          style={{
            background: '#17a2b8',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '6px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}
        >
          <ExternalLink size={16} />
          View TV Display
        </button>
      </div>

      {/* Status Overview */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        {/* Connection Status */}
        <div style={{
          ...statusCardStyle,
          borderColor: status.isOnline ? '#28a745' : '#dc3545',
          borderWidth: '2px'
        }}>
          <div style={{
            fontSize: '3rem',
            marginBottom: '1rem'
          }}>
            {status.isOnline ? <Wifi size={48} color="#28a745" /> : <WifiOff size={48} color="#dc3545" />}
          </div>
          <h3 style={{
            margin: '0 0 0.5rem 0',
            color: status.isOnline ? '#28a745' : '#dc3545',
            fontSize: '1.2rem'
          }}>
            {status.isOnline ? 'Online' : 'Offline'}
          </h3>
          <p style={{
            margin: 0,
            color: '#6c757d',
            fontSize: '0.9rem'
          }}>
            TV Display Status
          </p>
        </div>

        {/* Playback Status */}
        <div style={statusCardStyle}>
          <div style={{
            fontSize: '3rem',
            marginBottom: '1rem',
            color: status.isPlaying ? '#28a745' : '#ffc107'
          }}>
            {status.isPlaying ? '▶️' : '⏸️'}
          </div>
          <h3 style={{
            margin: '0 0 0.5rem 0',
            color: '#2c3e50',
            fontSize: '1.2rem'
          }}>
            {status.isPlaying ? 'Playing' : 'Paused'}
          </h3>
          <p style={{
            margin: 0,
            color: '#6c757d',
            fontSize: '0.9rem'
          }}>
            Slideshow Status
          </p>
        </div>

        {/* Current Slide */}
        <div style={statusCardStyle}>
          <div style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            color: '#3498db'
          }}>
            {status.currentSlide + 1}/{status.totalSlides}
          </div>
          <h3 style={{
            margin: '0 0 0.5rem 0',
            color: '#2c3e50',
            fontSize: '1.2rem'
          }}>
            Current Slide
          </h3>
          <p style={{
            margin: 0,
            color: '#6c757d',
            fontSize: '0.9rem'
          }}>
            Slide Position
          </p>
        </div>

        {/* Connected Devices */}
        <div style={statusCardStyle}>
          <div style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            color: '#17a2b8'
          }}>
            {status.connectedDevices}
          </div>
          <h3 style={{
            margin: '0 0 0.5rem 0',
            color: '#2c3e50',
            fontSize: '1.2rem'
          }}>
            Connected
          </h3>
          <p style={{
            margin: 0,
            color: '#6c757d',
            fontSize: '0.9rem'
          }}>
            TV Displays
          </p>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '2rem',
        marginBottom: '2rem'
      }}>
        {/* System Information */}
        <div style={{
          background: 'white',
          border: '1px solid #e9ecef',
          borderRadius: '12px',
          padding: '1.5rem'
        }}>
          <h3 style={{
            margin: '0 0 1.5rem 0',
            color: '#2c3e50',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <Monitor size={20} />
            System Information
          </h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Uptime</span>
                <span style={{ color: '#28a745', fontWeight: '600' }}>
                  {formatUptime(status.uptime)}
                </span>
              </div>
            </div>

            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Last Refresh</span>
                <span style={{ color: '#17a2b8', fontWeight: '600' }}>
                  {formatLastRefresh(status.lastRefresh)}
                </span>
              </div>
            </div>

            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Auto-Play</span>
                <span style={{
                  color: settings.autoPlay ? '#28a745' : '#dc3545',
                  fontWeight: '600'
                }}>
                  {settings.autoPlay ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </div>

            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Slide Interval</span>
                <span style={{ color: '#6f42c1', fontWeight: '600' }}>
                  {settings.slideInterval / 1000}s
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Content Statistics */}
        <div style={{
          background: 'white',
          border: '1px solid #e9ecef',
          borderRadius: '12px',
          padding: '1.5rem'
        }}>
          <h3 style={{
            margin: '0 0 1.5rem 0',
            color: '#2c3e50',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <BarChart3 size={20} />
            Content Statistics
          </h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Announcements</span>
                <span style={{ color: '#3498db', fontWeight: '600' }}>
                  {settings.showAnnouncements ? `Max ${settings.maxAnnouncements}` : 'Disabled'}
                </span>
              </div>
            </div>

            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Calendar Events</span>
                <span style={{ color: '#e74c3c', fontWeight: '600' }}>
                  {settings.showCalendarEvents ? `Max ${settings.maxEvents}` : 'Disabled'}
                </span>
              </div>
            </div>

            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Emergency Active</span>
                <span style={{
                  color: settings.emergencyActive ? '#dc3545' : '#28a745',
                  fontWeight: '600'
                }}>
                  {settings.emergencyActive ? 'Yes' : 'No'}
                </span>
              </div>
            </div>

            <div style={metricCardStyle}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '500' }}>Transition Type</span>
                <span style={{ color: '#6f42c1', fontWeight: '600' }}>
                  {settings.transitionType}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Clock */}
      <div style={{
        background: '#2c3e50',
        color: 'white',
        borderRadius: '12px',
        padding: '2rem',
        textAlign: 'center'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '1rem',
          marginBottom: '1rem'
        }}>
          <Clock size={24} />
          <h3 style={{ margin: 0, fontSize: '1.2rem' }}>Current Time</h3>
        </div>
        <div style={{
          fontSize: '2.5rem',
          fontWeight: 'bold',
          fontFamily: 'monospace'
        }}>
          {currentTime.toLocaleTimeString()}
        </div>
        <div style={{
          fontSize: '1.2rem',
          opacity: 0.8,
          marginTop: '0.5rem'
        }}>
          {currentTime.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </div>
      </div>
    </div>
  );
};

export default TVStatusMonitor;
