{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\tv\\\\TVDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TVDisplay = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Listen for real-time settings updates via localStorage\n  useEffect(() => {\n    const handleStorageChange = e => {\n      console.log('Storage change detected:', e.key, e.newValue);\n      if (e.key === 'tv_display_settings') {\n        // Settings changed, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings changed:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_display_settings_updated') {\n        // Settings update signal, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings updated:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_emergency_broadcast') {\n        // Emergency broadcast signal\n        const newSettings = tvControlService.getSettings();\n        console.log('Emergency broadcast detected via storage:', newSettings);\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      } else if (e.key === 'tv_emergency_active') {\n        // Emergency active state changed\n        const newSettings = tvControlService.getSettings();\n        console.log('Emergency active state changed:', e.newValue, newSettings);\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      }\n    };\n    const handleEmergencyBroadcast = e => {\n      console.log('Custom emergency broadcast event:', e.detail);\n      const newSettings = tvControlService.getSettings();\n      setSettings(newSettings);\n      setRefreshKey(prev => prev + 1);\n    };\n    const handleEmergencyCleared = () => {\n      console.log('Emergency cleared event');\n      const newSettings = tvControlService.getSettings();\n      setSettings(newSettings);\n      setRefreshKey(prev => prev + 1);\n    };\n\n    // Listen for storage changes from other tabs/windows\n    window.addEventListener('storage', handleStorageChange);\n    window.addEventListener('emergency-broadcast', handleEmergencyBroadcast);\n    window.addEventListener('emergency-cleared', handleEmergencyCleared);\n\n    // Also check for settings changes periodically (for same-tab updates)\n    const settingsCheckInterval = setInterval(() => {\n      const currentSettings = tvControlService.getSettings();\n      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {\n        console.log('Periodic check - Settings changed:', currentSettings);\n        setSettings(currentSettings);\n        // Force re-render for emergency messages\n        setRefreshKey(prev => prev + 1);\n      }\n    }, 250); // Check every 250ms for faster emergency response\n\n    // Emergency-specific check\n    const emergencyCheckInterval = setInterval(() => {\n      const emergencyActive = localStorage.getItem('tv_emergency_active') === 'true';\n      const emergencyMessage = localStorage.getItem('tv_emergency_message');\n      if (emergencyActive && emergencyMessage && !settings.emergencyActive) {\n        console.log('Emergency detected via periodic check:', emergencyMessage);\n        const newSettings = tvControlService.getSettings();\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      }\n    }, 100); // Check every 100ms for emergency\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('emergency-broadcast', handleEmergencyBroadcast);\n      window.removeEventListener('emergency-cleared', handleEmergencyCleared);\n      clearInterval(settingsCheckInterval);\n      clearInterval(emergencyCheckInterval);\n    };\n  }, [settings]);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach(command => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = command => {\n    var _slideshowRef$current, _slideshowRef$current2;\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if ((_slideshowRef$current = slideshowRef.current) !== null && _slideshowRef$current !== void 0 && _slideshowRef$current.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if ((_slideshowRef$current2 = slideshowRef.current) !== null && _slideshowRef$current2 !== void 0 && _slideshowRef$current2.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 || event.category_id && settings.eventCategories.includes(event.category_id);\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime()).slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides = [];\n\n    // Add announcements\n    if (settings.showAnnouncements && announcements && announcements.length > 0) {\n      const filteredAnnouncements = announcements.filter(announcement => {\n        const matchesCategory = settings.announcementCategories.length === 0 || settings.announcementCategories.includes(announcement.category_id);\n        return matchesCategory;\n      }).slice(0, settings.maxAnnouncements);\n      filteredAnnouncements.forEach(announcement => slides.push(/*#__PURE__*/_jsxDEV(TVAnnouncement, {\n        announcement: announcement\n      }, `announcement-${announcement.announcement_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this)));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach(event => slides.push(/*#__PURE__*/_jsxDEV(TVCalendarEvent, {\n        event: event\n      }, `event-${event.calendar_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)));\n    }\n    return slides;\n  };\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      margin: 0,\n      padding: 0,\n      fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n      background: 'linear-gradient(135deg, #22c55e 0%, #fbbf24 100%)',\n      // Green to Yellow gradient\n      minHeight: '100vh',\n      overflowX: 'hidden',\n      color: '#ffffff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(255, 255, 255, 0.95)',\n        padding: '1rem 2rem',\n        textAlign: 'center',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        color: '#2c3e50',\n        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'\n      },\n      children: formatDateTime()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"tv-content\",\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading latest announcements and events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this), hasError && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            marginBottom: '2rem',\n            fontWeight: 'bold',\n            color: '#e74c3c'\n          },\n          children: \"ERROR\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Unable to load content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            marginTop: '1rem',\n            opacity: 0.8\n          },\n          children: \"Please check your internet connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this), settings.emergencyActive && settings.emergencyMessage && (console.log('Rendering emergency message:', settings.emergencyMessage), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(220, 53, 69, 0.95)',\n          color: 'white',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999,\n          animation: 'emergency-flash 2s infinite'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '2rem',\n            animation: 'emergency-pulse 1s infinite',\n            fontWeight: 'bold',\n            color: '#ffffff'\n          },\n          children: \"EMERGENCY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            fontWeight: 'bold',\n            textAlign: 'center',\n            marginBottom: '2rem',\n            textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'\n          },\n          children: \"EMERGENCY ALERT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            textAlign: 'center',\n            lineHeight: '1.4',\n            maxWidth: '80%',\n            background: 'rgba(0, 0, 0, 0.3)',\n            padding: '2rem',\n            borderRadius: '20px'\n          },\n          children: settings.emergencyMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 11\n      }, this)), !isLoading && !hasError && !settings.emergencyActive && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: slides.length > 0 ? /*#__PURE__*/_jsxDEV(TVSlideshow, {\n          ref: slideshowRef,\n          autoPlayInterval: settings.slideInterval,\n          showProgress: true,\n          isPlaying: isPlaying && settings.autoPlay,\n          onSlideChange: setCurrentSlide,\n          children: slides\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-no-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '3rem',\n              marginBottom: '3rem',\n              fontWeight: 'bold',\n              color: '#6c757d'\n            },\n            children: \"NO CONTENT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No announcements or events to display\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              marginTop: '2rem',\n              opacity: 0.7\n            },\n            children: \"Check back later for updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 15\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      httpEquiv: \"refresh\",\n      content: \"600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 298,\n    columnNumber: 5\n  }, this);\n};\n_s(TVDisplay, \"KDYFlrHSu63QwnfhujBG3li3Uf4=\", false, function () {\n  return [useAnnouncements, useCalendar];\n});\n_c = TVDisplay;\nexport default TVDisplay;\nvar _c;\n$RefreshReg$(_c, \"TVDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAnnouncements", "useCalendar", "tvControlService", "TVAnnouncement", "TVCalendarEvent", "TVSlideshow", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TVDisplay", "_s", "currentTime", "setCurrentTime", "Date", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "settings", "setSettings", "getSettings", "isPlaying", "setIsPlaying", "currentSlide", "setCurrentSlide", "slideshowRef", "currentDate", "announcements", "loading", "announcementsLoading", "error", "announcementsError", "refresh", "refreshAnnouncements", "status", "page", "limit", "maxAnnouncements", "sort_by", "sort_order", "events", "eventsLoading", "eventsError", "refreshEvents", "unsubscribe", "onSettingsChange", "handleStorageChange", "e", "console", "log", "key", "newValue", "newSettings", "prev", "handleEmergencyBroadcast", "detail", "handleEmergencyCleared", "window", "addEventListener", "settingsCheckInterval", "setInterval", "currentSettings", "JSON", "stringify", "emergencyCheckInterval", "emergencyActive", "localStorage", "getItem", "emergencyMessage", "removeEventListener", "clearInterval", "sendHeartbeat", "setItem", "now", "toString", "updateStatus", "isOnline", "totalSlides", "createSlideContent", "length", "lastRefresh", "toISOString", "heartbeatInterval", "checkCommands", "commands", "getStoredCommands", "for<PERSON>ach", "command", "handleControlCommand", "clearProcessedCommands", "commandInterval", "_slideshowRef$current", "_slideshowRef$current2", "action", "current", "nextSlide", "prevSlide", "timeInterval", "refreshInterval", "reloadInterval", "location", "reload", "formatDateTime", "options", "weekday", "year", "month", "day", "hour", "minute", "toLocaleDateString", "getUpcomingEvents", "showCalendarEvents", "today", "thirtyDaysFromNow", "setDate", "getDate", "filter", "event", "eventDate", "event_date", "matchesCategory", "eventCategories", "category_id", "includes", "is_active", "sort", "a", "b", "getTime", "slice", "maxEvents", "slides", "showAnnouncements", "filteredAnnouncements", "announcement", "announcementCategories", "push", "announcement_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "upcomingEvents", "calendar_id", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "style", "margin", "padding", "fontFamily", "background", "minHeight", "overflowX", "color", "children", "textAlign", "fontSize", "fontWeight", "borderBottom", "boxShadow", "className", "marginBottom", "marginTop", "opacity", "position", "top", "left", "right", "bottom", "display", "flexDirection", "alignItems", "justifyContent", "zIndex", "animation", "textShadow", "lineHeight", "max<PERSON><PERSON><PERSON>", "borderRadius", "ref", "autoPlayInterval", "slideInterval", "showProgress", "autoPlay", "onSlideChange", "httpEquiv", "content", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/tv/TVDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService, TVDisplaySettings, TVControlCommand } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\n\nconst TVDisplay: React.FC = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState<TVDisplaySettings>(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef<any>(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Listen for real-time settings updates via localStorage\n  useEffect(() => {\n    const handleStorageChange = (e: StorageEvent) => {\n      console.log('Storage change detected:', e.key, e.newValue);\n\n      if (e.key === 'tv_display_settings') {\n        // Settings changed, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings changed:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_display_settings_updated') {\n        // Settings update signal, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings updated:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_emergency_broadcast') {\n        // Emergency broadcast signal\n        const newSettings = tvControlService.getSettings();\n        console.log('Emergency broadcast detected via storage:', newSettings);\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      } else if (e.key === 'tv_emergency_active') {\n        // Emergency active state changed\n        const newSettings = tvControlService.getSettings();\n        console.log('Emergency active state changed:', e.newValue, newSettings);\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      }\n    };\n\n    const handleEmergencyBroadcast = (e: CustomEvent) => {\n      console.log('Custom emergency broadcast event:', e.detail);\n      const newSettings = tvControlService.getSettings();\n      setSettings(newSettings);\n      setRefreshKey(prev => prev + 1);\n    };\n\n    const handleEmergencyCleared = () => {\n      console.log('Emergency cleared event');\n      const newSettings = tvControlService.getSettings();\n      setSettings(newSettings);\n      setRefreshKey(prev => prev + 1);\n    };\n\n    // Listen for storage changes from other tabs/windows\n    window.addEventListener('storage', handleStorageChange);\n    window.addEventListener('emergency-broadcast', handleEmergencyBroadcast as EventListener);\n    window.addEventListener('emergency-cleared', handleEmergencyCleared);\n\n    // Also check for settings changes periodically (for same-tab updates)\n    const settingsCheckInterval = setInterval(() => {\n      const currentSettings = tvControlService.getSettings();\n      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {\n        console.log('Periodic check - Settings changed:', currentSettings);\n        setSettings(currentSettings);\n        // Force re-render for emergency messages\n        setRefreshKey(prev => prev + 1);\n      }\n    }, 250); // Check every 250ms for faster emergency response\n\n    // Emergency-specific check\n    const emergencyCheckInterval = setInterval(() => {\n      const emergencyActive = localStorage.getItem('tv_emergency_active') === 'true';\n      const emergencyMessage = localStorage.getItem('tv_emergency_message');\n\n      if (emergencyActive && emergencyMessage && !settings.emergencyActive) {\n        console.log('Emergency detected via periodic check:', emergencyMessage);\n        const newSettings = tvControlService.getSettings();\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      }\n    }, 100); // Check every 100ms for emergency\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('emergency-broadcast', handleEmergencyBroadcast as EventListener);\n      window.removeEventListener('emergency-cleared', handleEmergencyCleared);\n      clearInterval(settingsCheckInterval);\n      clearInterval(emergencyCheckInterval);\n    };\n  }, [settings]);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach((command: TVControlCommand) => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = (command: TVControlCommand) => {\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if (slideshowRef.current?.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if (slideshowRef.current?.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options: Intl.DateTimeFormatOptions = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 ||\n        (event.category_id && settings.eventCategories.includes(event.category_id));\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())\n      .slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides: React.ReactNode[] = [];\n\n    // Add announcements\n    if (settings.showAnnouncements && announcements && announcements.length > 0) {\n      const filteredAnnouncements = announcements.filter(announcement => {\n        const matchesCategory = settings.announcementCategories.length === 0 ||\n          settings.announcementCategories.includes(announcement.category_id);\n        return matchesCategory;\n      }).slice(0, settings.maxAnnouncements);\n\n      filteredAnnouncements.forEach((announcement) => (\n        slides.push(\n          <TVAnnouncement\n            key={`announcement-${announcement.announcement_id}-${refreshKey}`}\n            announcement={announcement}\n          />\n        )\n      ));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event) => (\n        slides.push(\n          <TVCalendarEvent\n            key={`event-${event.calendar_id}-${refreshKey}`}\n            event={event}\n          />\n        )\n      ));\n    }\n\n    return slides;\n  };\n\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n\n  return (\n    <div style={{\n      margin: 0,\n      padding: 0,\n      fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n      background: 'linear-gradient(135deg, #22c55e 0%, #fbbf24 100%)', // Green to Yellow gradient\n      minHeight: '100vh',\n      overflowX: 'hidden',\n      color: '#ffffff'\n    }}>\n      {/* Current date and time */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.95)',\n        padding: '1rem 2rem',\n        textAlign: 'center',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        color: '#2c3e50',\n        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'\n      }}>\n        {formatDateTime()}\n      </div>\n\n      {/* Main content area */}\n      <main className=\"tv-content\">\n        {/* Loading state */}\n        {isLoading && (\n          <div className=\"tv-loading\">\n            <div className=\"tv-loading-spinner\"></div>\n            <div>Loading latest announcements and events...</div>\n          </div>\n        )}\n\n        {/* Error state */}\n        {hasError && !isLoading && (\n          <div className=\"tv-error\">\n            <div style={{ fontSize: '3rem', marginBottom: '2rem', fontWeight: 'bold', color: '#e74c3c' }}>ERROR</div>\n            <div>Unable to load content</div>\n            <div style={{ fontSize: '2rem', marginTop: '1rem', opacity: 0.8 }}>\n              Please check your internet connection\n            </div>\n          </div>\n        )}\n\n        {/* Emergency Message Override */}\n        {settings.emergencyActive && settings.emergencyMessage && (\n          console.log('Rendering emergency message:', settings.emergencyMessage),\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(220, 53, 69, 0.95)',\n            color: 'white',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 9999,\n            animation: 'emergency-flash 2s infinite'\n          }}>\n            <div style={{\n              fontSize: '4rem',\n              marginBottom: '2rem',\n              animation: 'emergency-pulse 1s infinite',\n              fontWeight: 'bold',\n              color: '#ffffff'\n            }}>\n              EMERGENCY\n            </div>\n            <div style={{\n              fontSize: '4rem',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              marginBottom: '2rem',\n              textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'\n            }}>\n              EMERGENCY ALERT\n            </div>\n            <div style={{\n              fontSize: '3rem',\n              textAlign: 'center',\n              lineHeight: '1.4',\n              maxWidth: '80%',\n              background: 'rgba(0, 0, 0, 0.3)',\n              padding: '2rem',\n              borderRadius: '20px'\n            }}>\n              {settings.emergencyMessage}\n            </div>\n          </div>\n        )}\n\n        {/* Content slideshow */}\n        {!isLoading && !hasError && !settings.emergencyActive && (\n          <>\n            {slides.length > 0 ? (\n              <TVSlideshow\n                ref={slideshowRef}\n                autoPlayInterval={settings.slideInterval}\n                showProgress={true}\n                isPlaying={isPlaying && settings.autoPlay}\n                onSlideChange={setCurrentSlide}\n              >\n                {slides}\n              </TVSlideshow>\n            ) : (\n              <div className=\"tv-no-content\">\n                <div style={{ fontSize: '3rem', marginBottom: '3rem', fontWeight: 'bold', color: '#6c757d' }}>NO CONTENT</div>\n                <div>No announcements or events to display</div>\n                <div style={{ fontSize: '2rem', marginTop: '2rem', opacity: 0.7 }}>\n                  Check back later for updates\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </main>\n\n\n\n      {/* Meta refresh as backup */}\n      <meta httpEquiv=\"refresh\" content=\"600\" />\n    </div>\n  );\n};\n\nexport default TVDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAA6C,iCAAiC;AACvG,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAoBK,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,YAAY,GAAGzB,MAAM,CAAM,IAAI,CAAC;;EAEtC;EACA,MAAM0B,WAAW,GAAG,IAAIX,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAM;IACJY,aAAa;IACbC,OAAO,EAAEC,oBAAoB;IAC7BC,KAAK,EAAEC,kBAAkB;IACzBC,OAAO,EAAEC;EACX,CAAC,GAAGhC,gBAAgB,CAAC;IACnBiC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAElB,QAAQ,CAACmB,gBAAgB;IAChCC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA,MAAM;IACJC,MAAM;IACNZ,OAAO,EAAEa,aAAa;IACtBX,KAAK,EAAEY,WAAW;IAClBV,OAAO,EAAEW;EACX,CAAC,GAAGzC,WAAW,CAACwB,WAAW,CAAC;;EAE5B;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM6C,WAAW,GAAGzC,gBAAgB,CAAC0C,gBAAgB,CAAC1B,WAAW,CAAC;IAClE,OAAOyB,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAM+C,mBAAmB,GAAIC,CAAe,IAAK;MAC/CC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,CAAC,CAACG,GAAG,EAAEH,CAAC,CAACI,QAAQ,CAAC;MAE1D,IAAIJ,CAAC,CAACG,GAAG,KAAK,qBAAqB,EAAE;QACnC;QACA,MAAME,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD4B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEG,WAAW,CAAC;QAC7DjC,WAAW,CAACiC,WAAW,CAAC;MAC1B,CAAC,MAAM,IAAIL,CAAC,CAACG,GAAG,KAAK,6BAA6B,EAAE;QAClD;QACA,MAAME,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD4B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEG,WAAW,CAAC;QAC7DjC,WAAW,CAACiC,WAAW,CAAC;MAC1B,CAAC,MAAM,IAAIL,CAAC,CAACG,GAAG,KAAK,wBAAwB,EAAE;QAC7C;QACA,MAAME,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD4B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEG,WAAW,CAAC;QACrEjC,WAAW,CAACiC,WAAW,CAAC;QACxBnC,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM,IAAIN,CAAC,CAACG,GAAG,KAAK,qBAAqB,EAAE;QAC1C;QACA,MAAME,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD4B,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,CAAC,CAACI,QAAQ,EAAEC,WAAW,CAAC;QACvEjC,WAAW,CAACiC,WAAW,CAAC;QACxBnC,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC;IACF,CAAC;IAED,MAAMC,wBAAwB,GAAIP,CAAc,IAAK;MACnDC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,CAAC,CAACQ,MAAM,CAAC;MAC1D,MAAMH,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;MAClDD,WAAW,CAACiC,WAAW,CAAC;MACxBnC,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;MACnCR,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtC,MAAMG,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;MAClDD,WAAW,CAACiC,WAAW,CAAC;MACxBnC,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC;;IAED;IACAI,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEZ,mBAAmB,CAAC;IACvDW,MAAM,CAACC,gBAAgB,CAAC,qBAAqB,EAAEJ,wBAAyC,CAAC;IACzFG,MAAM,CAACC,gBAAgB,CAAC,mBAAmB,EAAEF,sBAAsB,CAAC;;IAEpE;IACA,MAAMG,qBAAqB,GAAGC,WAAW,CAAC,MAAM;MAC9C,MAAMC,eAAe,GAAG1D,gBAAgB,CAACiB,WAAW,CAAC,CAAC;MACtD,IAAI0C,IAAI,CAACC,SAAS,CAACF,eAAe,CAAC,KAAKC,IAAI,CAACC,SAAS,CAAC7C,QAAQ,CAAC,EAAE;QAChE8B,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEY,eAAe,CAAC;QAClE1C,WAAW,CAAC0C,eAAe,CAAC;QAC5B;QACA5C,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET;IACA,MAAMW,sBAAsB,GAAGJ,WAAW,CAAC,MAAM;MAC/C,MAAMK,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,KAAK,MAAM;MAC9E,MAAMC,gBAAgB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAErE,IAAIF,eAAe,IAAIG,gBAAgB,IAAI,CAAClD,QAAQ,CAAC+C,eAAe,EAAE;QACpEjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEmB,gBAAgB,CAAC;QACvE,MAAMhB,WAAW,GAAGjD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClDD,WAAW,CAACiC,WAAW,CAAC;QACxBnC,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXI,MAAM,CAACY,mBAAmB,CAAC,SAAS,EAAEvB,mBAAmB,CAAC;MAC1DW,MAAM,CAACY,mBAAmB,CAAC,qBAAqB,EAAEf,wBAAyC,CAAC;MAC5FG,MAAM,CAACY,mBAAmB,CAAC,mBAAmB,EAAEb,sBAAsB,CAAC;MACvEc,aAAa,CAACX,qBAAqB,CAAC;MACpCW,aAAa,CAACN,sBAAsB,CAAC;IACvC,CAAC;EACH,CAAC,EAAE,CAAC9C,QAAQ,CAAC,CAAC;;EAEd;EACAnB,SAAS,CAAC,MAAM;IACd,MAAMwE,aAAa,GAAGA,CAAA,KAAM;MAC1BL,YAAY,CAACM,OAAO,CAAC,sBAAsB,EAAEzD,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MACnEvE,gBAAgB,CAACwE,YAAY,CAAC;QAC5BC,QAAQ,EAAE,IAAI;QACdvD,SAAS;QACTE,YAAY;QACZsD,WAAW,EAAEC,kBAAkB,CAAC,CAAC,CAACC,MAAM;QACxCC,WAAW,EAAE,IAAIjE,IAAI,CAAC,CAAC,CAACkE,WAAW,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IAEDV,aAAa,CAAC,CAAC;IACf,MAAMW,iBAAiB,GAAGtB,WAAW,CAACW,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE5D,OAAO,MAAMD,aAAa,CAACY,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAAC7D,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAE7B;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMoF,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,QAAQ,GAAGjF,gBAAgB,CAACkF,iBAAiB,CAAC,CAAC;MACrD,IAAID,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;QACvBK,QAAQ,CAACE,OAAO,CAAEC,OAAyB,IAAK;UAC9CC,oBAAoB,CAACD,OAAO,CAAC;QAC/B,CAAC,CAAC;QACFpF,gBAAgB,CAACsF,sBAAsB,CAAC,CAAC;MAC3C;IACF,CAAC;IAED,MAAMC,eAAe,GAAG9B,WAAW,CAACuB,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,OAAO,MAAMb,aAAa,CAACoB,eAAe,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMF,oBAAoB,GAAID,OAAyB,IAAK;IAAA,IAAAI,qBAAA,EAAAC,sBAAA;IAC1D,QAAQL,OAAO,CAACM,MAAM;MACpB,KAAK,MAAM;QACTvE,YAAY,CAAC,IAAI,CAAC;QAClB;MACF,KAAK,OAAO;QACVA,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,KAAK,MAAM;QACT,KAAAqE,qBAAA,GAAIlE,YAAY,CAACqE,OAAO,cAAAH,qBAAA,eAApBA,qBAAA,CAAsBI,SAAS,EAAE;UACnCtE,YAAY,CAACqE,OAAO,CAACC,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,UAAU;QACb,KAAAH,sBAAA,GAAInE,YAAY,CAACqE,OAAO,cAAAF,sBAAA,eAApBA,sBAAA,CAAsBI,SAAS,EAAE;UACnCvE,YAAY,CAACqE,OAAO,CAACE,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,SAAS;QACZ/D,oBAAoB,CAAC,CAAC;QACtBU,aAAa,CAAC,CAAC;QACf1B,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/B;MACF,KAAK,WAAW;QACd;QACA;IACJ;EACF,CAAC;;EAED;EACAtD,SAAS,CAAC,MAAM;IACd,MAAMkG,YAAY,GAAGrC,WAAW,CAAC,MAAM;MACrC9C,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMuD,aAAa,CAAC2B,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlG,SAAS,CAAC,MAAM;IACd,MAAMmG,eAAe,GAAGtC,WAAW,CAAC,MAAM;MACxC3B,oBAAoB,CAAC,CAAC;MACtBU,aAAa,CAAC,CAAC;MACf1B,aAAa,CAACoC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMiB,aAAa,CAAC4B,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACjE,oBAAoB,EAAEU,aAAa,CAAC,CAAC;;EAEzC;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMoG,cAAc,GAAGvC,WAAW,CAAC,MAAM;MACvCH,MAAM,CAAC2C,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAM/B,aAAa,CAAC6B,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAmC,GAAG;MAC1CC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD,OAAOhG,WAAW,CAACiG,kBAAkB,CAAC,OAAO,EAAEP,OAAO,CAAC;EACzD,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC7F,QAAQ,CAAC8F,kBAAkB,EAAE,OAAO,EAAE;IAE3C,MAAMC,KAAK,GAAG,IAAIlG,IAAI,CAAC,CAAC;IACxB,MAAMmG,iBAAiB,GAAG,IAAInG,IAAI,CAAC,CAAC;IACpCmG,iBAAiB,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE/C,OAAO5E,MAAM,CAAC6E,MAAM,CAACC,KAAK,IAAI;MAC5B,MAAMC,SAAS,GAAG,IAAIxG,IAAI,CAACuG,KAAK,CAACE,UAAU,CAAC;MAC5C,MAAMC,eAAe,GAAGvG,QAAQ,CAACwG,eAAe,CAAC3C,MAAM,KAAK,CAAC,IAC1DuC,KAAK,CAACK,WAAW,IAAIzG,QAAQ,CAACwG,eAAe,CAACE,QAAQ,CAACN,KAAK,CAACK,WAAW,CAAE;MAC7E,OAAOJ,SAAS,IAAIN,KAAK,IAAIM,SAAS,IAAIL,iBAAiB,IAAII,KAAK,CAACO,SAAS,IAAIJ,eAAe;IACnG,CAAC,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIjH,IAAI,CAACgH,CAAC,CAACP,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,GAAG,IAAIlH,IAAI,CAACiH,CAAC,CAACR,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CACnFC,KAAK,CAAC,CAAC,EAAEhH,QAAQ,CAACiH,SAAS,CAAC;EACjC,CAAC;;EAED;EACA,MAAMrD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMsD,MAAyB,GAAG,EAAE;;IAEpC;IACA,IAAIlH,QAAQ,CAACmH,iBAAiB,IAAI1G,aAAa,IAAIA,aAAa,CAACoD,MAAM,GAAG,CAAC,EAAE;MAC3E,MAAMuD,qBAAqB,GAAG3G,aAAa,CAAC0F,MAAM,CAACkB,YAAY,IAAI;QACjE,MAAMd,eAAe,GAAGvG,QAAQ,CAACsH,sBAAsB,CAACzD,MAAM,KAAK,CAAC,IAClE7D,QAAQ,CAACsH,sBAAsB,CAACZ,QAAQ,CAACW,YAAY,CAACZ,WAAW,CAAC;QACpE,OAAOF,eAAe;MACxB,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,EAAEhH,QAAQ,CAACmB,gBAAgB,CAAC;MAEtCiG,qBAAqB,CAAChD,OAAO,CAAEiD,YAAY,IACzCH,MAAM,CAACK,IAAI,cACTjI,OAAA,CAACJ,cAAc;QAEbmI,YAAY,EAAEA;MAAa,GADtB,gBAAgBA,YAAY,CAACG,eAAe,IAAI1H,UAAU,EAAE;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CACH,CACD,CAAC;IACJ;;IAEA;IACA,MAAMC,cAAc,GAAGhC,iBAAiB,CAAC,CAAC;IAC1C,IAAIgC,cAAc,CAAChE,MAAM,GAAG,CAAC,EAAE;MAC7BgE,cAAc,CAACzD,OAAO,CAAEgC,KAAK,IAC3Bc,MAAM,CAACK,IAAI,cACTjI,OAAA,CAACH,eAAe;QAEdiH,KAAK,EAAEA;MAAM,GADR,SAASA,KAAK,CAAC0B,WAAW,IAAIhI,UAAU,EAAE;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhD,CACH,CACD,CAAC;IACJ;IAEA,OAAOV,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGtD,kBAAkB,CAAC,CAAC;EACnC,MAAMmE,SAAS,GAAGpH,oBAAoB,IAAIY,aAAa;EACvD,MAAMyG,QAAQ,GAAGnH,kBAAkB,IAAIW,WAAW;EAElD,oBACElC,OAAA;IAAK2I,KAAK,EAAE;MACVC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE,iDAAiD;MAC7DC,UAAU,EAAE,mDAAmD;MAAE;MACjEC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEAnJ,OAAA;MAAK2I,KAAK,EAAE;QACVI,UAAU,EAAE,2BAA2B;QACvCF,OAAO,EAAE,WAAW;QACpBO,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,KAAK;QACjBJ,KAAK,EAAE,SAAS;QAChBK,YAAY,EAAE,8BAA8B;QAC5CC,SAAS,EAAE;MACb,CAAE;MAAAL,QAAA,EACCrD,cAAc,CAAC;IAAC;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGNtI,OAAA;MAAMyJ,SAAS,EAAC,YAAY;MAAAN,QAAA,GAEzBV,SAAS,iBACRzI,OAAA;QAAKyJ,SAAS,EAAC,YAAY;QAAAN,QAAA,gBACzBnJ,OAAA;UAAKyJ,SAAS,EAAC;QAAoB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CtI,OAAA;UAAAmJ,QAAA,EAAK;QAA0C;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,EAGAI,QAAQ,IAAI,CAACD,SAAS,iBACrBzI,OAAA;QAAKyJ,SAAS,EAAC,UAAU;QAAAN,QAAA,gBACvBnJ,OAAA;UAAK2I,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEK,YAAY,EAAE,MAAM;YAAEJ,UAAU,EAAE,MAAM;YAAEJ,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EAAC;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzGtI,OAAA;UAAAmJ,QAAA,EAAK;QAAsB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjCtI,OAAA;UAAK2I,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEM,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAT,QAAA,EAAC;QAEnE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5H,QAAQ,CAAC+C,eAAe,IAAI/C,QAAQ,CAACkD,gBAAgB,KACpDpB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE/B,QAAQ,CAACkD,gBAAgB,CAAC,eACtE5D,OAAA;QAAK2I,KAAK,EAAE;UACVkB,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTlB,UAAU,EAAE,yBAAyB;UACrCG,KAAK,EAAE,OAAO;UACdgB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;QACb,CAAE;QAAApB,QAAA,gBACAnJ,OAAA;UAAK2I,KAAK,EAAE;YACVU,QAAQ,EAAE,MAAM;YAChBK,YAAY,EAAE,MAAM;YACpBa,SAAS,EAAE,6BAA6B;YACxCjB,UAAU,EAAE,MAAM;YAClBJ,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,EAAC;QAEH;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtI,OAAA;UAAK2I,KAAK,EAAE;YACVU,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBF,SAAS,EAAE,QAAQ;YACnBM,YAAY,EAAE,MAAM;YACpBc,UAAU,EAAE;UACd,CAAE;UAAArB,QAAA,EAAC;QAEH;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtI,OAAA;UAAK2I,KAAK,EAAE;YACVU,QAAQ,EAAE,MAAM;YAChBD,SAAS,EAAE,QAAQ;YACnBqB,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,KAAK;YACf3B,UAAU,EAAE,oBAAoB;YAChCF,OAAO,EAAE,MAAM;YACf8B,YAAY,EAAE;UAChB,CAAE;UAAAxB,QAAA,EACCzI,QAAQ,CAACkD;QAAgB;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,CACP,EAGA,CAACG,SAAS,IAAI,CAACC,QAAQ,IAAI,CAAChI,QAAQ,CAAC+C,eAAe,iBACnDzD,OAAA,CAAAE,SAAA;QAAAiJ,QAAA,EACGvB,MAAM,CAACrD,MAAM,GAAG,CAAC,gBAChBvE,OAAA,CAACF,WAAW;UACV8K,GAAG,EAAE3J,YAAa;UAClB4J,gBAAgB,EAAEnK,QAAQ,CAACoK,aAAc;UACzCC,YAAY,EAAE,IAAK;UACnBlK,SAAS,EAAEA,SAAS,IAAIH,QAAQ,CAACsK,QAAS;UAC1CC,aAAa,EAAEjK,eAAgB;UAAAmI,QAAA,EAE9BvB;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAEdtI,OAAA;UAAKyJ,SAAS,EAAC,eAAe;UAAAN,QAAA,gBAC5BnJ,OAAA;YAAK2I,KAAK,EAAE;cAAEU,QAAQ,EAAE,MAAM;cAAEK,YAAY,EAAE,MAAM;cAAEJ,UAAU,EAAE,MAAM;cAAEJ,KAAK,EAAE;YAAU,CAAE;YAAAC,QAAA,EAAC;UAAU;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9GtI,OAAA;YAAAmJ,QAAA,EAAK;UAAqC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDtI,OAAA;YAAK2I,KAAK,EAAE;cAAEU,QAAQ,EAAE,MAAM;cAAEM,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAAT,QAAA,EAAC;UAEnE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN,gBACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAKPtI,OAAA;MAAMkL,SAAS,EAAC,SAAS;MAACC,OAAO,EAAC;IAAK;MAAAhD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAAClI,EAAA,CA9ZID,SAAmB;EAAA,QAiBnBV,gBAAgB,EAchBC,WAAW;AAAA;AAAA0L,EAAA,GA/BXjL,SAAmB;AAgazB,eAAeA,SAAS;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}