{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { AdminAuthProvider } from './contexts/AdminAuthContext';\nimport { StudentAuthProvider } from './contexts/StudentAuthContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ProtectedRoute, PublicRoute } from './components/common';\nimport { ErrorBoundary } from './components/ErrorBoundary';\nimport './styles/commentDepth.css';\nimport { AdminLogin, StudentLogin, AdminRegister } from './pages';\nimport WelcomePage from './pages/WelcomePage';\nimport AdminLayout from './components/admin/layout/AdminLayout';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport NewsFeed from './components/common/NewsFeed';\nimport Calendar from './pages/admin/Calendar';\nimport PostManagement from './pages/admin/PostManagement';\nimport StudentManagement from './pages/admin/StudentManagement';\nimport Archive from './pages/admin/Archive';\nimport Settings from './pages/admin/Settings';\nimport ApiTest from './pages/debug/ApiTest';\n// Removed unused student pages - using unified NewsFeed and Profile Settings modal\n// import StudentDashboard from './pages/student/StudentDashboard'; // REMOVED\n// import StudentNewsfeed from './pages/student/StudentNewsfeed'; // Now using unified NewsFeed\n// import StudentSettings from './pages/student/StudentSettings'; // REMOVED\nimport TVDisplay from './pages/tv/TVDisplay';\nimport './App.css';\n\n// Smart redirect component that determines the appropriate login page based on the current path\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartRedirect = () => {\n  _s();\n  const location = useLocation();\n\n  // If the path starts with /student, redirect to student login\n  if (location.pathname.startsWith('/student')) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/student/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Default to admin login for all other paths\n  return /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/admin/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 10\n  }, this);\n};\n\n// Admin Routes Component with isolated auth context\n_s(SmartRedirect, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = SmartRedirect;\nconst AdminRoutes = () => /*#__PURE__*/_jsxDEV(AdminAuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/register\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          children: /*#__PURE__*/_jsxDEV(AdminRegister, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/newsfeed\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(NewsFeed, {\n          userRole: \"admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/calendar\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Calendar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/posts\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(PostManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/student-management\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(StudentManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/archive\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Archive, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/settings\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(AdminLayout, {\n          children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/debug\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(ApiTest, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"admin\",\n        children: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/admin/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 45,\n  columnNumber: 3\n}, this);\n\n// Student Routes Component with isolated auth context\n_c2 = AdminRoutes;\nconst StudentRoutes = () => /*#__PURE__*/_jsxDEV(StudentAuthProvider, {\n  children: /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(PublicRoute, {\n        restricted: true,\n        children: /*#__PURE__*/_jsxDEV(StudentLogin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/newsfeed\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(NewsFeed, {\n          userRole: \"student\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        requiredRole: \"student\",\n        children: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/student/newsfeed\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 159,\n  columnNumber: 3\n}, this);\n_c3 = StudentRoutes;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ToastProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(WelcomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/tv-display\",\n            element: /*#__PURE__*/_jsxDEV(TVDisplay, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/*\",\n            element: /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/student/*\",\n            element: /*#__PURE__*/_jsxDEV(StudentRoutes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(SmartRedirect, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n}\n_c4 = App;\nexport default App;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SmartRedirect\");\n$RefreshReg$(_c2, \"AdminRoutes\");\n$RefreshReg$(_c3, \"StudentRoutes\");\n$RefreshReg$(_c4, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "AdminAuth<PERSON><PERSON><PERSON>", "StudentAuthProvider", "ToastProvider", "ProtectedRoute", "PublicRoute", "Error<PERSON>ou<PERSON><PERSON>", "AdminLogin", "StudentLogin", "AdminRegister", "WelcomePage", "AdminLayout", "AdminDashboard", "NewsFeed", "Calendar", "PostManagement", "StudentManagement", "Archive", "Settings", "ApiTest", "TVDisplay", "jsxDEV", "_jsxDEV", "SmartRedirect", "_s", "location", "pathname", "startsWith", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AdminRoutes", "children", "path", "element", "restricted", "requiredRole", "userRole", "_c2", "StudentRoutes", "_c3", "App", "className", "_c4", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport { AuthProvider } from './contexts';\nimport { AdminAuthProvider } from './contexts/AdminAuthContext';\nimport { StudentAuthProvider } from './contexts/StudentAuthContext';\nimport { ToastProvider } from './contexts/ToastContext';\nimport { ProtectedRoute, PublicRoute } from './components/common';\nimport { ErrorBoundary } from './components/ErrorBoundary';\nimport './styles/commentDepth.css';\nimport { AdminLogin, StudentLogin, AdminRegister } from './pages';\nimport WelcomePage from './pages/WelcomePage';\nimport AdminLayout from './components/admin/layout/AdminLayout';\nimport AdminDashboard from './pages/admin/AdminDashboard';\nimport NewsFeed from './components/common/NewsFeed';\nimport Calendar from './pages/admin/Calendar';\nimport PostManagement from './pages/admin/PostManagement';\nimport StudentManagement from './pages/admin/StudentManagement';\nimport Archive from './pages/admin/Archive';\nimport Settings from './pages/admin/Settings';\nimport ApiTest from './pages/debug/ApiTest';\nimport StudentLayout from './components/student/layout/StudentLayout';\n// Removed unused student pages - using unified NewsFeed and Profile Settings modal\n// import StudentDashboard from './pages/student/StudentDashboard'; // REMOVED\n// import StudentNewsfeed from './pages/student/StudentNewsfeed'; // Now using unified NewsFeed\n// import StudentSettings from './pages/student/StudentSettings'; // REMOVED\nimport TVDisplay from './pages/tv/TVDisplay';\nimport TVControlPanel from './components/admin/tv-control/TVControlPanel';\nimport './App.css';\n\n// Smart redirect component that determines the appropriate login page based on the current path\nconst SmartRedirect: React.FC = () => {\n  const location = useLocation();\n\n  // If the path starts with /student, redirect to student login\n  if (location.pathname.startsWith('/student')) {\n    return <Navigate to=\"/student/login\" replace />;\n  }\n\n  // Default to admin login for all other paths\n  return <Navigate to=\"/admin/login\" replace />;\n};\n\n// Admin Routes Component with isolated auth context\nconst AdminRoutes: React.FC = () => (\n  <AdminAuthProvider>\n    <Routes>\n      {/* Admin public routes */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute restricted>\n            <AdminLogin />\n          </PublicRoute>\n        }\n      />\n\n      <Route\n        path=\"/register\"\n        element={\n          <PublicRoute restricted>\n            <ErrorBoundary>\n              <AdminRegister />\n            </ErrorBoundary>\n          </PublicRoute>\n        }\n      />\n\n      {/* Admin protected routes with layout */}\n      <Route\n        path=\"/dashboard\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <AdminDashboard />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/newsfeed\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <NewsFeed userRole=\"admin\" />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/calendar\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Calendar />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/posts\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <PostManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/student-management\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <StudentManagement />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/archive\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Archive />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/settings\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <AdminLayout>\n              <Settings />\n            </AdminLayout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/debug\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <ApiTest />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/\"\n        element={\n          <ProtectedRoute requiredRole=\"admin\">\n            <Navigate to=\"/admin/dashboard\" replace />\n          </ProtectedRoute>\n        }\n      />\n    </Routes>\n  </AdminAuthProvider>\n);\n\n// Student Routes Component with isolated auth context\nconst StudentRoutes: React.FC = () => (\n  <StudentAuthProvider>\n    <Routes>\n      {/* Student public routes */}\n      <Route\n        path=\"/login\"\n        element={\n          <PublicRoute restricted>\n            <StudentLogin />\n          </PublicRoute>\n        }\n      />\n\n      {/* Student protected routes - using unified NewsFeed only */}\n      <Route\n        path=\"/newsfeed\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <NewsFeed userRole=\"student\" />\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/\"\n        element={\n          <ProtectedRoute requiredRole=\"student\">\n            <Navigate to=\"/student/newsfeed\" replace />\n          </ProtectedRoute>\n        }\n      />\n    </Routes>\n  </StudentAuthProvider>\n);\n\nfunction App() {\n  return (\n    <ToastProvider>\n      <Router>\n        <div className=\"App\">\n          <Routes>\n            {/* Welcome page as default route */}\n            <Route path=\"/\" element={<WelcomePage />} />\n\n            {/* TV Display route - no authentication required */}\n            <Route path=\"/tv-display\" element={<TVDisplay />} />\n\n            {/* Admin routes with isolated auth context */}\n            <Route path=\"/admin/*\" element={<AdminRoutes />} />\n\n            {/* Student routes with isolated auth context */}\n            <Route path=\"/student/*\" element={<StudentRoutes />} />\n\n            {/* Catch all route - smart redirect based on path */}\n            <Route path=\"*\" element={<SmartRedirect />} />\n          </Routes>\n        </div>\n      </Router>\n    </ToastProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAEhG,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,EAAEC,WAAW,QAAQ,qBAAqB;AACjE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,OAAO,2BAA2B;AAClC,SAASC,UAAU,EAAEC,YAAY,EAAEC,aAAa,QAAQ,SAAS;AACjE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,uCAAuC;AAC/D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,OAAO,MAAM,uBAAuB;AAE3C;AACA;AACA;AACA;AACA,OAAOC,SAAS,MAAM,sBAAsB;AAE5C,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIyB,QAAQ,CAACC,QAAQ,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC5C,oBAAOL,OAAA,CAACvB,QAAQ;MAAC6B,EAAE,EAAC,gBAAgB;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjD;;EAEA;EACA,oBAAOX,OAAA,CAACvB,QAAQ;IAAC6B,EAAE,EAAC,cAAc;IAACC,OAAO;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC/C,CAAC;;AAED;AAAAT,EAAA,CAZMD,aAAuB;EAAA,QACVvB,WAAW;AAAA;AAAAkC,EAAA,GADxBX,aAAuB;AAa7B,MAAMY,WAAqB,GAAGA,CAAA,kBAC5Bb,OAAA,CAACrB,iBAAiB;EAAAmC,QAAA,eAChBd,OAAA,CAACzB,MAAM;IAAAuC,QAAA,gBAELd,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACjB,WAAW;QAACkC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAACf,UAAU;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAACjB,WAAW;QAACkC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAAChB,aAAa;UAAA8B,QAAA,eACZd,OAAA,CAACb,aAAa;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,YAAY;MACjBC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,WAAW;UAAAyB,QAAA,eACVd,OAAA,CAACV,cAAc;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACT,QAAQ;UAAC4B,QAAQ,EAAC;QAAO;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,WAAW;UAAAyB,QAAA,eACVd,OAAA,CAACR,QAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,WAAW;UAAAyB,QAAA,eACVd,OAAA,CAACP,cAAc;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,qBAAqB;MAC1BC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,WAAW;UAAAyB,QAAA,eACVd,OAAA,CAACN,iBAAiB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,UAAU;MACfC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,WAAW;UAAAyB,QAAA,eACVd,OAAA,CAACL,OAAO;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACX,WAAW;UAAAyB,QAAA,eACVd,OAAA,CAACJ,QAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACH,OAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,GAAG;MACRC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,OAAO;QAAAJ,QAAA,eAClCd,OAAA,CAACvB,QAAQ;UAAC6B,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACQ,CACpB;;AAED;AAAAS,GAAA,GAjHMP,WAAqB;AAkH3B,MAAMQ,aAAuB,GAAGA,CAAA,kBAC9BrB,OAAA,CAACpB,mBAAmB;EAAAkC,QAAA,eAClBd,OAAA,CAACzB,MAAM;IAAAuC,QAAA,gBAELd,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,QAAQ;MACbC,OAAO,eACLhB,OAAA,CAACjB,WAAW;QAACkC,UAAU;QAAAH,QAAA,eACrBd,OAAA,CAACd,YAAY;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,WAAW;MAChBC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACT,QAAQ;UAAC4B,QAAQ,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFX,OAAA,CAACxB,KAAK;MACJuC,IAAI,EAAC,GAAG;MACRC,OAAO,eACLhB,OAAA,CAAClB,cAAc;QAACoC,YAAY,EAAC,SAAS;QAAAJ,QAAA,eACpCd,OAAA,CAACvB,QAAQ;UAAC6B,EAAE,EAAC,mBAAmB;UAACC,OAAO;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACU,CACtB;AAACW,GAAA,GAhCID,aAAuB;AAkC7B,SAASE,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACnB,aAAa;IAAAiC,QAAA,eACZd,OAAA,CAAC1B,MAAM;MAAAwC,QAAA,eACLd,OAAA;QAAKwB,SAAS,EAAC,KAAK;QAAAV,QAAA,eAClBd,OAAA,CAACzB,MAAM;UAAAuC,QAAA,gBAELd,OAAA,CAACxB,KAAK;YAACuC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACZ,WAAW;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG5CX,OAAA,CAACxB,KAAK;YAACuC,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEhB,OAAA,CAACF,SAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpDX,OAAA,CAACxB,KAAK;YAACuC,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEhB,OAAA,CAACa,WAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnDX,OAAA,CAACxB,KAAK;YAACuC,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhB,OAAA,CAACqB,aAAa;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGvDX,OAAA,CAACxB,KAAK;YAACuC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhB,OAAA,CAACC,aAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACc,GAAA,GAzBQF,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAX,EAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}