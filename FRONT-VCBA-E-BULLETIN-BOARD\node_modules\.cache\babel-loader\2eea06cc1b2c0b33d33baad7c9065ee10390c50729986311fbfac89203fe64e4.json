{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\tv\\\\TVDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TVDisplay = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime());\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides = [];\n\n    // Add announcements\n    if (announcements && announcements.length > 0) {\n      announcements.forEach((announcement, index) => slides.push(/*#__PURE__*/_jsxDEV(TVAnnouncement, {\n        announcement: announcement\n      }, `announcement-${announcement.announcement_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event, index) => slides.push(/*#__PURE__*/_jsxDEV(TVCalendarEvent, {\n        event: event\n      }, `event-${event.calendar_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this)));\n    }\n    return slides;\n  };\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tv-display\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"tv-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"VCBA E-Bulletin Board\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subtitle\",\n        children: \"Villamor College of Business and Arts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-datetime\",\n      children: formatDateTime()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"tv-content\",\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading latest announcements and events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this), hasError && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '2rem'\n          },\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Unable to load content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            marginTop: '1rem',\n            opacity: 0.8\n          },\n          children: \"Please check your internet connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), !isLoading && !hasError && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: slides.length > 0 ? /*#__PURE__*/_jsxDEV(TVSlideshow, {\n          autoPlayInterval: 15000 // 15 seconds per slide\n          ,\n          showProgress: true,\n          children: slides\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-no-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '6rem',\n              marginBottom: '3rem'\n            },\n            children: \"\\uD83D\\uDCE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No announcements or events to display\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              marginTop: '2rem',\n              opacity: 0.7\n            },\n            children: \"Check back later for updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 15\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '1rem',\n        right: '2rem',\n        background: 'rgba(0, 0, 0, 0.6)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '20px',\n        fontSize: '1.4rem',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\uD83D\\uDD04\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Auto-refresh active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      httpEquiv: \"refresh\",\n      content: \"600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(TVDisplay, \"g4t3BZde6ugKPVZ9rjl6zcWQ4FE=\", false, function () {\n  return [useAnnouncements, useCalendar];\n});\n_c = TVDisplay;\nexport default TVDisplay;\nvar _c;\n$RefreshReg$(_c, \"TVDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAnnouncements", "useCalendar", "tvControlService", "TVAnnouncement", "TVCalendarEvent", "TVSlideshow", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TVDisplay", "_s", "currentTime", "setCurrentTime", "Date", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "settings", "setSettings", "getSettings", "isPlaying", "setIsPlaying", "currentSlide", "setCurrentSlide", "slideshowRef", "currentDate", "announcements", "loading", "announcementsLoading", "error", "announcementsError", "refresh", "refreshAnnouncements", "status", "page", "limit", "maxAnnouncements", "sort_by", "sort_order", "events", "eventsLoading", "eventsError", "refreshEvents", "timeInterval", "setInterval", "clearInterval", "refreshInterval", "prev", "reloadInterval", "window", "location", "reload", "formatDateTime", "options", "weekday", "year", "month", "day", "hour", "minute", "toLocaleDateString", "getUpcomingEvents", "today", "thirtyDaysFromNow", "setDate", "getDate", "filter", "event", "eventDate", "event_date", "is_active", "sort", "a", "b", "getTime", "createSlideContent", "slides", "length", "for<PERSON>ach", "announcement", "index", "push", "announcement_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "upcomingEvents", "calendar_id", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "style", "fontSize", "marginBottom", "marginTop", "opacity", "autoPlayInterval", "showProgress", "position", "bottom", "right", "background", "color", "padding", "borderRadius", "zIndex", "display", "alignItems", "gap", "httpEquiv", "content", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/tv/TVDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService, TVDisplaySettings, TVControlCommand } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\n\nconst TVDisplay: React.FC = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState<TVDisplaySettings>(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef<any>(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options: Intl.DateTimeFormatOptions = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime());\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides: React.ReactNode[] = [];\n    \n    // Add announcements\n    if (announcements && announcements.length > 0) {\n      announcements.forEach((announcement, index) => (\n        slides.push(\n          <TVAnnouncement \n            key={`announcement-${announcement.announcement_id}-${refreshKey}`} \n            announcement={announcement} \n          />\n        )\n      ));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event, index) => (\n        slides.push(\n          <TVCalendarEvent \n            key={`event-${event.calendar_id}-${refreshKey}`} \n            event={event} \n          />\n        )\n      ));\n    }\n\n    return slides;\n  };\n\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n\n  return (\n    <div className=\"tv-display\">\n      {/* Header with school branding */}\n      <header className=\"tv-header\">\n        <h1>VCBA E-Bulletin Board</h1>\n        <div className=\"subtitle\">Villamor College of Business and Arts</div>\n      </header>\n\n      {/* Current date and time */}\n      <div className=\"tv-datetime\">\n        {formatDateTime()}\n      </div>\n\n      {/* Main content area */}\n      <main className=\"tv-content\">\n        {/* Loading state */}\n        {isLoading && (\n          <div className=\"tv-loading\">\n            <div className=\"tv-loading-spinner\"></div>\n            <div>Loading latest announcements and events...</div>\n          </div>\n        )}\n\n        {/* Error state */}\n        {hasError && !isLoading && (\n          <div className=\"tv-error\">\n            <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>⚠️</div>\n            <div>Unable to load content</div>\n            <div style={{ fontSize: '2rem', marginTop: '1rem', opacity: 0.8 }}>\n              Please check your internet connection\n            </div>\n          </div>\n        )}\n\n        {/* Content slideshow */}\n        {!isLoading && !hasError && (\n          <>\n            {slides.length > 0 ? (\n              <TVSlideshow \n                autoPlayInterval={15000} // 15 seconds per slide\n                showProgress={true}\n              >\n                {slides}\n              </TVSlideshow>\n            ) : (\n              <div className=\"tv-no-content\">\n                <div style={{ fontSize: '6rem', marginBottom: '3rem' }}>📢</div>\n                <div>No announcements or events to display</div>\n                <div style={{ fontSize: '2rem', marginTop: '2rem', opacity: 0.7 }}>\n                  Check back later for updates\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </main>\n\n      {/* Footer with refresh indicator */}\n      <div style={{\n        position: 'fixed',\n        bottom: '1rem',\n        right: '2rem',\n        background: 'rgba(0, 0, 0, 0.6)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '20px',\n        fontSize: '1.4rem',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      }}>\n        <span>🔄</span>\n        <span>Auto-refresh active</span>\n      </div>\n\n      {/* Meta refresh as backup */}\n      <meta httpEquiv=\"refresh\" content=\"600\" />\n    </div>\n  );\n};\n\nexport default TVDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAA6C,iCAAiC;AACvG,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAoBK,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,YAAY,GAAGzB,MAAM,CAAM,IAAI,CAAC;;EAEtC;EACA,MAAM0B,WAAW,GAAG,IAAIX,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAM;IACJY,aAAa;IACbC,OAAO,EAAEC,oBAAoB;IAC7BC,KAAK,EAAEC,kBAAkB;IACzBC,OAAO,EAAEC;EACX,CAAC,GAAGhC,gBAAgB,CAAC;IACnBiC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAElB,QAAQ,CAACmB,gBAAgB;IAChCC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA,MAAM;IACJC,MAAM;IACNZ,OAAO,EAAEa,aAAa;IACtBX,KAAK,EAAEY,WAAW;IAClBV,OAAO,EAAEW;EACX,CAAC,GAAGzC,WAAW,CAACwB,WAAW,CAAC;;EAE5B;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM6C,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrC/B,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM+B,aAAa,CAACF,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMgD,eAAe,GAAGF,WAAW,CAAC,MAAM;MACxCZ,oBAAoB,CAAC,CAAC;MACtBU,aAAa,CAAC,CAAC;MACf1B,aAAa,CAAC+B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMF,aAAa,CAACC,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACd,oBAAoB,EAAEU,aAAa,CAAC,CAAC;;EAEzC;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMkD,cAAc,GAAGJ,WAAW,CAAC,MAAM;MACvCK,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMN,aAAa,CAACG,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAmC,GAAG;MAC1CC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD,OAAO/C,WAAW,CAACgD,kBAAkB,CAAC,OAAO,EAAEP,OAAO,CAAC;EACzD,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAIhD,IAAI,CAAC,CAAC;IACxB,MAAMiD,iBAAiB,GAAG,IAAIjD,IAAI,CAAC,CAAC;IACpCiD,iBAAiB,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE/C,OAAO1B,MAAM,CAAC2B,MAAM,CAACC,KAAK,IAAI;MAC5B,MAAMC,SAAS,GAAG,IAAItD,IAAI,CAACqD,KAAK,CAACE,UAAU,CAAC;MAC5C,OAAOD,SAAS,IAAIN,KAAK,IAAIM,SAAS,IAAIL,iBAAiB,IAAII,KAAK,CAACG,SAAS;IAChF,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI3D,IAAI,CAAC0D,CAAC,CAACH,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,GAAG,IAAI5D,IAAI,CAAC2D,CAAC,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC;EACxF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,MAAyB,GAAG,EAAE;;IAEpC;IACA,IAAIlD,aAAa,IAAIA,aAAa,CAACmD,MAAM,GAAG,CAAC,EAAE;MAC7CnD,aAAa,CAACoD,OAAO,CAAC,CAACC,YAAY,EAAEC,KAAK,KACxCJ,MAAM,CAACK,IAAI,cACT1E,OAAA,CAACJ,cAAc;QAEb4E,YAAY,EAAEA;MAAa,GADtB,gBAAgBA,YAAY,CAACG,eAAe,IAAInE,UAAU,EAAE;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CACH,CACD,CAAC;IACJ;;IAEA;IACA,MAAMC,cAAc,GAAG1B,iBAAiB,CAAC,CAAC;IAC1C,IAAI0B,cAAc,CAACV,MAAM,GAAG,CAAC,EAAE;MAC7BU,cAAc,CAACT,OAAO,CAAC,CAACX,KAAK,EAAEa,KAAK,KAClCJ,MAAM,CAACK,IAAI,cACT1E,OAAA,CAACH,eAAe;QAEd+D,KAAK,EAAEA;MAAM,GADR,SAASA,KAAK,CAACqB,WAAW,IAAIzE,UAAU,EAAE;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhD,CACH,CACD,CAAC;IACJ;IAEA,OAAOV,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,kBAAkB,CAAC,CAAC;EACnC,MAAMc,SAAS,GAAG7D,oBAAoB,IAAIY,aAAa;EACvD,MAAMkD,QAAQ,GAAG5D,kBAAkB,IAAIW,WAAW;EAElD,oBACElC,OAAA;IAAKoF,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzBrF,OAAA;MAAQoF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3BrF,OAAA;QAAAqF,QAAA,EAAI;MAAqB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B/E,OAAA;QAAKoF,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAqC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGT/E,OAAA;MAAKoF,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBxC,cAAc,CAAC;IAAC;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGN/E,OAAA;MAAMoF,SAAS,EAAC,YAAY;MAAAC,QAAA,GAEzBH,SAAS,iBACRlF,OAAA;QAAKoF,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBrF,OAAA;UAAKoF,SAAS,EAAC;QAAoB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C/E,OAAA;UAAAqF,QAAA,EAAK;QAA0C;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,EAGAI,QAAQ,IAAI,CAACD,SAAS,iBACrBlF,OAAA;QAAKoF,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBrF,OAAA;UAAKsF,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAH,QAAA,EAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChE/E,OAAA;UAAAqF,QAAA,EAAK;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjC/E,OAAA;UAAKsF,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAL,QAAA,EAAC;QAEnE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACG,SAAS,IAAI,CAACC,QAAQ,iBACtBnF,OAAA,CAAAE,SAAA;QAAAmF,QAAA,EACGhB,MAAM,CAACC,MAAM,GAAG,CAAC,gBAChBtE,OAAA,CAACF,WAAW;UACV6F,gBAAgB,EAAE,KAAM,CAAC;UAAA;UACzBC,YAAY,EAAE,IAAK;UAAAP,QAAA,EAElBhB;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAEd/E,OAAA;UAAKoF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BrF,OAAA;YAAKsF,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,EAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChE/E,OAAA;YAAAqF,QAAA,EAAK;UAAqC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD/E,OAAA;YAAKsF,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEE,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAAL,QAAA,EAAC;UAEnE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN,gBACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGP/E,OAAA;MAAKsF,KAAK,EAAE;QACVO,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,oBAAoB;QAChCC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,eAAe;QACxBC,YAAY,EAAE,MAAM;QACpBZ,QAAQ,EAAE,QAAQ;QAClBa,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAlB,QAAA,gBACArF,OAAA;QAAAqF,QAAA,EAAM;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACf/E,OAAA;QAAAqF,QAAA,EAAM;MAAmB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGN/E,OAAA;MAAMwG,SAAS,EAAC,SAAS;MAACC,OAAO,EAAC;IAAK;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA3MID,SAAmB;EAAA,QAiBnBV,gBAAgB,EAchBC,WAAW;AAAA;AAAAgH,EAAA,GA/BXvG,SAAmB;AA6MzB,eAAeA,SAAS;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}