{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVSlideshow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVSlideshow = ({\n  children,\n  autoPlayInterval = 12000,\n  // 12 seconds default\n  showProgress = true,\n  className = ''\n}) => {\n  _s();\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [progress, setProgress] = useState(0);\n  const totalSlides = children.length;\n\n  // Go to next slide\n  const nextSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    setCurrentSlide(prev => (prev + 1) % totalSlides);\n    setProgress(0);\n  }, [totalSlides]);\n\n  // Go to previous slide\n  const prevSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    setCurrentSlide(prev => (prev - 1 + totalSlides) % totalSlides);\n    setProgress(0);\n  }, [totalSlides]);\n\n  // Go to specific slide\n  const goToSlide = useCallback(index => {\n    if (index >= 0 && index < totalSlides) {\n      setCurrentSlide(index);\n      setProgress(0);\n    }\n  }, [totalSlides]);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n    const interval = setInterval(() => {\n      nextSlide();\n    }, autoPlayInterval);\n    return () => clearInterval(interval);\n  }, [isPlaying, autoPlayInterval, nextSlide, totalSlides]);\n\n  // Progress bar animation\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        const increment = 100 / (autoPlayInterval / 100);\n        return prev + increment;\n      });\n    }, 100);\n    return () => clearInterval(progressInterval);\n  }, [currentSlide, isPlaying, autoPlayInterval, totalSlides]);\n\n  // Reset progress when slide changes\n  useEffect(() => {\n    setProgress(0);\n  }, [currentSlide]);\n\n  // Pause/resume on hover (optional for TV display)\n  const handleMouseEnter = () => {\n    setIsPlaying(false);\n  };\n  const handleMouseLeave = () => {\n    setIsPlaying(true);\n  };\n\n  // Handle keyboard navigation (for testing)\n  useEffect(() => {\n    const handleKeyPress = event => {\n      switch (event.key) {\n        case 'ArrowLeft':\n          prevSlide();\n          break;\n        case 'ArrowRight':\n          nextSlide();\n          break;\n        case ' ':\n          setIsPlaying(!isPlaying);\n          break;\n        default:\n          break;\n      }\n    };\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [nextSlide, prevSlide, isPlaying]);\n\n  // Don't render if no slides\n  if (totalSlides === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-no-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          marginBottom: '2rem'\n        },\n        children: \"\\uD83D\\uDCED\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"No content available to display\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Single slide - no slideshow needed\n  if (totalSlides === 1) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `tv-slideshow ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-slide active\",\n        children: children[0]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `tv-slideshow ${className}`,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [children.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `tv-slide ${index === currentSlide ? 'active' : index === (currentSlide - 1 + totalSlides) % totalSlides ? 'prev' : ''}`,\n      style: {\n        zIndex: index === currentSlide ? 10 : 1\n      },\n      children: child\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this)), showProgress && totalSlides > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-progress\",\n      children: children.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `tv-progress-dot ${index === currentSlide ? 'active' : ''}`,\n        onClick: () => goToSlide(index),\n        style: {\n          cursor: 'pointer',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: index === currentSlide && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            height: '100%',\n            width: `${Math.min(progress, 100)}%`,\n            background: 'rgba(255, 255, 255, 0.8)',\n            borderRadius: '50%',\n            transition: 'width 0.1s linear'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 17\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '2rem',\n        right: '3rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        zIndex: 1000\n      },\n      children: [currentSlide + 1, \" / \", totalSlides]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '2rem',\n        left: '3rem',\n        background: isPlaying ? 'rgba(39, 174, 96, 0.8)' : 'rgba(231, 76, 60, 0.8)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.6rem',\n        fontWeight: '500',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: isPlaying ? '▶️' : '⏸️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: isPlaying ? 'Auto-playing' : 'Paused'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '6rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'none',\n        // Hidden for TV display\n        gap: '2rem',\n        zIndex: 1000\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: prevSlide,\n        style: {\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '50%',\n          width: '60px',\n          height: '60px',\n          fontSize: '2rem',\n          cursor: 'pointer'\n        },\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setIsPlaying(!isPlaying),\n        style: {\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '50%',\n          width: '60px',\n          height: '60px',\n          fontSize: '1.5rem',\n          cursor: 'pointer'\n        },\n        children: isPlaying ? '⏸️' : '▶️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: nextSlide,\n        style: {\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '50%',\n          width: '60px',\n          height: '60px',\n          fontSize: '2rem',\n          cursor: 'pointer'\n        },\n        children: \"\\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(TVSlideshow, \"V8hNO5ubi0Y9ENJIS6Yz+Nn+Wrw=\");\n_c = TVSlideshow;\nexport default TVSlideshow;\nvar _c;\n$RefreshReg$(_c, \"TVSlideshow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "jsxDEV", "_jsxDEV", "TVSlideshow", "children", "autoPlayInterval", "showProgress", "className", "_s", "currentSlide", "setCurrentSlide", "isPlaying", "setIsPlaying", "progress", "setProgress", "totalSlides", "length", "nextSlide", "prev", "prevSlide", "goToSlide", "index", "interval", "setInterval", "clearInterval", "progressInterval", "increment", "handleMouseEnter", "handleMouseLeave", "handleKeyPress", "event", "key", "window", "addEventListener", "removeEventListener", "style", "fontSize", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseEnter", "onMouseLeave", "map", "child", "zIndex", "_", "onClick", "cursor", "position", "overflow", "top", "left", "height", "width", "Math", "min", "background", "borderRadius", "transition", "right", "color", "padding", "fontWeight", "display", "alignItems", "gap", "bottom", "transform", "border", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVSlideshow.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport '../../styles/tv.css';\n\ninterface TVSlideshowProps {\n  children: React.ReactNode[];\n  autoPlayInterval?: number; // in milliseconds\n  showProgress?: boolean;\n  className?: string;\n  isPlaying?: boolean;\n  onSlideChange?: (slideIndex: number) => void;\n}\n\ninterface TVSlideshowRef {\n  nextSlide: () => void;\n  prevSlide: () => void;\n  goToSlide: (index: number) => void;\n  getCurrentSlide: () => number;\n}\n\nconst TVSlideshow: React.FC<TVSlideshowProps> = ({\n  children,\n  autoPlayInterval = 12000, // 12 seconds default\n  showProgress = true,\n  className = ''\n}) => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [progress, setProgress] = useState(0);\n\n  const totalSlides = children.length;\n\n  // Go to next slide\n  const nextSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    setCurrentSlide((prev) => (prev + 1) % totalSlides);\n    setProgress(0);\n  }, [totalSlides]);\n\n  // Go to previous slide\n  const prevSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);\n    setProgress(0);\n  }, [totalSlides]);\n\n  // Go to specific slide\n  const goToSlide = useCallback((index: number) => {\n    if (index >= 0 && index < totalSlides) {\n      setCurrentSlide(index);\n      setProgress(0);\n    }\n  }, [totalSlides]);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n\n    const interval = setInterval(() => {\n      nextSlide();\n    }, autoPlayInterval);\n\n    return () => clearInterval(interval);\n  }, [isPlaying, autoPlayInterval, nextSlide, totalSlides]);\n\n  // Progress bar animation\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n\n    const progressInterval = setInterval(() => {\n      setProgress((prev) => {\n        const increment = 100 / (autoPlayInterval / 100);\n        return prev + increment;\n      });\n    }, 100);\n\n    return () => clearInterval(progressInterval);\n  }, [currentSlide, isPlaying, autoPlayInterval, totalSlides]);\n\n  // Reset progress when slide changes\n  useEffect(() => {\n    setProgress(0);\n  }, [currentSlide]);\n\n  // Pause/resume on hover (optional for TV display)\n  const handleMouseEnter = () => {\n    setIsPlaying(false);\n  };\n\n  const handleMouseLeave = () => {\n    setIsPlaying(true);\n  };\n\n  // Handle keyboard navigation (for testing)\n  useEffect(() => {\n    const handleKeyPress = (event: KeyboardEvent) => {\n      switch (event.key) {\n        case 'ArrowLeft':\n          prevSlide();\n          break;\n        case 'ArrowRight':\n          nextSlide();\n          break;\n        case ' ':\n          setIsPlaying(!isPlaying);\n          break;\n        default:\n          break;\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [nextSlide, prevSlide, isPlaying]);\n\n  // Don't render if no slides\n  if (totalSlides === 0) {\n    return (\n      <div className=\"tv-no-content\">\n        <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>📭</div>\n        <div>No content available to display</div>\n      </div>\n    );\n  }\n\n  // Single slide - no slideshow needed\n  if (totalSlides === 1) {\n    return (\n      <div className={`tv-slideshow ${className}`}>\n        <div className=\"tv-slide active\">\n          {children[0]}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={`tv-slideshow ${className}`}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {/* Slides */}\n      {children.map((child, index) => (\n        <div\n          key={index}\n          className={`tv-slide ${\n            index === currentSlide \n              ? 'active' \n              : index === (currentSlide - 1 + totalSlides) % totalSlides \n                ? 'prev' \n                : ''\n          }`}\n          style={{\n            zIndex: index === currentSlide ? 10 : 1\n          }}\n        >\n          {child}\n        </div>\n      ))}\n\n      {/* Progress indicators */}\n      {showProgress && totalSlides > 1 && (\n        <div className=\"tv-progress\">\n          {children.map((_, index) => (\n            <div\n              key={index}\n              className={`tv-progress-dot ${index === currentSlide ? 'active' : ''}`}\n              onClick={() => goToSlide(index)}\n              style={{\n                cursor: 'pointer',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n            >\n              {/* Progress bar for current slide */}\n              {index === currentSlide && (\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    height: '100%',\n                    width: `${Math.min(progress, 100)}%`,\n                    background: 'rgba(255, 255, 255, 0.8)',\n                    borderRadius: '50%',\n                    transition: 'width 0.1s linear'\n                  }}\n                />\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Slide counter */}\n      <div style={{\n        position: 'fixed',\n        top: '2rem',\n        right: '3rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        zIndex: 1000\n      }}>\n        {currentSlide + 1} / {totalSlides}\n      </div>\n\n      {/* Auto-play indicator */}\n      <div style={{\n        position: 'fixed',\n        top: '2rem',\n        left: '3rem',\n        background: isPlaying ? 'rgba(39, 174, 96, 0.8)' : 'rgba(231, 76, 60, 0.8)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.6rem',\n        fontWeight: '500',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      }}>\n        <span>{isPlaying ? '▶️' : '⏸️'}</span>\n        <span>{isPlaying ? 'Auto-playing' : 'Paused'}</span>\n      </div>\n\n      {/* Navigation arrows (hidden by default, can be shown for testing) */}\n      <div style={{\n        position: 'fixed',\n        bottom: '6rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'none', // Hidden for TV display\n        gap: '2rem',\n        zIndex: 1000\n      }}>\n        <button\n          onClick={prevSlide}\n          style={{\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '50%',\n            width: '60px',\n            height: '60px',\n            fontSize: '2rem',\n            cursor: 'pointer'\n          }}\n        >\n          ←\n        </button>\n        <button\n          onClick={() => setIsPlaying(!isPlaying)}\n          style={{\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '50%',\n            width: '60px',\n            height: '60px',\n            fontSize: '1.5rem',\n            cursor: 'pointer'\n          }}\n        >\n          {isPlaying ? '⏸️' : '▶️'}\n        </button>\n        <button\n          onClick={nextSlide}\n          style={{\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '50%',\n            width: '60px',\n            height: '60px',\n            fontSize: '2rem',\n            cursor: 'pointer'\n          }}\n        >\n          →\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default TVSlideshow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAyC,OAAO;AAChG,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkB7B,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,gBAAgB,GAAG,KAAK;EAAE;EAC1BC,YAAY,GAAG,IAAI;EACnBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EAE3C,MAAMiB,WAAW,GAAGX,QAAQ,CAACY,MAAM;;EAEnC;EACA,MAAMC,SAAS,GAAGjB,WAAW,CAAC,MAAM;IAClC,IAAIe,WAAW,KAAK,CAAC,EAAE;IACvBL,eAAe,CAAEQ,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,WAAW,CAAC;IACnDD,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMI,SAAS,GAAGnB,WAAW,CAAC,MAAM;IAClC,IAAIe,WAAW,KAAK,CAAC,EAAE;IACvBL,eAAe,CAAEQ,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,GAAGH,WAAW,IAAIA,WAAW,CAAC;IACjED,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMK,SAAS,GAAGpB,WAAW,CAAEqB,KAAa,IAAK;IAC/C,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGN,WAAW,EAAE;MACrCL,eAAe,CAACW,KAAK,CAAC;MACtBP,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;;EAEjB;EACAhB,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,SAAS,IAAII,WAAW,IAAI,CAAC,EAAE;IAEpC,MAAMO,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCN,SAAS,CAAC,CAAC;IACb,CAAC,EAAEZ,gBAAgB,CAAC;IAEpB,OAAO,MAAMmB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACX,SAAS,EAAEN,gBAAgB,EAAEY,SAAS,EAAEF,WAAW,CAAC,CAAC;;EAEzD;EACAhB,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,SAAS,IAAII,WAAW,IAAI,CAAC,EAAE;IAEpC,MAAMU,gBAAgB,GAAGF,WAAW,CAAC,MAAM;MACzCT,WAAW,CAAEI,IAAI,IAAK;QACpB,MAAMQ,SAAS,GAAG,GAAG,IAAIrB,gBAAgB,GAAG,GAAG,CAAC;QAChD,OAAOa,IAAI,GAAGQ,SAAS;MACzB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMF,aAAa,CAACC,gBAAgB,CAAC;EAC9C,CAAC,EAAE,CAAChB,YAAY,EAAEE,SAAS,EAAEN,gBAAgB,EAAEU,WAAW,CAAC,CAAC;;EAE5D;EACAhB,SAAS,CAAC,MAAM;IACde,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMgB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACAb,SAAS,CAAC,MAAM;IACd,MAAM8B,cAAc,GAAIC,KAAoB,IAAK;MAC/C,QAAQA,KAAK,CAACC,GAAG;QACf,KAAK,WAAW;UACdZ,SAAS,CAAC,CAAC;UACX;QACF,KAAK,YAAY;UACfF,SAAS,CAAC,CAAC;UACX;QACF,KAAK,GAAG;UACNL,YAAY,CAAC,CAACD,SAAS,CAAC;UACxB;QACF;UACE;MACJ;IACF,CAAC;IAEDqB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,cAAc,CAAC;IAClD,OAAO,MAAMG,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEL,cAAc,CAAC;EACpE,CAAC,EAAE,CAACZ,SAAS,EAAEE,SAAS,EAAER,SAAS,CAAC,CAAC;;EAErC;EACA,IAAII,WAAW,KAAK,CAAC,EAAE;IACrB,oBACEb,OAAA;MAAKK,SAAS,EAAC,eAAe;MAAAH,QAAA,gBAC5BF,OAAA;QAAKiC,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAjC,QAAA,EAAC;MAAE;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChEvC,OAAA;QAAAE,QAAA,EAAK;MAA+B;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAEV;;EAEA;EACA,IAAI1B,WAAW,KAAK,CAAC,EAAE;IACrB,oBACEb,OAAA;MAAKK,SAAS,EAAE,gBAAgBA,SAAS,EAAG;MAAAH,QAAA,eAC1CF,OAAA;QAAKK,SAAS,EAAC,iBAAiB;QAAAH,QAAA,EAC7BA,QAAQ,CAAC,CAAC;MAAC;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvC,OAAA;IACEK,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCmC,YAAY,EAAEf,gBAAiB;IAC/BgB,YAAY,EAAEf,gBAAiB;IAAAxB,QAAA,GAG9BA,QAAQ,CAACwC,GAAG,CAAC,CAACC,KAAK,EAAExB,KAAK,kBACzBnB,OAAA;MAEEK,SAAS,EAAE,YACTc,KAAK,KAAKZ,YAAY,GAClB,QAAQ,GACRY,KAAK,KAAK,CAACZ,YAAY,GAAG,CAAC,GAAGM,WAAW,IAAIA,WAAW,GACtD,MAAM,GACN,EAAE,EACP;MACHoB,KAAK,EAAE;QACLW,MAAM,EAAEzB,KAAK,KAAKZ,YAAY,GAAG,EAAE,GAAG;MACxC,CAAE;MAAAL,QAAA,EAEDyC;IAAK,GAZDxB,KAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAaP,CACN,CAAC,EAGDnC,YAAY,IAAIS,WAAW,GAAG,CAAC,iBAC9Bb,OAAA;MAAKK,SAAS,EAAC,aAAa;MAAAH,QAAA,EACzBA,QAAQ,CAACwC,GAAG,CAAC,CAACG,CAAC,EAAE1B,KAAK,kBACrBnB,OAAA;QAEEK,SAAS,EAAE,mBAAmBc,KAAK,KAAKZ,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;QACvEuC,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAACC,KAAK,CAAE;QAChCc,KAAK,EAAE;UACLc,MAAM,EAAE,SAAS;UACjBC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE;QACZ,CAAE;QAAA/C,QAAA,EAGDiB,KAAK,KAAKZ,YAAY,iBACrBP,OAAA;UACEiC,KAAK,EAAE;YACLe,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC5C,QAAQ,EAAE,GAAG,CAAC,GAAG;YACpC6C,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE;UACd;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACF,GAvBIpB,KAAK;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDvC,OAAA;MAAKiC,KAAK,EAAE;QACVe,QAAQ,EAAE,OAAO;QACjBE,GAAG,EAAE,MAAM;QACXS,KAAK,EAAE,MAAM;QACbH,UAAU,EAAE,oBAAoB;QAChCI,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,WAAW;QACpBJ,YAAY,EAAE,MAAM;QACpBvB,QAAQ,EAAE,QAAQ;QAClB4B,UAAU,EAAE,KAAK;QACjBlB,MAAM,EAAE;MACV,CAAE;MAAA1C,QAAA,GACCK,YAAY,GAAG,CAAC,EAAC,KAAG,EAACM,WAAW;IAAA;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAGNvC,OAAA;MAAKiC,KAAK,EAAE;QACVe,QAAQ,EAAE,OAAO;QACjBE,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,MAAM;QACZK,UAAU,EAAE/C,SAAS,GAAG,wBAAwB,GAAG,wBAAwB;QAC3EmD,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,WAAW;QACpBJ,YAAY,EAAE,MAAM;QACpBvB,QAAQ,EAAE,QAAQ;QAClB4B,UAAU,EAAE,KAAK;QACjBlB,MAAM,EAAE,IAAI;QACZmB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAA/D,QAAA,gBACAF,OAAA;QAAAE,QAAA,EAAOO,SAAS,GAAG,IAAI,GAAG;MAAI;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACtCvC,OAAA;QAAAE,QAAA,EAAOO,SAAS,GAAG,cAAc,GAAG;MAAQ;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNvC,OAAA;MAAKiC,KAAK,EAAE;QACVe,QAAQ,EAAE,OAAO;QACjBkB,MAAM,EAAE,MAAM;QACdf,IAAI,EAAE,KAAK;QACXgB,SAAS,EAAE,kBAAkB;QAC7BJ,OAAO,EAAE,MAAM;QAAE;QACjBE,GAAG,EAAE,MAAM;QACXrB,MAAM,EAAE;MACV,CAAE;MAAA1C,QAAA,gBACAF,OAAA;QACE8C,OAAO,EAAE7B,SAAU;QACnBgB,KAAK,EAAE;UACLuB,UAAU,EAAE,oBAAoB;UAChCI,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdX,YAAY,EAAE,KAAK;UACnBJ,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdlB,QAAQ,EAAE,MAAM;UAChBa,MAAM,EAAE;QACV,CAAE;QAAA7C,QAAA,EACH;MAED;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvC,OAAA;QACE8C,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAAC,CAACD,SAAS,CAAE;QACxCwB,KAAK,EAAE;UACLuB,UAAU,EAAE,oBAAoB;UAChCI,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdX,YAAY,EAAE,KAAK;UACnBJ,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdlB,QAAQ,EAAE,QAAQ;UAClBa,MAAM,EAAE;QACV,CAAE;QAAA7C,QAAA,EAEDO,SAAS,GAAG,IAAI,GAAG;MAAI;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACTvC,OAAA;QACE8C,OAAO,EAAE/B,SAAU;QACnBkB,KAAK,EAAE;UACLuB,UAAU,EAAE,oBAAoB;UAChCI,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdX,YAAY,EAAE,KAAK;UACnBJ,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdlB,QAAQ,EAAE,MAAM;UAChBa,MAAM,EAAE;QACV,CAAE;QAAA7C,QAAA,EACH;MAED;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA7QIL,WAAuC;AAAAoE,EAAA,GAAvCpE,WAAuC;AA+Q7C,eAAeA,WAAW;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}