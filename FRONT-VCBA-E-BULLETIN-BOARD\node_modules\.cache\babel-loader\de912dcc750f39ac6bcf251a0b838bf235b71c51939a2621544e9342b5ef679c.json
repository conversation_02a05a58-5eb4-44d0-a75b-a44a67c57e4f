{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\";\nimport React from 'react';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Truncate content if too long for TV display\n  const truncateContent = (content, maxLength = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image\n    if (announcement.image_url) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_url && attachment.file_url.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          images.push({\n            url: attachment.file_url,\n            alt: `${announcement.title} - Image ${index + 2}`\n          });\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `tv-announcement ${isUrgent ? 'urgent' : ''}`,\n    children: [isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#e74c3c',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '10px',\n        marginBottom: '2rem',\n        fontSize: '2rem',\n        fontWeight: 'bold',\n        textAlign: 'center',\n        textTransform: 'uppercase',\n        letterSpacing: '1px'\n      },\n      children: announcement.is_alert ? '🚨 IMPORTANT ALERT' : '📌 PINNED ANNOUNCEMENT'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"tv-announcement-title\",\n      children: announcement.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-announcement-content\",\n      children: truncateContent(announcement.content)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-announcement-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tv-announcement-category\",\n          style: {\n            backgroundColor: getCategoryColor()\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.6rem',\n              fontWeight: '600'\n            },\n            children: [\"\\uD83D\\uDCC5 \", formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.4rem',\n              opacity: 0.8\n            },\n            children: [\"\\uD83D\\uDD52 \", formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Posted by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: announcement.author_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "truncate<PERSON><PERSON><PERSON>", "content", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_url", "push", "url", "alt", "title", "attachments", "for<PERSON>ach", "attachment", "index", "file_url", "match", "className", "children", "style", "background", "color", "padding", "borderRadius", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "gap", "category_name", "backgroundColor", "flexDirection", "created_at", "opacity", "author_name", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Truncate content if too long for TV display\n  const truncateContent = (content: string, maxLength: number = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image\n    if (announcement.image_url) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_url && attachment.file_url.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          images.push({\n            url: attachment.file_url,\n            alt: `${announcement.title} - Image ${index + 2}`\n          });\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  return (\n    <div className={`tv-announcement ${isUrgent ? 'urgent' : ''}`}>\n      {/* Alert indicator for urgent announcements */}\n      {isUrgent && (\n        <div style={{\n          background: '#e74c3c',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '10px',\n          marginBottom: '2rem',\n          fontSize: '2rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '1px'\n        }}>\n          {announcement.is_alert ? '🚨 IMPORTANT ALERT' : '📌 PINNED ANNOUNCEMENT'}\n        </div>\n      )}\n\n      {/* Announcement title */}\n      <h2 className=\"tv-announcement-title\">\n        {announcement.title}\n      </h2>\n\n      {/* Announcement content */}\n      <div className=\"tv-announcement-content\">\n        {truncateContent(announcement.content)}\n      </div>\n\n      {/* Announcement metadata */}\n      <div className=\"tv-announcement-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <span \n              className=\"tv-announcement-category\"\n              style={{ backgroundColor: getCategoryColor() }}\n            >\n              {announcement.category_name}\n            </span>\n          )}\n\n          {/* Date and time */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n            <span style={{ fontSize: '1.6rem', fontWeight: '600' }}>\n              📅 {formatDate(announcement.created_at)}\n            </span>\n            <span style={{ fontSize: '1.4rem', opacity: 0.8 }}>\n              🕒 {formatTime(announcement.created_at)}\n            </span>\n          </div>\n        </div>\n\n        {/* Author information */}\n        {announcement.author_name && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Posted by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {announcement.author_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{announcement.reaction_count} reactions</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{announcement.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAC1E;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,OAAe,EAAEC,SAAiB,GAAG,GAAG,KAAK;IACpE,IAAID,OAAO,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,OAAO;IAC/C,OAAOA,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EACvD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,YAAY,CAACqB,cAAc,EAAE;MAC/B,OAAOrB,YAAY,CAACqB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGtB,YAAY,CAACuB,QAAQ,IAAIvB,YAAY,CAACwB,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI1B,YAAY,CAAC2B,SAAS,EAAE;MAC1BD,MAAM,CAACE,IAAI,CAAC;QACVC,GAAG,EAAE7B,YAAY,CAAC2B,SAAS;QAC3BG,GAAG,EAAE,GAAG9B,YAAY,CAAC+B,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/B,YAAY,CAACgC,WAAW,EAAE;MAC5BhC,YAAY,CAACgC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,QAAQ,IAAIF,UAAU,CAACE,QAAQ,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACnFX,MAAM,CAACE,IAAI,CAAC;YACVC,GAAG,EAAEK,UAAU,CAACE,QAAQ;YACxBN,GAAG,EAAE,GAAG9B,YAAY,CAAC+B,KAAK,YAAYI,KAAK,GAAG,CAAC;UACjD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA,OAAOT,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;EAEtC,oBACE3B,OAAA;IAAKwC,SAAS,EAAE,mBAAmBhB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;IAAAiB,QAAA,GAE3DjB,QAAQ,iBACPxB,OAAA;MAAK0C,KAAK,EAAE;QACVC,UAAU,EAAE,SAAS;QACrBC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,WAAW;QACpBC,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,QAAQ;QACnBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE;MACjB,CAAE;MAAAX,QAAA,EACCvC,YAAY,CAACuB,QAAQ,GAAG,oBAAoB,GAAG;IAAwB;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACN,eAGDxD,OAAA;MAAIwC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAClCvC,YAAY,CAAC+B;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGLxD,OAAA;MAAKwC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EACrCzB,eAAe,CAACd,YAAY,CAACe,OAAO;IAAC;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAGNxD,OAAA;MAAKwC,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCzC,OAAA;QAAK0C,KAAK,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAlB,QAAA,GAEhEvC,YAAY,CAAC0D,aAAa,iBACzB5D,OAAA;UACEwC,SAAS,EAAC,0BAA0B;UACpCE,KAAK,EAAE;YAAEmB,eAAe,EAAEvC,gBAAgB,CAAC;UAAE,CAAE;UAAAmB,QAAA,EAE9CvC,YAAY,CAAC0D;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACP,eAGDxD,OAAA;UAAK0C,KAAK,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEK,aAAa,EAAE,QAAQ;YAAEH,GAAG,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBACtEzC,OAAA;YAAM0C,KAAK,EAAE;cAAEM,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAR,QAAA,GAAC,eACnD,EAACtC,UAAU,CAACD,YAAY,CAAC6D,UAAU,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACPxD,OAAA;YAAM0C,KAAK,EAAE;cAAEM,QAAQ,EAAE,QAAQ;cAAEgB,OAAO,EAAE;YAAI,CAAE;YAAAvB,QAAA,GAAC,eAC9C,EAAC7B,UAAU,CAACV,YAAY,CAAC6D,UAAU,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLtD,YAAY,CAAC+D,WAAW,iBACvBjE,OAAA;QAAK0C,KAAK,EAAE;UACVe,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXX,QAAQ,EAAE;QACZ,CAAE;QAAAP,QAAA,gBACAzC,OAAA;UAAM0C,KAAK,EAAE;YAAEsB,OAAO,EAAE;UAAI,CAAE;UAAAvB,QAAA,EAAC;QAAU;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChDxD,OAAA;UAAM0C,KAAK,EAAE;YAAEO,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAChCvC,YAAY,CAAC+D;QAAW;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACtD,YAAY,CAACgE,cAAc,IAAIhE,YAAY,CAACiE,aAAa,kBACzDnE,OAAA;MAAK0C,KAAK,EAAE;QACV0B,SAAS,EAAE,MAAM;QACjBvB,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCG,YAAY,EAAE,MAAM;QACpBW,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,MAAM;QACXX,QAAQ,EAAE;MACZ,CAAE;MAAAP,QAAA,GACCvC,YAAY,CAACgE,cAAc,IAAIhE,YAAY,CAACgE,cAAc,GAAG,CAAC,iBAC7DlE,OAAA;QAAK0C,KAAK,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBACnEzC,OAAA;UAAAyC,QAAA,EAAM;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfxD,OAAA;UAAAyC,QAAA,GAAOvC,YAAY,CAACgE,cAAc,EAAC,YAAU;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACN,EACAtD,YAAY,CAACiE,aAAa,IAAIjE,YAAY,CAACiE,aAAa,GAAG,CAAC,iBAC3DnE,OAAA;QAAK0C,KAAK,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBACnEzC,OAAA;UAAAyC,QAAA,EAAM;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfxD,OAAA;UAAAyC,QAAA,GAAOvC,YAAY,CAACiE,aAAa,EAAC,WAAS;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACa,EAAA,GApKIpE,cAA6C;AAsKnD,eAAeA,cAAc;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}