{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVCalendarEvent.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVCalendarEvent = ({\n  event\n}) => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color - keep original colors\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Keep original red for events\n  };\n\n  // Determine event type text\n  const getEventIcon = () => {\n    if (event.is_holiday) return 'HOLIDAY';\n    if (event.is_recurring) return 'RECURRING';\n    if (event.is_alert) return 'ALERT';\n    return 'EVENT';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // TV-friendly description truncation\n  const truncateDescription = (description, maxLength = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n  const isLongDescription = event.description && event.description.length > 200;\n\n  // Get event images\n  const getEventImages = () => {\n    const images = [];\n\n    // Check if event has images (from API response)\n    if (event.images && Array.isArray(event.images)) {\n      event.images.forEach((img, index) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getEventImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex(prev => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: isUpcoming() ? 'linear-gradient(135deg, #fefce8 0%, #ffffff 100%)' : 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: isUpcoming() ? '0 10px 30px rgba(245, 158, 11, 0.2)' : '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming() ? '4px solid #f59e0b' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block',\n      // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [isUpcoming() && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '20px',\n          fontSize: '1.6rem',\n          fontWeight: 'bold',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n          marginBottom: '1.5rem',\n          alignSelf: 'flex-start'\n        },\n        children: \"UPCOMING EVENT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            background: 'rgba(255, 255, 255, 0.9)',\n            padding: '0.8rem',\n            borderRadius: '15px',\n            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'\n          },\n          children: getEventIcon()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            flexWrap: 'wrap'\n          },\n          children: [event.is_holiday && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '15px',\n              fontSize: '1.4rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 4px 15px rgba(243, 156, 18, 0.3)'\n            },\n            children: \"HOLIDAY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), event.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '15px',\n              fontSize: '1.4rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)'\n            },\n            children: \"IMPORTANT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: images.length > 0 ? '3rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        },\n        children: event.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          margin: '1.5rem 0',\n          padding: '1.5rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          borderRadius: '15px',\n          border: '2px solid rgba(231, 76, 60, 0.2)',\n          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '1.8rem',\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '1rem',\n            borderRadius: '15px',\n            boxShadow: `0 6px 15px ${getCategoryColor()}40`,\n            fontWeight: 'bold'\n          },\n          children: \"DATE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.8rem',\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '2.2rem',\n              fontWeight: '700',\n              color: '#2c3e50'\n            },\n            children: formatDateRange()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n              background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n              padding: '0.6rem 1rem',\n              borderRadius: '10px',\n              display: 'inline-block'\n            },\n            children: getDaysUntilEvent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: images.length > 0 ? '2rem' : '2.6rem',\n          lineHeight: '1.6',\n          color: '#374151',\n          background: 'rgba(255, 255, 255, 0.8)',\n          padding: images.length > 0 ? '1.5rem' : '2rem',\n          borderRadius: '15px',\n          border: '2px solid rgba(231, 76, 60, 0.2)',\n          wordWrap: 'break-word',\n          flex: 1,\n          marginBottom: '1.5rem',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        },\n        children: truncateDescription(event.description, images.length > 0 ? 300 : 500)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '20px',\n            fontWeight: '700',\n            fontSize: '1.4rem',\n            boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            alignSelf: 'flex-start'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          },\n          children: event.created_by_name && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#e74c3c',\n              fontWeight: '600'\n            },\n            children: [\"By: \", event.created_by_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-description\",\n      children: truncateDescription(event.description)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: getCategoryColor(),\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            fontWeight: '600',\n            fontSize: '1.6rem'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), event.subcategory_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: event.subcategory_color || '#95a5a6',\n            color: 'white',\n            padding: '0.6rem 1.2rem',\n            borderRadius: '20px',\n            fontSize: '1.4rem'\n          },\n          children: event.subcategory_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this), event.is_recurring && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.6rem',\n            color: '#8e44ad'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [event.recurrence_pattern === 'yearly' && 'Yearly', event.recurrence_pattern === 'monthly' && 'Monthly', event.recurrence_pattern === 'weekly' && 'Weekly']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Organized by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: event.created_by_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '0 0 40%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        position: 'relative',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%',\n          height: '100%',\n          borderRadius: '0px',\n          // Removed border radius to show full image\n          overflow: 'hidden',\n          boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n          border: `4px solid ${getCategoryColor()}`,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: images[currentImageIndex].url,\n          alt: images[currentImageIndex].alt,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block',\n            transition: 'opacity 0.5s ease-in-out'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '1rem',\n            right: '1rem',\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '1.2rem',\n            fontWeight: '600'\n          },\n          children: [currentImageIndex + 1, \" / \", images.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 15\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '1rem',\n            right: '1rem',\n            background: 'rgba(231, 76, 60, 0.8)',\n            color: 'white',\n            padding: '0.5rem',\n            borderRadius: '50%',\n            fontSize: '0.8rem',\n            fontWeight: 'bold'\n          },\n          children: \"AUTO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(TVCalendarEvent, \"iwdYV/csWqs0gMEM0R8yiwCHnVs=\");\n_c = TVCalendarEvent;\nexport default TVCalendarEvent;\nvar _c;\n$RefreshReg$(_c, \"TVCalendarEvent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getImageUrl", "jsxDEV", "_jsxDEV", "TVCalendarEvent", "event", "_s", "currentImageIndex", "setCurrentImageIndex", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatDateRange", "startDate", "event_date", "end_date", "endDate", "startFormatted", "endFormatted", "getDaysUntilEvent", "today", "eventDate", "diffTime", "getTime", "diffDays", "Math", "ceil", "abs", "getCategoryColor", "category_color", "getEventIcon", "is_holiday", "is_recurring", "is_alert", "isUpcoming", "truncateDescription", "description", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "isLongDescription", "getEventImages", "images", "Array", "isArray", "for<PERSON>ach", "img", "index", "file_path", "imageUrl", "push", "url", "alt", "title", "interval", "setInterval", "prev", "clearInterval", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "maxHeight", "display", "gap", "alignItems", "children", "flex", "flexDirection", "justifyContent", "zIndex", "color", "fontSize", "fontWeight", "textTransform", "letterSpacing", "marginBottom", "alignSelf", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexWrap", "lineHeight", "textShadow", "wordWrap", "textAlign", "category_name", "created_by_name", "className", "subcategory_name", "subcategory_color", "recurrence_pattern", "opacity", "minHeight", "width", "height", "src", "objectFit", "transition", "onError", "e", "currentTarget", "bottom", "right", "top", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVCalendarEvent.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVCalendarEventProps {\n  event: CalendarEvent;\n}\n\nconst TVCalendarEvent: React.FC<TVCalendarEventProps> = ({ event }) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    \n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    \n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color - keep original colors\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Keep original red for events\n  };\n\n  // Determine event type text\n  const getEventIcon = () => {\n    if (event.is_holiday) return 'HOLIDAY';\n    if (event.is_recurring) return 'RECURRING';\n    if (event.is_alert) return 'ALERT';\n    return 'EVENT';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // TV-friendly description truncation\n  const truncateDescription = (description: string, maxLength: number = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n\n  const isLongDescription = event.description && event.description.length > 200;\n\n  // Get event images\n  const getEventImages = () => {\n    const images: { url: string; alt: string }[] = [];\n\n    // Check if event has images (from API response)\n    if ((event as any).images && Array.isArray((event as any).images)) {\n      (event as any).images.forEach((img: any, index: number) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getEventImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex((prev) => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  return (\n    <div style={{\n      background: isUpcoming()\n        ? 'linear-gradient(135deg, #fefce8 0%, #ffffff 100%)'\n        : 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: isUpcoming()\n        ? '0 10px 30px rgba(245, 158, 11, 0.2)'\n        : '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming()\n        ? '4px solid #f59e0b'\n        : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block', // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    }}>\n      {/* Content Section - Full width if no images, 60% if images exist */}\n      <div style={{\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        {/* Upcoming event indicator */}\n        {isUpcoming() && (\n          <div style={{\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.6rem',\n            fontWeight: 'bold',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n            marginBottom: '1.5rem',\n            alignSelf: 'flex-start'\n          }}>\n            UPCOMING EVENT\n          </div>\n        )}\n        {/* Event type indicator */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          marginBottom: '1.5rem'\n        }}>\n          <div style={{\n            fontSize: '3rem',\n            background: 'rgba(255, 255, 255, 0.9)',\n            padding: '0.8rem',\n            borderRadius: '15px',\n            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'\n          }}>\n            {getEventIcon()}\n          </div>\n\n          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n            {event.is_holiday && (\n              <span style={{\n                background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '15px',\n                fontSize: '1.4rem',\n                fontWeight: '700',\n                textTransform: 'uppercase',\n                letterSpacing: '1px',\n                boxShadow: '0 4px 15px rgba(243, 156, 18, 0.3)'\n              }}>\n                HOLIDAY\n              </span>\n            )}\n            {event.is_alert && (\n              <span style={{\n                background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '15px',\n                fontSize: '1.4rem',\n                fontWeight: '700',\n                textTransform: 'uppercase',\n                letterSpacing: '1px',\n                boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)'\n              }}>\n                IMPORTANT\n              </span>\n            )}\n          </div>\n        </div>\n\n        {/* Event title - Larger when no images */}\n        <h2 style={{\n          fontSize: images.length > 0 ? '3rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        }}>\n          {event.title}\n        </h2>\n\n        {/* Event date with countdown */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          margin: '1.5rem 0',\n          padding: '1.5rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          borderRadius: '15px',\n          border: '2px solid rgba(231, 76, 60, 0.2)',\n          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            fontSize: '1.8rem',\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '1rem',\n            borderRadius: '15px',\n            boxShadow: `0 6px 15px ${getCategoryColor()}40`,\n            fontWeight: 'bold'\n          }}>\n            DATE\n          </div>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.8rem', flex: 1 }}>\n            <span style={{\n              fontSize: '2.2rem',\n              fontWeight: '700',\n              color: '#2c3e50'\n            }}>\n              {formatDateRange()}\n            </span>\n            <span style={{\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n              background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n              padding: '0.6rem 1rem',\n              borderRadius: '10px',\n              display: 'inline-block'\n            }}>\n              {getDaysUntilEvent()}\n            </span>\n          </div>\n        </div>\n\n        {/* Event description - Larger when no images */}\n        {event.description && (\n          <div style={{\n            fontSize: images.length > 0 ? '2rem' : '2.6rem',\n            lineHeight: '1.6',\n            color: '#374151',\n            background: 'rgba(255, 255, 255, 0.8)',\n            padding: images.length > 0 ? '1.5rem' : '2rem',\n            borderRadius: '15px',\n            border: '2px solid rgba(231, 76, 60, 0.2)',\n            wordWrap: 'break-word',\n            flex: 1,\n            marginBottom: '1.5rem',\n            textAlign: images.length > 0 ? 'left' : 'center'\n          }}>\n            {truncateDescription(event.description, images.length > 0 ? 300 : 500)}\n          </div>\n        )}\n\n        {/* Category and metadata */}\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        }}>\n          {event.category_name && (\n            <div style={{\n              background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '20px',\n              fontWeight: '700',\n              fontSize: '1.4rem',\n              boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              alignSelf: 'flex-start'\n            }}>\n              {event.category_name}\n            </div>\n          )}\n\n          {/* Creator information */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          }}>\n            {event.created_by_name && (\n              <span style={{ color: '#e74c3c', fontWeight: '600' }}>\n                By: {event.created_by_name}\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Event description */}\n      {event.description && (\n        <div className=\"tv-event-description\">\n          {truncateDescription(event.description)}\n        </div>\n      )}\n\n      {/* Event metadata */}\n      <div className=\"tv-event-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {event.category_name && (\n            <span \n              style={{\n                background: getCategoryColor(),\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '25px',\n                fontWeight: '600',\n                fontSize: '1.6rem'\n              }}\n            >\n              {event.category_name}\n            </span>\n          )}\n\n          {/* Subcategory */}\n          {event.subcategory_name && (\n            <span style={{\n              background: event.subcategory_color || '#95a5a6',\n              color: 'white',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              fontSize: '1.4rem'\n            }}>\n              {event.subcategory_name}\n            </span>\n          )}\n\n          {/* Recurring indicator */}\n          {event.is_recurring && (\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              gap: '0.5rem',\n              fontSize: '1.6rem',\n              color: '#8e44ad'\n            }}>\n              <span>🔄</span>\n              <span>\n                {event.recurrence_pattern === 'yearly' && 'Yearly'}\n                {event.recurrence_pattern === 'monthly' && 'Monthly'}\n                {event.recurrence_pattern === 'weekly' && 'Weekly'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Created by information */}\n        {event.created_by_name && (\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Organized by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {event.created_by_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Right Side - Images (40% width) */}\n      {images.length > 0 && (\n        <div style={{\n          flex: '0 0 40%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          position: 'relative',\n          minHeight: '400px'\n        }}>\n          {/* Single Image or Current Image from Carousel */}\n          <div style={{\n            width: '100%',\n            height: '100%',\n            borderRadius: '0px', // Removed border radius to show full image\n            overflow: 'hidden',\n            boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n            border: `4px solid ${getCategoryColor()}`,\n            position: 'relative'\n          }}>\n            <img\n              src={images[currentImageIndex].url}\n              alt={images[currentImageIndex].alt}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                display: 'block',\n                transition: 'opacity 0.5s ease-in-out'\n              }}\n              onError={(e) => {\n                e.currentTarget.style.display = 'none';\n              }}\n            />\n\n            {/* Image counter for multiple images */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                bottom: '1rem',\n                right: '1rem',\n                background: 'rgba(0, 0, 0, 0.7)',\n                color: 'white',\n                padding: '0.5rem 1rem',\n                borderRadius: '20px',\n                fontSize: '1.2rem',\n                fontWeight: '600'\n              }}>\n                {currentImageIndex + 1} / {images.length}\n              </div>\n            )}\n\n            {/* Auto-rotation indicator */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                top: '1rem',\n                right: '1rem',\n                background: 'rgba(231, 76, 60, 0.8)',\n                color: 'white',\n                padding: '0.5rem',\n                borderRadius: '50%',\n                fontSize: '0.8rem',\n                fontWeight: 'bold'\n              }}>\n                AUTO\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVCalendarEvent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EAC7D;EACA,MAAMU,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAACP,KAAK,CAACe,UAAU,CAAC;IAE5C,IAAIf,KAAK,CAACgB,QAAQ,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAIV,IAAI,CAACP,KAAK,CAACgB,QAAQ,CAAC;MACxC,MAAME,cAAc,GAAGJ,SAAS,CAACN,kBAAkB,CAAC,OAAO,EAAE;QAC3DG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,MAAMO,YAAY,GAAGF,OAAO,CAACT,kBAAkB,CAAC,OAAO,EAAE;QACvDG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdF,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAO,GAAGQ,cAAc,MAAMC,YAAY,EAAE;IAC9C;IAEA,OAAOf,UAAU,CAACJ,KAAK,CAACe,UAAU,CAAC;EACrC,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACP,KAAK,CAACe,UAAU,CAAC;IAC5C,MAAMQ,QAAQ,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,UAAU;IACrC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAMA,QAAQ,OAAO;IAC9C,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW;IACvC,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,WAAW;EACzC,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI7B,KAAK,CAAC8B,cAAc,EAAE;MACxB,OAAO9B,KAAK,CAAC8B,cAAc;IAC7B;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI/B,KAAK,CAACgC,UAAU,EAAE,OAAO,SAAS;IACtC,IAAIhC,KAAK,CAACiC,YAAY,EAAE,OAAO,WAAW;IAC1C,IAAIjC,KAAK,CAACkC,QAAQ,EAAE,OAAO,OAAO;IAClC,OAAO,OAAO;EAChB,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMd,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACP,KAAK,CAACe,UAAU,CAAC;IAC5C,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3F,OAAOC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAGA,CAACC,WAAmB,EAAEC,SAAiB,GAAG,GAAG,KAAK;IAC5E,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,WAAW;IACvE,OAAOA,WAAW,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EAC3D,CAAC;EAED,MAAMC,iBAAiB,GAAG1C,KAAK,CAACqC,WAAW,IAAIrC,KAAK,CAACqC,WAAW,CAACE,MAAM,GAAG,GAAG;;EAE7E;EACA,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAsC,GAAG,EAAE;;IAEjD;IACA,IAAK5C,KAAK,CAAS4C,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAE9C,KAAK,CAAS4C,MAAM,CAAC,EAAE;MAChE5C,KAAK,CAAS4C,MAAM,CAACG,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAK;QACzD,IAAID,GAAG,CAACE,SAAS,EAAE;UACjB,MAAMC,QAAQ,GAAGvD,WAAW,CAACoD,GAAG,CAACE,SAAS,CAAC;UAC3C,IAAIC,QAAQ,EAAE;YACZP,MAAM,CAACQ,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGtD,KAAK,CAACuD,KAAK,YAAYN,KAAK,GAAG,CAAC;YAC1C,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOL,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,cAAc,CAAC,CAAC;;EAE/B;EACAhD,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,CAACL,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMiB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCtD,oBAAoB,CAAEuD,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAId,MAAM,CAACL,MAAM,CAAC;MAC5D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMoB,aAAa,CAACH,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACZ,MAAM,CAACL,MAAM,CAAC,CAAC;EAEnB,oBACEzC,OAAA;IAAK8D,KAAK,EAAE;MACVC,UAAU,EAAE1B,UAAU,CAAC,CAAC,GACpB,mDAAmD,GACnD,mDAAmD;MACvD2B,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE9B,UAAU,CAAC,CAAC,GACnB,qCAAqC,GACrC,gCAAgC;MACpC+B,MAAM,EAAE/B,UAAU,CAAC,CAAC,GAChB,mBAAmB,GACnB,aAAaN,gBAAgB,CAAC,CAAC,EAAE;MACrCsC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE1B,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;MAAE;MAC/CgC,GAAG,EAAE3B,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG;MACrCiC,UAAU,EAAE5B,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;IAC9C,CAAE;IAAAkC,QAAA,gBAEA3E,OAAA;MAAK8D,KAAK,EAAE;QACVc,IAAI,EAAE9B,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG;QACzC+B,OAAO,EAAE,MAAM;QACfK,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,eAAe;QAC/BT,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,GAECtC,UAAU,CAAC,CAAC,iBACXrC,OAAA;QAAK8D,KAAK,EAAE;UACVC,UAAU,EAAE,2CAA2C;UACvDiB,KAAK,EAAE,OAAO;UACdf,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,MAAM;UACpBiB,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,MAAM;UAClBC,aAAa,EAAE,WAAW;UAC1BC,aAAa,EAAE,KAAK;UACpBjB,SAAS,EAAE,mCAAmC;UAC9CkB,YAAY,EAAE,QAAQ;UACtBC,SAAS,EAAE;QACb,CAAE;QAAAX,QAAA,EAAC;MAEH;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAED1F,OAAA;QAAK8D,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,QAAQ;UACbY,YAAY,EAAE;QAChB,CAAE;QAAAV,QAAA,gBACA3E,OAAA;UAAK8D,KAAK,EAAE;YACVmB,QAAQ,EAAE,MAAM;YAChBlB,UAAU,EAAE,0BAA0B;YACtCE,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,MAAM;YACpBG,SAAS,EAAE;UACb,CAAE;UAAAQ,QAAA,EACC1C,YAAY,CAAC;QAAC;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN1F,OAAA;UAAK8D,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEkB,QAAQ,EAAE;UAAO,CAAE;UAAAhB,QAAA,GAC5DzE,KAAK,CAACgC,UAAU,iBACflC,OAAA;YAAM8D,KAAK,EAAE;cACXC,UAAU,EAAE,2CAA2C;cACvDiB,KAAK,EAAE,OAAO;cACdf,OAAO,EAAE,eAAe;cACxBD,YAAY,EAAE,MAAM;cACpBiB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBC,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE,KAAK;cACpBjB,SAAS,EAAE;YACb,CAAE;YAAAQ,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAxF,KAAK,CAACkC,QAAQ,iBACbpC,OAAA;YAAM8D,KAAK,EAAE;cACXC,UAAU,EAAE,2CAA2C;cACvDiB,KAAK,EAAE,OAAO;cACdf,OAAO,EAAE,eAAe;cACxBD,YAAY,EAAE,MAAM;cACpBiB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBC,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE,KAAK;cACpBjB,SAAS,EAAE;YACb,CAAE;YAAAQ,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1F,OAAA;QAAI8D,KAAK,EAAE;UACTmB,QAAQ,EAAEnC,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;UAC7CyC,UAAU,EAAE,KAAK;UACjBhB,MAAM,EAAE,cAAc;UACtBc,KAAK,EAAE,SAAS;UAChBY,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,8BAA8B;UAC1CC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAEjD,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAC1C,CAAE;QAAAkC,QAAA,EACCzE,KAAK,CAACuD;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGL1F,OAAA;QAAK8D,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,QAAQ;UACbP,MAAM,EAAE,UAAU;UAClBD,OAAO,EAAE,QAAQ;UACjBF,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,MAAM;UACpBI,MAAM,EAAE,kCAAkC;UAC1CD,SAAS,EAAE;QACb,CAAE;QAAAQ,QAAA,gBACA3E,OAAA;UAAK8D,KAAK,EAAE;YACVmB,QAAQ,EAAE,QAAQ;YAClBlB,UAAU,EAAE,2BAA2BhC,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrFiD,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,MAAM;YACfD,YAAY,EAAE,MAAM;YACpBG,SAAS,EAAE,cAAcpC,gBAAgB,CAAC,CAAC,IAAI;YAC/CmD,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1F,OAAA;UAAK8D,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEK,aAAa,EAAE,QAAQ;YAAEJ,GAAG,EAAE,QAAQ;YAAEG,IAAI,EAAE;UAAE,CAAE;UAAAD,QAAA,gBAC/E3E,OAAA;YAAM8D,KAAK,EAAE;cACXmB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBF,KAAK,EAAE;YACT,CAAE;YAAAL,QAAA,EACC5D,eAAe,CAAC;UAAC;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACP1F,OAAA;YAAM8D,KAAK,EAAE;cACXmB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBF,KAAK,EAAE3C,UAAU,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;cAC3C0B,UAAU,EAAE1B,UAAU,CAAC,CAAC,GAAG,wBAAwB,GAAG,0BAA0B;cAChF4B,OAAO,EAAE,aAAa;cACtBD,YAAY,EAAE,MAAM;cACpBQ,OAAO,EAAE;YACX,CAAE;YAAAG,QAAA,EACCrD,iBAAiB,CAAC;UAAC;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxF,KAAK,CAACqC,WAAW,iBAChBvC,OAAA;QAAK8D,KAAK,EAAE;UACVmB,QAAQ,EAAEnC,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ;UAC/CmD,UAAU,EAAE,KAAK;UACjBZ,KAAK,EAAE,SAAS;UAChBjB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAEnB,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAC9CuB,YAAY,EAAE,MAAM;UACpBI,MAAM,EAAE,kCAAkC;UAC1C0B,QAAQ,EAAE,YAAY;UACtBlB,IAAI,EAAE,CAAC;UACPS,YAAY,EAAE,QAAQ;UACtBU,SAAS,EAAEjD,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAC1C,CAAE;QAAAkC,QAAA,EACCrC,mBAAmB,CAACpC,KAAK,CAACqC,WAAW,EAAEO,MAAM,CAACL,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;MAAC;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACN,eAGD1F,OAAA;QAAK8D,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfK,aAAa,EAAE,QAAQ;UACvBJ,GAAG,EAAE;QACP,CAAE;QAAAE,QAAA,GACCzE,KAAK,CAAC8F,aAAa,iBAClBhG,OAAA;UAAK8D,KAAK,EAAE;YACVC,UAAU,EAAE,2BAA2BhC,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrFiD,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBkB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE,QAAQ;YAClBd,SAAS,EAAE,cAAcpC,gBAAgB,CAAC,CAAC,IAAI;YAC/CoD,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBE,SAAS,EAAE;UACb,CAAE;UAAAX,QAAA,EACCzE,KAAK,CAAC8F;QAAa;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACN,eAGD1F,OAAA;UAAK8D,KAAK,EAAE;YACVU,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACbkB,QAAQ,EAAE,MAAM;YAChBV,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,EACCzE,KAAK,CAAC+F,eAAe,iBACpBjG,OAAA;YAAM8D,KAAK,EAAE;cAAEkB,KAAK,EAAE,SAAS;cAAEE,UAAU,EAAE;YAAM,CAAE;YAAAP,QAAA,GAAC,MAChD,EAACzE,KAAK,CAAC+F,eAAe;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxF,KAAK,CAACqC,WAAW,iBAChBvC,OAAA;MAAKkG,SAAS,EAAC,sBAAsB;MAAAvB,QAAA,EAClCrC,mBAAmB,CAACpC,KAAK,CAACqC,WAAW;IAAC;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN,eAGD1F,OAAA;MAAKkG,SAAS,EAAC,eAAe;MAAAvB,QAAA,gBAC5B3E,OAAA;QAAK8D,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAO,CAAE;QAAAE,QAAA,GAEhEzE,KAAK,CAAC8F,aAAa,iBAClBhG,OAAA;UACE8D,KAAK,EAAE;YACLC,UAAU,EAAEhC,gBAAgB,CAAC,CAAC;YAC9BiD,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBkB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAEDzE,KAAK,CAAC8F;QAAa;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EAGAxF,KAAK,CAACiG,gBAAgB,iBACrBnG,OAAA;UAAM8D,KAAK,EAAE;YACXC,UAAU,EAAE7D,KAAK,CAACkG,iBAAiB,IAAI,SAAS;YAChDpB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBiB,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EACCzE,KAAK,CAACiG;QAAgB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP,EAGAxF,KAAK,CAACiC,YAAY,iBACjBnC,OAAA;UAAK8D,KAAK,EAAE;YACVU,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACbQ,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,gBACA3E,OAAA;YAAA2E,QAAA,EAAM;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACf1F,OAAA;YAAA2E,QAAA,GACGzE,KAAK,CAACmG,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EACjDnG,KAAK,CAACmG,kBAAkB,KAAK,SAAS,IAAI,SAAS,EACnDnG,KAAK,CAACmG,kBAAkB,KAAK,QAAQ,IAAI,QAAQ;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLxF,KAAK,CAAC+F,eAAe,iBACpBjG,OAAA;QAAK8D,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,MAAM;UACXQ,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,gBACA3E,OAAA;UAAM8D,KAAK,EAAE;YAAEwC,OAAO,EAAE;UAAI,CAAE;UAAA3B,QAAA,EAAC;QAAa;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnD1F,OAAA;UAAM8D,KAAK,EAAE;YAAEoB,UAAU,EAAE;UAAM,CAAE;UAAAP,QAAA,EAChCzE,KAAK,CAAC+F;QAAe;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL5C,MAAM,CAACL,MAAM,GAAG,CAAC,iBAChBzC,OAAA;MAAK8D,KAAK,EAAE;QACVc,IAAI,EAAE,SAAS;QACfJ,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBI,cAAc,EAAE,QAAQ;QACxBT,QAAQ,EAAE,UAAU;QACpBkC,SAAS,EAAE;MACb,CAAE;MAAA5B,QAAA,eAEA3E,OAAA;QAAK8D,KAAK,EAAE;UACV0C,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdzC,YAAY,EAAE,KAAK;UAAE;UACrBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE,aAAarC,gBAAgB,CAAC,CAAC,EAAE;UACzCsC,QAAQ,EAAE;QACZ,CAAE;QAAAM,QAAA,gBACA3E,OAAA;UACE0G,GAAG,EAAE5D,MAAM,CAAC1C,iBAAiB,CAAC,CAACmD,GAAI;UACnCC,GAAG,EAAEV,MAAM,CAAC1C,iBAAiB,CAAC,CAACoD,GAAI;UACnCM,KAAK,EAAE;YACL0C,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdE,SAAS,EAAE,OAAO;YAClBnC,OAAO,EAAE,OAAO;YAChBoC,UAAU,EAAE;UACd,CAAE;UACFC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAACjD,KAAK,CAACU,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGD5C,MAAM,CAACL,MAAM,GAAG,CAAC,iBAChBzC,OAAA;UAAK8D,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpB2C,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACblD,UAAU,EAAE,oBAAoB;YAChCiB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,aAAa;YACtBD,YAAY,EAAE,MAAM;YACpBiB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,GACCvE,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAAC0C,MAAM,CAACL,MAAM;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN,EAGA5C,MAAM,CAACL,MAAM,GAAG,CAAC,iBAChBzC,OAAA;UAAK8D,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpB6C,GAAG,EAAE,MAAM;YACXD,KAAK,EAAE,MAAM;YACblD,UAAU,EAAE,wBAAwB;YACpCiB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,KAAK;YACnBiB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvF,EAAA,CAheIF,eAA+C;AAAAkH,EAAA,GAA/ClH,eAA+C;AAkerD,eAAeA,eAAe;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}