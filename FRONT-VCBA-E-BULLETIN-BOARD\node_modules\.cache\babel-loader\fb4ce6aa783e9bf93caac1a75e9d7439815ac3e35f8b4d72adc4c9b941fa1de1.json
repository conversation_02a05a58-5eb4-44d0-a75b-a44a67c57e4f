{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVCalendarEvent.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVCalendarEvent = ({\n  event\n}) => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const descriptionRef = useRef(null);\n  const [shouldScroll, setShouldScroll] = useState(false);\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color - keep original colors\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Keep original red for events\n  };\n\n  // Determine event type text\n  const getEventIcon = () => {\n    if (event.is_holiday) return 'HOLIDAY';\n    if (event.is_recurring) return 'RECURRING';\n    if (event.is_alert) return 'ALERT';\n    return 'EVENT';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // No truncation needed - using auto-scroll instead\n\n  // Get event images\n  const getEventImages = () => {\n    const images = [];\n\n    // Check if event has images (from API response)\n    if (event.images && Array.isArray(event.images)) {\n      event.images.forEach((img, index) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getEventImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex(prev => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  // Check if description needs scrolling and set up auto-scroll\n  useEffect(() => {\n    const checkScrollNeed = () => {\n      if (descriptionRef.current && event.description) {\n        const element = descriptionRef.current;\n        const needsScroll = element.scrollHeight > element.clientHeight;\n        setShouldScroll(needsScroll);\n        if (needsScroll) {\n          // Start auto-scroll after 2 seconds\n          const startScrollTimeout = setTimeout(() => {\n            element.style.animation = 'none'; // Reset animation\n            void element.offsetHeight; // Trigger reflow (void to satisfy ESLint)\n            element.style.animation = `autoScroll ${Math.max(10, element.scrollHeight / 20)}s linear infinite`;\n          }, 2000);\n          return () => clearTimeout(startScrollTimeout);\n        }\n      }\n    };\n    checkScrollNeed();\n  }, [event.description, images.length]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: isUpcoming() ? 'linear-gradient(135deg, #fefce8 0%, #ffffff 100%)' : 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: isUpcoming() ? '0 10px 30px rgba(245, 158, 11, 0.2)' : '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming() ? '4px solid #f59e0b' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block',\n      // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [isUpcoming() && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '20px',\n          fontSize: '1.6rem',\n          fontWeight: 'bold',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n          marginBottom: '1.5rem',\n          alignSelf: 'flex-start'\n        },\n        children: \"UPCOMING EVENT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          marginBottom: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '1.2rem',\n            background: 'rgba(255, 255, 255, 0.9)',\n            padding: '0.8rem',\n            borderRadius: '15px',\n            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',\n            fontWeight: 'bold',\n            color: '#2c3e50'\n          },\n          children: getEventIcon()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            flexWrap: 'wrap'\n          },\n          children: [event.is_holiday && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '15px',\n              fontSize: '1.4rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 4px 15px rgba(243, 156, 18, 0.3)'\n            },\n            children: \"HOLIDAY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), event.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '15px',\n              fontSize: '1.4rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)'\n            },\n            children: \"IMPORTANT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: images.length > 0 ? '3rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        },\n        children: event.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          margin: '1.5rem 0',\n          padding: '1.5rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          borderRadius: '15px',\n          border: '2px solid rgba(231, 76, 60, 0.2)',\n          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.1)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '1.8rem',\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '1rem',\n            borderRadius: '15px',\n            boxShadow: `0 6px 15px ${getCategoryColor()}40`,\n            fontWeight: 'bold'\n          },\n          children: \"DATE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.8rem',\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '2.2rem',\n              fontWeight: '700',\n              color: '#2c3e50'\n            },\n            children: formatDateRange()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n              background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n              padding: '0.6rem 1rem',\n              borderRadius: '10px',\n              display: 'inline-block'\n            },\n            children: getDaysUntilEvent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: descriptionRef,\n        style: {\n          fontSize: images.length > 0 ? '2rem' : '2.6rem',\n          lineHeight: '1.6',\n          color: '#374151',\n          background: 'rgba(255, 255, 255, 0.8)',\n          padding: images.length > 0 ? '1.5rem' : '2rem',\n          borderRadius: '15px',\n          border: '2px solid rgba(231, 76, 60, 0.2)',\n          wordWrap: 'break-word',\n          flex: 1,\n          marginBottom: '1.5rem',\n          textAlign: images.length > 0 ? 'left' : 'center',\n          maxHeight: images.length > 0 ? '250px' : '350px',\n          overflow: 'hidden',\n          position: 'relative'\n        },\n        children: [event.description, shouldScroll && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '10px',\n            right: '10px',\n            background: 'rgba(231, 76, 60, 0.8)',\n            color: 'white',\n            padding: '0.3rem 0.6rem',\n            borderRadius: '10px',\n            fontSize: '0.8rem',\n            fontWeight: 'bold'\n          },\n          children: \"SCROLLING\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '20px',\n            fontWeight: '700',\n            fontSize: '1.4rem',\n            boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            alignSelf: 'flex-start'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          },\n          children: event.created_by_name && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#e74c3c',\n              fontWeight: '600'\n            },\n            children: [\"By: \", event.created_by_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: getCategoryColor(),\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            fontWeight: '600',\n            fontSize: '1.6rem'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this), event.subcategory_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: event.subcategory_color || '#95a5a6',\n            color: 'white',\n            padding: '0.6rem 1.2rem',\n            borderRadius: '20px',\n            fontSize: '1.4rem'\n          },\n          children: event.subcategory_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), event.is_recurring && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.6rem',\n            color: '#8e44ad'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: 'bold'\n            },\n            children: \"REPEATS:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [event.recurrence_pattern === 'yearly' && 'Yearly', event.recurrence_pattern === 'monthly' && 'Monthly', event.recurrence_pattern === 'weekly' && 'Weekly']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Organized by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: event.created_by_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '0 0 40%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        position: 'relative',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%',\n          height: '100%',\n          borderRadius: '0px',\n          // Removed border radius to show full image\n          overflow: 'hidden',\n          boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n          border: `4px solid ${getCategoryColor()}`,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: images[currentImageIndex].url,\n          alt: images[currentImageIndex].alt,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block',\n            transition: 'opacity 0.5s ease-in-out'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '1rem',\n            right: '1rem',\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '1.2rem',\n            fontWeight: '600'\n          },\n          children: [currentImageIndex + 1, \" / \", images.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 15\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '1rem',\n            right: '1rem',\n            background: 'rgba(231, 76, 60, 0.8)',\n            color: 'white',\n            padding: '0.5rem',\n            borderRadius: '50%',\n            fontSize: '0.8rem',\n            fontWeight: 'bold'\n          },\n          children: \"AUTO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(TVCalendarEvent, \"ZXteECXEVMMTWrZVGjzTzCMbz78=\");\n_c = TVCalendarEvent;\nexport default TVCalendarEvent;\nvar _c;\n$RefreshReg$(_c, \"TVCalendarEvent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getImageUrl", "jsxDEV", "_jsxDEV", "TVCalendarEvent", "event", "_s", "currentImageIndex", "setCurrentImageIndex", "descriptionRef", "shouldScroll", "setShouldScroll", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatDateRange", "startDate", "event_date", "end_date", "endDate", "startFormatted", "endFormatted", "getDaysUntilEvent", "today", "eventDate", "diffTime", "getTime", "diffDays", "Math", "ceil", "abs", "getCategoryColor", "category_color", "getEventIcon", "is_holiday", "is_recurring", "is_alert", "isUpcoming", "getEventImages", "images", "Array", "isArray", "for<PERSON>ach", "img", "index", "file_path", "imageUrl", "push", "url", "alt", "title", "length", "interval", "setInterval", "prev", "clearInterval", "checkScrollNeed", "current", "description", "element", "needsScroll", "scrollHeight", "clientHeight", "startScrollTimeout", "setTimeout", "style", "animation", "offsetHeight", "max", "clearTimeout", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "maxHeight", "display", "gap", "alignItems", "children", "flex", "flexDirection", "justifyContent", "zIndex", "color", "fontSize", "fontWeight", "textTransform", "letterSpacing", "marginBottom", "alignSelf", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flexWrap", "lineHeight", "textShadow", "wordWrap", "textAlign", "ref", "bottom", "right", "category_name", "created_by_name", "className", "subcategory_name", "subcategory_color", "recurrence_pattern", "opacity", "minHeight", "width", "height", "src", "objectFit", "transition", "onError", "e", "currentTarget", "top", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVCalendarEvent.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVCalendarEventProps {\n  event: CalendarEvent;\n}\n\nconst TVCalendarEvent: React.FC<TVCalendarEventProps> = ({ event }) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const descriptionRef = useRef<HTMLDivElement>(null);\n  const [shouldScroll, setShouldScroll] = useState(false);\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    \n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    \n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color - keep original colors\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Keep original red for events\n  };\n\n  // Determine event type text\n  const getEventIcon = () => {\n    if (event.is_holiday) return 'HOLIDAY';\n    if (event.is_recurring) return 'RECURRING';\n    if (event.is_alert) return 'ALERT';\n    return 'EVENT';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // No truncation needed - using auto-scroll instead\n\n  // Get event images\n  const getEventImages = () => {\n    const images: { url: string; alt: string }[] = [];\n\n    // Check if event has images (from API response)\n    if ((event as any).images && Array.isArray((event as any).images)) {\n      (event as any).images.forEach((img: any, index: number) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getEventImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex((prev) => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  // Check if description needs scrolling and set up auto-scroll\n  useEffect(() => {\n    const checkScrollNeed = () => {\n      if (descriptionRef.current && event.description) {\n        const element = descriptionRef.current;\n        const needsScroll = element.scrollHeight > element.clientHeight;\n        setShouldScroll(needsScroll);\n\n        if (needsScroll) {\n          // Start auto-scroll after 2 seconds\n          const startScrollTimeout = setTimeout(() => {\n            element.style.animation = 'none'; // Reset animation\n            void element.offsetHeight; // Trigger reflow (void to satisfy ESLint)\n            element.style.animation = `autoScroll ${Math.max(10, element.scrollHeight / 20)}s linear infinite`;\n          }, 2000);\n\n          return () => clearTimeout(startScrollTimeout);\n        }\n      }\n    };\n\n    checkScrollNeed();\n  }, [event.description, images.length]);\n\n  return (\n    <div style={{\n      background: isUpcoming()\n        ? 'linear-gradient(135deg, #fefce8 0%, #ffffff 100%)'\n        : 'linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: isUpcoming()\n        ? '0 10px 30px rgba(245, 158, 11, 0.2)'\n        : '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming()\n        ? '4px solid #f59e0b'\n        : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block', // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    }}>\n      {/* Content Section - Full width if no images, 60% if images exist */}\n      <div style={{\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        {/* Upcoming event indicator */}\n        {isUpcoming() && (\n          <div style={{\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.6rem',\n            fontWeight: 'bold',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n            marginBottom: '1.5rem',\n            alignSelf: 'flex-start'\n          }}>\n            UPCOMING EVENT\n          </div>\n        )}\n        {/* Event type indicator */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          marginBottom: '1.5rem'\n        }}>\n          <div style={{\n            fontSize: '1.2rem',\n            background: 'rgba(255, 255, 255, 0.9)',\n            padding: '0.8rem',\n            borderRadius: '15px',\n            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',\n            fontWeight: 'bold',\n            color: '#2c3e50'\n          }}>\n            {getEventIcon()}\n          </div>\n\n          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n            {event.is_holiday && (\n              <span style={{\n                background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '15px',\n                fontSize: '1.4rem',\n                fontWeight: '700',\n                textTransform: 'uppercase',\n                letterSpacing: '1px',\n                boxShadow: '0 4px 15px rgba(243, 156, 18, 0.3)'\n              }}>\n                HOLIDAY\n              </span>\n            )}\n            {event.is_alert && (\n              <span style={{\n                background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '15px',\n                fontSize: '1.4rem',\n                fontWeight: '700',\n                textTransform: 'uppercase',\n                letterSpacing: '1px',\n                boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)'\n              }}>\n                IMPORTANT\n              </span>\n            )}\n          </div>\n        </div>\n\n        {/* Event title - Larger when no images */}\n        <h2 style={{\n          fontSize: images.length > 0 ? '3rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#2c3e50',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        }}>\n          {event.title}\n        </h2>\n\n        {/* Event date with countdown */}\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1.5rem',\n          margin: '1.5rem 0',\n          padding: '1.5rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          borderRadius: '15px',\n          border: '2px solid rgba(231, 76, 60, 0.2)',\n          boxShadow: '0 6px 20px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            fontSize: '1.8rem',\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '1rem',\n            borderRadius: '15px',\n            boxShadow: `0 6px 15px ${getCategoryColor()}40`,\n            fontWeight: 'bold'\n          }}>\n            DATE\n          </div>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.8rem', flex: 1 }}>\n            <span style={{\n              fontSize: '2.2rem',\n              fontWeight: '700',\n              color: '#2c3e50'\n            }}>\n              {formatDateRange()}\n            </span>\n            <span style={{\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n              background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n              padding: '0.6rem 1rem',\n              borderRadius: '10px',\n              display: 'inline-block'\n            }}>\n              {getDaysUntilEvent()}\n            </span>\n          </div>\n        </div>\n\n        {/* Event description - Auto-scrolling for long text */}\n        {event.description && (\n          <div\n            ref={descriptionRef}\n            style={{\n              fontSize: images.length > 0 ? '2rem' : '2.6rem',\n              lineHeight: '1.6',\n              color: '#374151',\n              background: 'rgba(255, 255, 255, 0.8)',\n              padding: images.length > 0 ? '1.5rem' : '2rem',\n              borderRadius: '15px',\n              border: '2px solid rgba(231, 76, 60, 0.2)',\n              wordWrap: 'break-word',\n              flex: 1,\n              marginBottom: '1.5rem',\n              textAlign: images.length > 0 ? 'left' : 'center',\n              maxHeight: images.length > 0 ? '250px' : '350px',\n              overflow: 'hidden',\n              position: 'relative'\n            }}\n          >\n            {event.description}\n\n            {/* Scrolling indicator */}\n            {shouldScroll && (\n              <div style={{\n                position: 'absolute',\n                bottom: '10px',\n                right: '10px',\n                background: 'rgba(231, 76, 60, 0.8)',\n                color: 'white',\n                padding: '0.3rem 0.6rem',\n                borderRadius: '10px',\n                fontSize: '0.8rem',\n                fontWeight: 'bold'\n              }}>\n                SCROLLING\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Category and metadata */}\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        }}>\n          {event.category_name && (\n            <div style={{\n              background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '20px',\n              fontWeight: '700',\n              fontSize: '1.4rem',\n              boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              alignSelf: 'flex-start'\n            }}>\n              {event.category_name}\n            </div>\n          )}\n\n          {/* Creator information */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          }}>\n            {event.created_by_name && (\n              <span style={{ color: '#e74c3c', fontWeight: '600' }}>\n                By: {event.created_by_name}\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* Event metadata */}\n      <div className=\"tv-event-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {event.category_name && (\n            <span \n              style={{\n                background: getCategoryColor(),\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '25px',\n                fontWeight: '600',\n                fontSize: '1.6rem'\n              }}\n            >\n              {event.category_name}\n            </span>\n          )}\n\n          {/* Subcategory */}\n          {event.subcategory_name && (\n            <span style={{\n              background: event.subcategory_color || '#95a5a6',\n              color: 'white',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              fontSize: '1.4rem'\n            }}>\n              {event.subcategory_name}\n            </span>\n          )}\n\n          {/* Recurring indicator */}\n          {event.is_recurring && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '1.6rem',\n              color: '#8e44ad'\n            }}>\n              <span style={{ fontWeight: 'bold' }}>REPEATS:</span>\n              <span>\n                {event.recurrence_pattern === 'yearly' && 'Yearly'}\n                {event.recurrence_pattern === 'monthly' && 'Monthly'}\n                {event.recurrence_pattern === 'weekly' && 'Weekly'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Created by information */}\n        {event.created_by_name && (\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Organized by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {event.created_by_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Right Side - Images (40% width) */}\n      {images.length > 0 && (\n        <div style={{\n          flex: '0 0 40%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          position: 'relative',\n          minHeight: '400px'\n        }}>\n          {/* Single Image or Current Image from Carousel */}\n          <div style={{\n            width: '100%',\n            height: '100%',\n            borderRadius: '0px', // Removed border radius to show full image\n            overflow: 'hidden',\n            boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n            border: `4px solid ${getCategoryColor()}`,\n            position: 'relative'\n          }}>\n            <img\n              src={images[currentImageIndex].url}\n              alt={images[currentImageIndex].alt}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                display: 'block',\n                transition: 'opacity 0.5s ease-in-out'\n              }}\n              onError={(e) => {\n                e.currentTarget.style.display = 'none';\n              }}\n            />\n\n            {/* Image counter for multiple images */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                bottom: '1rem',\n                right: '1rem',\n                background: 'rgba(0, 0, 0, 0.7)',\n                color: 'white',\n                padding: '0.5rem 1rem',\n                borderRadius: '20px',\n                fontSize: '1.2rem',\n                fontWeight: '600'\n              }}>\n                {currentImageIndex + 1} / {images.length}\n              </div>\n            )}\n\n            {/* Auto-rotation indicator */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                top: '1rem',\n                right: '1rem',\n                background: 'rgba(231, 76, 60, 0.8)',\n                color: 'white',\n                padding: '0.5rem',\n                borderRadius: '50%',\n                fontSize: '0.8rem',\n                fontWeight: 'bold'\n              }}>\n                AUTO\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVCalendarEvent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAE1D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAMW,cAAc,GAAGT,MAAM,CAAiB,IAAI,CAAC;EACnD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACvD;EACA,MAAMc,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAACV,KAAK,CAACkB,UAAU,CAAC;IAE5C,IAAIlB,KAAK,CAACmB,QAAQ,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAIV,IAAI,CAACV,KAAK,CAACmB,QAAQ,CAAC;MACxC,MAAME,cAAc,GAAGJ,SAAS,CAACN,kBAAkB,CAAC,OAAO,EAAE;QAC3DG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,MAAMO,YAAY,GAAGF,OAAO,CAACT,kBAAkB,CAAC,OAAO,EAAE;QACvDG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdF,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAO,GAAGQ,cAAc,MAAMC,YAAY,EAAE;IAC9C;IAEA,OAAOf,UAAU,CAACP,KAAK,CAACkB,UAAU,CAAC;EACrC,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACV,KAAK,CAACkB,UAAU,CAAC;IAC5C,MAAMQ,QAAQ,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,UAAU;IACrC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAMA,QAAQ,OAAO;IAC9C,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW;IACvC,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,WAAW;EACzC,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhC,KAAK,CAACiC,cAAc,EAAE;MACxB,OAAOjC,KAAK,CAACiC,cAAc;IAC7B;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIlC,KAAK,CAACmC,UAAU,EAAE,OAAO,SAAS;IACtC,IAAInC,KAAK,CAACoC,YAAY,EAAE,OAAO,WAAW;IAC1C,IAAIpC,KAAK,CAACqC,QAAQ,EAAE,OAAO,OAAO;IAClC,OAAO,OAAO;EAChB,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMd,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACV,KAAK,CAACkB,UAAU,CAAC;IAC5C,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3F,OAAOC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;;EAEA;EACA,MAAMW,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAsC,GAAG,EAAE;;IAEjD;IACA,IAAKxC,KAAK,CAASwC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAE1C,KAAK,CAASwC,MAAM,CAAC,EAAE;MAChExC,KAAK,CAASwC,MAAM,CAACG,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAK;QACzD,IAAID,GAAG,CAACE,SAAS,EAAE;UACjB,MAAMC,QAAQ,GAAGnD,WAAW,CAACgD,GAAG,CAACE,SAAS,CAAC;UAC3C,IAAIC,QAAQ,EAAE;YACZP,MAAM,CAACQ,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGlD,KAAK,CAACmD,KAAK,YAAYN,KAAK,GAAG,CAAC;YAC1C,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOL,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,cAAc,CAAC,CAAC;;EAE/B;EACA7C,SAAS,CAAC,MAAM;IACd,IAAI8C,MAAM,CAACY,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCnD,oBAAoB,CAAEoD,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIf,MAAM,CAACY,MAAM,CAAC;MAC5D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMI,aAAa,CAACH,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACb,MAAM,CAACY,MAAM,CAAC,CAAC;;EAEnB;EACA1D,SAAS,CAAC,MAAM;IACd,MAAM+D,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAIrD,cAAc,CAACsD,OAAO,IAAI1D,KAAK,CAAC2D,WAAW,EAAE;QAC/C,MAAMC,OAAO,GAAGxD,cAAc,CAACsD,OAAO;QACtC,MAAMG,WAAW,GAAGD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,YAAY;QAC/DzD,eAAe,CAACuD,WAAW,CAAC;QAE5B,IAAIA,WAAW,EAAE;UACf;UACA,MAAMG,kBAAkB,GAAGC,UAAU,CAAC,MAAM;YAC1CL,OAAO,CAACM,KAAK,CAACC,SAAS,GAAG,MAAM,CAAC,CAAC;YAClC,KAAKP,OAAO,CAACQ,YAAY,CAAC,CAAC;YAC3BR,OAAO,CAACM,KAAK,CAACC,SAAS,GAAG,cAActC,IAAI,CAACwC,GAAG,CAAC,EAAE,EAAET,OAAO,CAACE,YAAY,GAAG,EAAE,CAAC,mBAAmB;UACpG,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,MAAMQ,YAAY,CAACN,kBAAkB,CAAC;QAC/C;MACF;IACF,CAAC;IAEDP,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACzD,KAAK,CAAC2D,WAAW,EAAEnB,MAAM,CAACY,MAAM,CAAC,CAAC;EAEtC,oBACEtD,OAAA;IAAKoE,KAAK,EAAE;MACVK,UAAU,EAAEjC,UAAU,CAAC,CAAC,GACpB,mDAAmD,GACnD,mDAAmD;MACvDkC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAErC,UAAU,CAAC,CAAC,GACnB,qCAAqC,GACrC,gCAAgC;MACpCsC,MAAM,EAAEtC,UAAU,CAAC,CAAC,GAChB,mBAAmB,GACnB,aAAaN,gBAAgB,CAAC,CAAC,EAAE;MACrC6C,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAExC,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;MAAE;MAC/C6B,GAAG,EAAEzC,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG;MACrC8B,UAAU,EAAE1C,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;IAC9C,CAAE;IAAA+B,QAAA,gBAEArF,OAAA;MAAKoE,KAAK,EAAE;QACVkB,IAAI,EAAE5C,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG;QACzC4B,OAAO,EAAE,MAAM;QACfK,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,eAAe;QAC/BT,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,GAEC7C,UAAU,CAAC,CAAC,iBACXxC,OAAA;QAAKoE,KAAK,EAAE;UACVK,UAAU,EAAE,2CAA2C;UACvDiB,KAAK,EAAE,OAAO;UACdf,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,MAAM;UACpBiB,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,MAAM;UAClBC,aAAa,EAAE,WAAW;UAC1BC,aAAa,EAAE,KAAK;UACpBjB,SAAS,EAAE,mCAAmC;UAC9CkB,YAAY,EAAE,QAAQ;UACtBC,SAAS,EAAE;QACb,CAAE;QAAAX,QAAA,EAAC;MAEH;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAEDpG,OAAA;QAAKoE,KAAK,EAAE;UACVc,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,QAAQ;UACbY,YAAY,EAAE;QAChB,CAAE;QAAAV,QAAA,gBACArF,OAAA;UAAKoE,KAAK,EAAE;YACVuB,QAAQ,EAAE,QAAQ;YAClBlB,UAAU,EAAE,0BAA0B;YACtCE,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,MAAM;YACpBG,SAAS,EAAE,+BAA+B;YAC1Ce,UAAU,EAAE,MAAM;YAClBF,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,EACCjD,YAAY,CAAC;QAAC;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENpG,OAAA;UAAKoE,KAAK,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEkB,QAAQ,EAAE;UAAO,CAAE;UAAAhB,QAAA,GAC5DnF,KAAK,CAACmC,UAAU,iBACfrC,OAAA;YAAMoE,KAAK,EAAE;cACXK,UAAU,EAAE,2CAA2C;cACvDiB,KAAK,EAAE,OAAO;cACdf,OAAO,EAAE,eAAe;cACxBD,YAAY,EAAE,MAAM;cACpBiB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBC,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE,KAAK;cACpBjB,SAAS,EAAE;YACb,CAAE;YAAAQ,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAlG,KAAK,CAACqC,QAAQ,iBACbvC,OAAA;YAAMoE,KAAK,EAAE;cACXK,UAAU,EAAE,2CAA2C;cACvDiB,KAAK,EAAE,OAAO;cACdf,OAAO,EAAE,eAAe;cACxBD,YAAY,EAAE,MAAM;cACpBiB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBC,aAAa,EAAE,WAAW;cAC1BC,aAAa,EAAE,KAAK;cACpBjB,SAAS,EAAE;YACb,CAAE;YAAAQ,QAAA,EAAC;UAEH;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpG,OAAA;QAAIoE,KAAK,EAAE;UACTuB,QAAQ,EAAEjD,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;UAC7CsC,UAAU,EAAE,KAAK;UACjBhB,MAAM,EAAE,cAAc;UACtBc,KAAK,EAAE,SAAS;UAChBY,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,8BAA8B;UAC1CC,QAAQ,EAAE,YAAY;UACtBC,SAAS,EAAE/D,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAC1C,CAAE;QAAA+B,QAAA,EACCnF,KAAK,CAACmD;MAAK;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGLpG,OAAA;QAAKoE,KAAK,EAAE;UACVc,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,QAAQ;UACbP,MAAM,EAAE,UAAU;UAClBD,OAAO,EAAE,QAAQ;UACjBF,UAAU,EAAE,0BAA0B;UACtCC,YAAY,EAAE,MAAM;UACpBI,MAAM,EAAE,kCAAkC;UAC1CD,SAAS,EAAE;QACb,CAAE;QAAAQ,QAAA,gBACArF,OAAA;UAAKoE,KAAK,EAAE;YACVuB,QAAQ,EAAE,QAAQ;YAClBlB,UAAU,EAAE,2BAA2BvC,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrFwD,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,MAAM;YACfD,YAAY,EAAE,MAAM;YACpBG,SAAS,EAAE,cAAc3C,gBAAgB,CAAC,CAAC,IAAI;YAC/C0D,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpG,OAAA;UAAKoE,KAAK,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEK,aAAa,EAAE,QAAQ;YAAEJ,GAAG,EAAE,QAAQ;YAAEG,IAAI,EAAE;UAAE,CAAE;UAAAD,QAAA,gBAC/ErF,OAAA;YAAMoE,KAAK,EAAE;cACXuB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBF,KAAK,EAAE;YACT,CAAE;YAAAL,QAAA,EACCnE,eAAe,CAAC;UAAC;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACPpG,OAAA;YAAMoE,KAAK,EAAE;cACXuB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBF,KAAK,EAAElD,UAAU,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;cAC3CiC,UAAU,EAAEjC,UAAU,CAAC,CAAC,GAAG,wBAAwB,GAAG,0BAA0B;cAChFmC,OAAO,EAAE,aAAa;cACtBD,YAAY,EAAE,MAAM;cACpBQ,OAAO,EAAE;YACX,CAAE;YAAAG,QAAA,EACC5D,iBAAiB,CAAC;UAAC;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLlG,KAAK,CAAC2D,WAAW,iBAChB7D,OAAA;QACE0G,GAAG,EAAEpG,cAAe;QACpB8D,KAAK,EAAE;UACLuB,QAAQ,EAAEjD,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ;UAC/CgD,UAAU,EAAE,KAAK;UACjBZ,KAAK,EAAE,SAAS;UAChBjB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAEjC,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAC9CoB,YAAY,EAAE,MAAM;UACpBI,MAAM,EAAE,kCAAkC;UAC1C0B,QAAQ,EAAE,YAAY;UACtBlB,IAAI,EAAE,CAAC;UACPS,YAAY,EAAE,QAAQ;UACtBU,SAAS,EAAE/D,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ;UAChD2B,SAAS,EAAEvC,MAAM,CAACY,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,OAAO;UAChD0B,QAAQ,EAAE,QAAQ;UAClBD,QAAQ,EAAE;QACZ,CAAE;QAAAM,QAAA,GAEDnF,KAAK,CAAC2D,WAAW,EAGjBtD,YAAY,iBACXP,OAAA;UAAKoE,KAAK,EAAE;YACVW,QAAQ,EAAE,UAAU;YACpB4B,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACbnC,UAAU,EAAE,wBAAwB;YACpCiB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBiB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGDpG,OAAA;QAAKoE,KAAK,EAAE;UACVc,OAAO,EAAE,MAAM;UACfK,aAAa,EAAE,QAAQ;UACvBJ,GAAG,EAAE;QACP,CAAE;QAAAE,QAAA,GACCnF,KAAK,CAAC2G,aAAa,iBAClB7G,OAAA;UAAKoE,KAAK,EAAE;YACVK,UAAU,EAAE,2BAA2BvC,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrFwD,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBkB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE,QAAQ;YAClBd,SAAS,EAAE,cAAc3C,gBAAgB,CAAC,CAAC,IAAI;YAC/C2D,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBE,SAAS,EAAE;UACb,CAAE;UAAAX,QAAA,EACCnF,KAAK,CAAC2G;QAAa;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACN,eAGDpG,OAAA;UAAKoE,KAAK,EAAE;YACVc,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACbkB,QAAQ,EAAE,MAAM;YAChBV,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,EACCnF,KAAK,CAAC4G,eAAe,iBACpB9G,OAAA;YAAMoE,KAAK,EAAE;cAAEsB,KAAK,EAAE,SAAS;cAAEE,UAAU,EAAE;YAAM,CAAE;YAAAP,QAAA,GAAC,MAChD,EAACnF,KAAK,CAAC4G,eAAe;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAKNpG,OAAA;MAAK+G,SAAS,EAAC,eAAe;MAAA1B,QAAA,gBAC5BrF,OAAA;QAAKoE,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAO,CAAE;QAAAE,QAAA,GAEhEnF,KAAK,CAAC2G,aAAa,iBAClB7G,OAAA;UACEoE,KAAK,EAAE;YACLK,UAAU,EAAEvC,gBAAgB,CAAC,CAAC;YAC9BwD,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBkB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAEDnF,KAAK,CAAC2G;QAAa;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EAGAlG,KAAK,CAAC8G,gBAAgB,iBACrBhH,OAAA;UAAMoE,KAAK,EAAE;YACXK,UAAU,EAAEvE,KAAK,CAAC+G,iBAAiB,IAAI,SAAS;YAChDvB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBiB,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EACCnF,KAAK,CAAC8G;QAAgB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP,EAGAlG,KAAK,CAACoC,YAAY,iBACjBtC,OAAA;UAAKoE,KAAK,EAAE;YACVc,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACbQ,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,gBACArF,OAAA;YAAMoE,KAAK,EAAE;cAAEwB,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAAQ;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDpG,OAAA;YAAAqF,QAAA,GACGnF,KAAK,CAACgH,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EACjDhH,KAAK,CAACgH,kBAAkB,KAAK,SAAS,IAAI,SAAS,EACnDhH,KAAK,CAACgH,kBAAkB,KAAK,QAAQ,IAAI,QAAQ;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLlG,KAAK,CAAC4G,eAAe,iBACpB9G,OAAA;QAAKoE,KAAK,EAAE;UACVc,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,GAAG,EAAE,MAAM;UACXQ,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,gBACArF,OAAA;UAAMoE,KAAK,EAAE;YAAE+C,OAAO,EAAE;UAAI,CAAE;UAAA9B,QAAA,EAAC;QAAa;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDpG,OAAA;UAAMoE,KAAK,EAAE;YAAEwB,UAAU,EAAE;UAAM,CAAE;UAAAP,QAAA,EAChCnF,KAAK,CAAC4G;QAAe;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL1D,MAAM,CAACY,MAAM,GAAG,CAAC,iBAChBtD,OAAA;MAAKoE,KAAK,EAAE;QACVkB,IAAI,EAAE,SAAS;QACfJ,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBI,cAAc,EAAE,QAAQ;QACxBT,QAAQ,EAAE,UAAU;QACpBqC,SAAS,EAAE;MACb,CAAE;MAAA/B,QAAA,eAEArF,OAAA;QAAKoE,KAAK,EAAE;UACViD,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACd5C,YAAY,EAAE,KAAK;UAAE;UACrBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE,aAAa5C,gBAAgB,CAAC,CAAC,EAAE;UACzC6C,QAAQ,EAAE;QACZ,CAAE;QAAAM,QAAA,gBACArF,OAAA;UACEuH,GAAG,EAAE7E,MAAM,CAACtC,iBAAiB,CAAC,CAAC+C,GAAI;UACnCC,GAAG,EAAEV,MAAM,CAACtC,iBAAiB,CAAC,CAACgD,GAAI;UACnCgB,KAAK,EAAE;YACLiD,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdE,SAAS,EAAE,OAAO;YAClBtC,OAAO,EAAE,OAAO;YAChBuC,UAAU,EAAE;UACd,CAAE;UACFC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAACxD,KAAK,CAACc,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGD1D,MAAM,CAACY,MAAM,GAAG,CAAC,iBAChBtD,OAAA;UAAKoE,KAAK,EAAE;YACVW,QAAQ,EAAE,UAAU;YACpB4B,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACbnC,UAAU,EAAE,oBAAoB;YAChCiB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,aAAa;YACtBD,YAAY,EAAE,MAAM;YACpBiB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,GACCjF,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACsC,MAAM,CAACY,MAAM;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN,EAGA1D,MAAM,CAACY,MAAM,GAAG,CAAC,iBAChBtD,OAAA;UAAKoE,KAAK,EAAE;YACVW,QAAQ,EAAE,UAAU;YACpB8C,GAAG,EAAE,MAAM;YACXjB,KAAK,EAAE,MAAM;YACbnC,UAAU,EAAE,wBAAwB;YACpCiB,KAAK,EAAE,OAAO;YACdf,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,KAAK;YACnBiB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAP,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjG,EAAA,CAxgBIF,eAA+C;AAAA6H,EAAA,GAA/C7H,eAA+C;AA0gBrD,eAAeA,eAAe;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}