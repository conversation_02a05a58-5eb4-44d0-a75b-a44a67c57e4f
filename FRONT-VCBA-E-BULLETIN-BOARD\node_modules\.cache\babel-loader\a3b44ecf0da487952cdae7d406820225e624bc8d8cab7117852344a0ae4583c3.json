{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\tv-control\\\\TVPlaybackControls.tsx\";\nimport React from 'react';\nimport { tvControlService } from '../../../services/tvControlService';\nimport { Play, Pause, SkipForward, SkipBack, RefreshCw, Square } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TVPlaybackControls = ({\n  settings,\n  status\n}) => {\n  const handlePlay = () => {\n    tvControlService.play();\n  };\n  const handlePause = () => {\n    tvControlService.pause();\n  };\n  const handleNext = () => {\n    tvControlService.next();\n  };\n  const handlePrevious = () => {\n    tvControlService.previous();\n  };\n  const handleRefresh = () => {\n    tvControlService.refresh();\n  };\n  const handleToggleAutoPlay = () => {\n    tvControlService.updateSettings({\n      autoPlay: !settings.autoPlay\n    });\n  };\n  const formatTime = ms => {\n    const seconds = Math.floor(ms / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const controlButtonStyle = {\n    background: '#3498db',\n    color: 'white',\n    border: 'none',\n    borderRadius: '50%',\n    width: '60px',\n    height: '60px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    boxShadow: '0 4px 12px rgba(52, 152, 219, 0.3)'\n  };\n  const secondaryButtonStyle = {\n    background: '#95a5a6',\n    color: 'white',\n    border: 'none',\n    borderRadius: '8px',\n    padding: '0.75rem 1.5rem',\n    cursor: 'pointer',\n    fontSize: '0.9rem',\n    fontWeight: '500',\n    transition: 'all 0.2s ease'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: '1.8rem',\n        fontWeight: '600',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50'\n      },\n      children: \"Playback Controls\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '2rem',\n        marginBottom: '2rem',\n        border: '1px solid #e9ecef'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#6c757d',\n              fontSize: '0.9rem'\n            },\n            children: \"Current Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              color: status.isPlaying ? '#28a745' : '#dc3545'\n            },\n            children: status.isPlaying ? '▶️ Playing' : '⏸️ Paused'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#6c757d',\n              fontSize: '0.9rem'\n            },\n            children: \"Current Slide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              color: '#2c3e50'\n            },\n            children: [status.currentSlide + 1, \" of \", status.totalSlides]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#6c757d',\n              fontSize: '0.9rem'\n            },\n            children: \"Slide Duration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              color: '#2c3e50'\n            },\n            children: formatTime(settings.slideInterval)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 0.5rem 0',\n              color: '#6c757d',\n              fontSize: '0.9rem'\n            },\n            children: \"Auto-Play\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              color: settings.autoPlay ? '#28a745' : '#dc3545'\n            },\n            children: settings.autoPlay ? '✅ Enabled' : '❌ Disabled'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        gap: '1.5rem',\n        marginBottom: '3rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handlePrevious,\n        style: controlButtonStyle,\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#2980b9';\n          e.currentTarget.style.transform = 'scale(1.05)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = '#3498db';\n          e.currentTarget.style.transform = 'scale(1)';\n        },\n        title: \"Previous Slide\",\n        children: /*#__PURE__*/_jsxDEV(SkipBack, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: status.isPlaying ? handlePause : handlePlay,\n        style: {\n          ...controlButtonStyle,\n          width: '80px',\n          height: '80px',\n          background: status.isPlaying ? '#e74c3c' : '#27ae60'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = status.isPlaying ? '#c0392b' : '#229954';\n          e.currentTarget.style.transform = 'scale(1.05)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = status.isPlaying ? '#e74c3c' : '#27ae60';\n          e.currentTarget.style.transform = 'scale(1)';\n        },\n        title: status.isPlaying ? 'Pause' : 'Play',\n        children: status.isPlaying ? /*#__PURE__*/_jsxDEV(Pause, {\n          size: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(Play, {\n          size: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleNext,\n        style: controlButtonStyle,\n        onMouseEnter: e => {\n          e.currentTarget.style.background = '#2980b9';\n          e.currentTarget.style.transform = 'scale(1.05)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = '#3498db';\n          e.currentTarget.style.transform = 'scale(1)';\n        },\n        title: \"Next Slide\",\n        children: /*#__PURE__*/_jsxDEV(SkipForward, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '1rem',\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleRefresh,\n        style: secondaryButtonStyle,\n        onMouseEnter: e => e.currentTarget.style.background = '#7f8c8d',\n        onMouseLeave: e => e.currentTarget.style.background = '#95a5a6',\n        children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n          size: 16,\n          style: {\n            marginRight: '0.5rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), \"Refresh Content\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleToggleAutoPlay,\n        style: {\n          ...secondaryButtonStyle,\n          background: settings.autoPlay ? '#e74c3c' : '#27ae60'\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = settings.autoPlay ? '#c0392b' : '#229954';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = settings.autoPlay ? '#e74c3c' : '#27ae60';\n        },\n        children: settings.autoPlay ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Square, {\n            size: 16,\n            style: {\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), \"Disable Auto-Play\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Play, {\n            size: 16,\n            style: {\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), \"Enable Auto-Play\"]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '3rem',\n        padding: '2rem',\n        background: '#f8f9fa',\n        borderRadius: '12px',\n        border: '1px solid #e9ecef'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          fontSize: '1.2rem',\n          fontWeight: '600',\n          margin: '0 0 1rem 0',\n          color: '#2c3e50'\n        },\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem',\n            background: 'white',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Keyboard Shortcuts (on TV display):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '0.5rem 0 0 0',\n              paddingLeft: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Space: Play/Pause\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2190 \\u2192: Previous/Next slide\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"R: Refresh content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1rem',\n            background: 'white',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Remote Control:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0.5rem 0 0 0',\n              fontSize: '0.9rem',\n              color: '#6c757d'\n            },\n            children: \"Use this panel to control the TV display remotely from any device connected to the same network.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_c = TVPlaybackControls;\nexport default TVPlaybackControls;\nvar _c;\n$RefreshReg$(_c, \"TVPlaybackControls\");", "map": {"version": 3, "names": ["React", "tvControlService", "Play", "Pause", "SkipForward", "SkipBack", "RefreshCw", "Square", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TVPlaybackControls", "settings", "status", "handlePlay", "play", "handlePause", "pause", "handleNext", "next", "handlePrevious", "previous", "handleRefresh", "refresh", "handleToggleAutoPlay", "updateSettings", "autoPlay", "formatTime", "ms", "seconds", "Math", "floor", "minutes", "remainingSeconds", "toString", "padStart", "controlButtonStyle", "background", "color", "border", "borderRadius", "width", "height", "display", "alignItems", "justifyContent", "cursor", "transition", "boxShadow", "secondaryButtonStyle", "padding", "fontSize", "fontWeight", "children", "style", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "gridTemplateColumns", "gap", "isPlaying", "currentSlide", "totalSlides", "slideInterval", "onClick", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "title", "size", "flexWrap", "marginRight", "marginTop", "paddingLeft", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/tv-control/TVPlaybackControls.tsx"], "sourcesContent": ["import React from 'react';\nimport { tvControlService, TVDisplaySettings, TVDisplayStatus } from '../../../services/tvControlService';\nimport { Play, Pause, SkipForward, SkipBack, RefreshCw, Square } from 'lucide-react';\n\ninterface TVPlaybackControlsProps {\n  settings: TVDisplaySettings;\n  status: TVDisplayStatus;\n}\n\nconst TVPlaybackControls: React.FC<TVPlaybackControlsProps> = ({ settings, status }) => {\n  const handlePlay = () => {\n    tvControlService.play();\n  };\n\n  const handlePause = () => {\n    tvControlService.pause();\n  };\n\n  const handleNext = () => {\n    tvControlService.next();\n  };\n\n  const handlePrevious = () => {\n    tvControlService.previous();\n  };\n\n  const handleRefresh = () => {\n    tvControlService.refresh();\n  };\n\n  const handleToggleAutoPlay = () => {\n    tvControlService.updateSettings({\n      autoPlay: !settings.autoPlay\n    });\n  };\n\n  const formatTime = (ms: number) => {\n    const seconds = Math.floor(ms / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const controlButtonStyle = {\n    background: '#3498db',\n    color: 'white',\n    border: 'none',\n    borderRadius: '50%',\n    width: '60px',\n    height: '60px',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    transition: 'all 0.2s ease',\n    boxShadow: '0 4px 12px rgba(52, 152, 219, 0.3)'\n  };\n\n  const secondaryButtonStyle = {\n    background: '#95a5a6',\n    color: 'white',\n    border: 'none',\n    borderRadius: '8px',\n    padding: '0.75rem 1.5rem',\n    cursor: 'pointer',\n    fontSize: '0.9rem',\n    fontWeight: '500',\n    transition: 'all 0.2s ease'\n  };\n\n  return (\n    <div>\n      <h2 style={{\n        fontSize: '1.8rem',\n        fontWeight: '600',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50'\n      }}>\n        Playback Controls\n      </h2>\n\n      {/* Current Status Display */}\n      <div style={{\n        background: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '2rem',\n        marginBottom: '2rem',\n        border: '1px solid #e9ecef'\n      }}>\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n          gap: '2rem'\n        }}>\n          <div>\n            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>\n              Current Status\n            </h4>\n            <div style={{\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              color: status.isPlaying ? '#28a745' : '#dc3545'\n            }}>\n              {status.isPlaying ? '▶️ Playing' : '⏸️ Paused'}\n            </div>\n          </div>\n\n          <div>\n            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>\n              Current Slide\n            </h4>\n            <div style={{ fontSize: '1.5rem', fontWeight: '600', color: '#2c3e50' }}>\n              {status.currentSlide + 1} of {status.totalSlides}\n            </div>\n          </div>\n\n          <div>\n            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>\n              Slide Duration\n            </h4>\n            <div style={{ fontSize: '1.5rem', fontWeight: '600', color: '#2c3e50' }}>\n              {formatTime(settings.slideInterval)}\n            </div>\n          </div>\n\n          <div>\n            <h4 style={{ margin: '0 0 0.5rem 0', color: '#6c757d', fontSize: '0.9rem' }}>\n              Auto-Play\n            </h4>\n            <div style={{\n              fontSize: '1.5rem',\n              fontWeight: '600',\n              color: settings.autoPlay ? '#28a745' : '#dc3545'\n            }}>\n              {settings.autoPlay ? '✅ Enabled' : '❌ Disabled'}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Control Buttons */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        gap: '1.5rem',\n        marginBottom: '3rem'\n      }}>\n        {/* Previous Button */}\n        <button\n          onClick={handlePrevious}\n          style={controlButtonStyle}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#2980b9';\n            e.currentTarget.style.transform = 'scale(1.05)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = '#3498db';\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n          title=\"Previous Slide\"\n        >\n          <SkipBack size={24} />\n        </button>\n\n        {/* Play/Pause Button */}\n        <button\n          onClick={status.isPlaying ? handlePause : handlePlay}\n          style={{\n            ...controlButtonStyle,\n            width: '80px',\n            height: '80px',\n            background: status.isPlaying ? '#e74c3c' : '#27ae60'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = status.isPlaying ? '#c0392b' : '#229954';\n            e.currentTarget.style.transform = 'scale(1.05)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = status.isPlaying ? '#e74c3c' : '#27ae60';\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n          title={status.isPlaying ? 'Pause' : 'Play'}\n        >\n          {status.isPlaying ? <Pause size={32} /> : <Play size={32} />}\n        </button>\n\n        {/* Next Button */}\n        <button\n          onClick={handleNext}\n          style={controlButtonStyle}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = '#2980b9';\n            e.currentTarget.style.transform = 'scale(1.05)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = '#3498db';\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n          title=\"Next Slide\"\n        >\n          <SkipForward size={24} />\n        </button>\n      </div>\n\n      {/* Secondary Controls */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '1rem',\n        flexWrap: 'wrap'\n      }}>\n        <button\n          onClick={handleRefresh}\n          style={secondaryButtonStyle}\n          onMouseEnter={(e) => e.currentTarget.style.background = '#7f8c8d'}\n          onMouseLeave={(e) => e.currentTarget.style.background = '#95a5a6'}\n        >\n          <RefreshCw size={16} style={{ marginRight: '0.5rem' }} />\n          Refresh Content\n        </button>\n\n        <button\n          onClick={handleToggleAutoPlay}\n          style={{\n            ...secondaryButtonStyle,\n            background: settings.autoPlay ? '#e74c3c' : '#27ae60'\n          }}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.background = settings.autoPlay ? '#c0392b' : '#229954';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.background = settings.autoPlay ? '#e74c3c' : '#27ae60';\n          }}\n        >\n          {settings.autoPlay ? (\n            <>\n              <Square size={16} style={{ marginRight: '0.5rem' }} />\n              Disable Auto-Play\n            </>\n          ) : (\n            <>\n              <Play size={16} style={{ marginRight: '0.5rem' }} />\n              Enable Auto-Play\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Quick Actions */}\n      <div style={{\n        marginTop: '3rem',\n        padding: '2rem',\n        background: '#f8f9fa',\n        borderRadius: '12px',\n        border: '1px solid #e9ecef'\n      }}>\n        <h3 style={{\n          fontSize: '1.2rem',\n          fontWeight: '600',\n          margin: '0 0 1rem 0',\n          color: '#2c3e50'\n        }}>\n          Quick Actions\n        </h3>\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '1rem'\n        }}>\n          <div style={{\n            padding: '1rem',\n            background: 'white',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          }}>\n            <strong>Keyboard Shortcuts (on TV display):</strong>\n            <ul style={{ margin: '0.5rem 0 0 0', paddingLeft: '1.5rem' }}>\n              <li>Space: Play/Pause</li>\n              <li>← →: Previous/Next slide</li>\n              <li>R: Refresh content</li>\n            </ul>\n          </div>\n          <div style={{\n            padding: '1rem',\n            background: 'white',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          }}>\n            <strong>Remote Control:</strong>\n            <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.9rem', color: '#6c757d' }}>\n              Use this panel to control the TV display remotely from any device connected to the same network.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TVPlaybackControls;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAA4C,oCAAoC;AACzG,SAASC,IAAI,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOrF,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAO,CAAC,KAAK;EACtF,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBd,gBAAgB,CAACe,IAAI,CAAC,CAAC;EACzB,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBhB,gBAAgB,CAACiB,KAAK,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBlB,gBAAgB,CAACmB,IAAI,CAAC,CAAC;EACzB,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BpB,gBAAgB,CAACqB,QAAQ,CAAC,CAAC;EAC7B,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BtB,gBAAgB,CAACuB,OAAO,CAAC,CAAC;EAC5B,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCxB,gBAAgB,CAACyB,cAAc,CAAC;MAC9BC,QAAQ,EAAE,CAACd,QAAQ,CAACc;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAIC,EAAU,IAAK;IACjC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,EAAE,GAAG,IAAI,CAAC;IACrC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAO,GAAGG,OAAO,IAAIC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE,CAAC;EAED,MAAMC,kBAAkB,GAAG;IACzBC,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,eAAe;IAC3BC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,oBAAoB,GAAG;IAC3BZ,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,MAAM;IACdC,YAAY,EAAE,KAAK;IACnBU,OAAO,EAAE,gBAAgB;IACzBJ,MAAM,EAAE,SAAS;IACjBK,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,KAAK;IACjBL,UAAU,EAAE;EACd,CAAC;EAED,oBACEvC,OAAA;IAAA6C,QAAA,gBACE7C,OAAA;MAAI8C,KAAK,EAAE;QACTH,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,KAAK;QACjBG,MAAM,EAAE,YAAY;QACpBjB,KAAK,EAAE;MACT,CAAE;MAAAe,QAAA,EAAC;IAEH;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGLnD,OAAA;MAAK8C,KAAK,EAAE;QACVjB,UAAU,EAAE,SAAS;QACrBG,YAAY,EAAE,MAAM;QACpBU,OAAO,EAAE,MAAM;QACfU,YAAY,EAAE,MAAM;QACpBrB,MAAM,EAAE;MACV,CAAE;MAAAc,QAAA,eACA7C,OAAA;QAAK8C,KAAK,EAAE;UACVX,OAAO,EAAE,MAAM;UACfkB,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,gBACA7C,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAI8C,KAAK,EAAE;cAAEC,MAAM,EAAE,cAAc;cAAEjB,KAAK,EAAE,SAAS;cAAEa,QAAQ,EAAE;YAAS,CAAE;YAAAE,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnD,OAAA;YAAK8C,KAAK,EAAE;cACVH,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBd,KAAK,EAAEzB,MAAM,CAACkD,SAAS,GAAG,SAAS,GAAG;YACxC,CAAE;YAAAV,QAAA,EACCxC,MAAM,CAACkD,SAAS,GAAG,YAAY,GAAG;UAAW;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAI8C,KAAK,EAAE;cAAEC,MAAM,EAAE,cAAc;cAAEjB,KAAK,EAAE,SAAS;cAAEa,QAAQ,EAAE;YAAS,CAAE;YAAAE,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnD,OAAA;YAAK8C,KAAK,EAAE;cAAEH,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAEd,KAAK,EAAE;YAAU,CAAE;YAAAe,QAAA,GACrExC,MAAM,CAACmD,YAAY,GAAG,CAAC,EAAC,MAAI,EAACnD,MAAM,CAACoD,WAAW;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAI8C,KAAK,EAAE;cAAEC,MAAM,EAAE,cAAc;cAAEjB,KAAK,EAAE,SAAS;cAAEa,QAAQ,EAAE;YAAS,CAAE;YAAAE,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnD,OAAA;YAAK8C,KAAK,EAAE;cAAEH,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,KAAK;cAAEd,KAAK,EAAE;YAAU,CAAE;YAAAe,QAAA,EACrE1B,UAAU,CAACf,QAAQ,CAACsD,aAAa;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAI8C,KAAK,EAAE;cAAEC,MAAM,EAAE,cAAc;cAAEjB,KAAK,EAAE,SAAS;cAAEa,QAAQ,EAAE;YAAS,CAAE;YAAAE,QAAA,EAAC;UAE7E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnD,OAAA;YAAK8C,KAAK,EAAE;cACVH,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBd,KAAK,EAAE1B,QAAQ,CAACc,QAAQ,GAAG,SAAS,GAAG;YACzC,CAAE;YAAA2B,QAAA,EACCzC,QAAQ,CAACc,QAAQ,GAAG,WAAW,GAAG;UAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAK8C,KAAK,EAAE;QACVX,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpBkB,GAAG,EAAE,QAAQ;QACbF,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,gBAEA7C,OAAA;QACE2D,OAAO,EAAE/C,cAAe;QACxBkC,KAAK,EAAElB,kBAAmB;QAC1BgC,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAG,SAAS;UAC5CgC,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,aAAa;QACjD,CAAE;QACFC,YAAY,EAAGH,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAG,SAAS;UAC5CgC,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,UAAU;QAC9C,CAAE;QACFE,KAAK,EAAC,gBAAgB;QAAApB,QAAA,eAEtB7C,OAAA,CAACJ,QAAQ;UAACsE,IAAI,EAAE;QAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAGTnD,OAAA;QACE2D,OAAO,EAAEtD,MAAM,CAACkD,SAAS,GAAG/C,WAAW,GAAGF,UAAW;QACrDwC,KAAK,EAAE;UACL,GAAGlB,kBAAkB;UACrBK,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdL,UAAU,EAAExB,MAAM,CAACkD,SAAS,GAAG,SAAS,GAAG;QAC7C,CAAE;QACFK,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAGxB,MAAM,CAACkD,SAAS,GAAG,SAAS,GAAG,SAAS;UAC3EM,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,aAAa;QACjD,CAAE;QACFC,YAAY,EAAGH,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAGxB,MAAM,CAACkD,SAAS,GAAG,SAAS,GAAG,SAAS;UAC3EM,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,UAAU;QAC9C,CAAE;QACFE,KAAK,EAAE5D,MAAM,CAACkD,SAAS,GAAG,OAAO,GAAG,MAAO;QAAAV,QAAA,EAE1CxC,MAAM,CAACkD,SAAS,gBAAGvD,OAAA,CAACN,KAAK;UAACwE,IAAI,EAAE;QAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACP,IAAI;UAACyE,IAAI,EAAE;QAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAGTnD,OAAA;QACE2D,OAAO,EAAEjD,UAAW;QACpBoC,KAAK,EAAElB,kBAAmB;QAC1BgC,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAG,SAAS;UAC5CgC,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,aAAa;QACjD,CAAE;QACFC,YAAY,EAAGH,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAG,SAAS;UAC5CgC,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACiB,SAAS,GAAG,UAAU;QAC9C,CAAE;QACFE,KAAK,EAAC,YAAY;QAAApB,QAAA,eAElB7C,OAAA,CAACL,WAAW;UAACuE,IAAI,EAAE;QAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnD,OAAA;MAAK8C,KAAK,EAAE;QACVX,OAAO,EAAE,MAAM;QACfE,cAAc,EAAE,QAAQ;QACxBiB,GAAG,EAAE,MAAM;QACXa,QAAQ,EAAE;MACZ,CAAE;MAAAtB,QAAA,gBACA7C,OAAA;QACE2D,OAAO,EAAE7C,aAAc;QACvBgC,KAAK,EAAEL,oBAAqB;QAC5BmB,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAG,SAAU;QAClEmC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAG,SAAU;QAAAgB,QAAA,gBAElE7C,OAAA,CAACH,SAAS;UAACqE,IAAI,EAAE,EAAG;UAACpB,KAAK,EAAE;YAAEsB,WAAW,EAAE;UAAS;QAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAE3D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETnD,OAAA;QACE2D,OAAO,EAAE3C,oBAAqB;QAC9B8B,KAAK,EAAE;UACL,GAAGL,oBAAoB;UACvBZ,UAAU,EAAEzB,QAAQ,CAACc,QAAQ,GAAG,SAAS,GAAG;QAC9C,CAAE;QACF0C,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAGzB,QAAQ,CAACc,QAAQ,GAAG,SAAS,GAAG,SAAS;QAC9E,CAAE;QACF8C,YAAY,EAAGH,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAAChB,KAAK,CAACjB,UAAU,GAAGzB,QAAQ,CAACc,QAAQ,GAAG,SAAS,GAAG,SAAS;QAC9E,CAAE;QAAA2B,QAAA,EAEDzC,QAAQ,CAACc,QAAQ,gBAChBlB,OAAA,CAAAE,SAAA;UAAA2C,QAAA,gBACE7C,OAAA,CAACF,MAAM;YAACoE,IAAI,EAAE,EAAG;YAACpB,KAAK,EAAE;cAAEsB,WAAW,EAAE;YAAS;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAExD;QAAA,eAAE,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;UAAA2C,QAAA,gBACE7C,OAAA,CAACP,IAAI;YAACyE,IAAI,EAAE,EAAG;YAACpB,KAAK,EAAE;cAAEsB,WAAW,EAAE;YAAS;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEtD;QAAA,eAAE;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnD,OAAA;MAAK8C,KAAK,EAAE;QACVuB,SAAS,EAAE,MAAM;QACjB3B,OAAO,EAAE,MAAM;QACfb,UAAU,EAAE,SAAS;QACrBG,YAAY,EAAE,MAAM;QACpBD,MAAM,EAAE;MACV,CAAE;MAAAc,QAAA,gBACA7C,OAAA;QAAI8C,KAAK,EAAE;UACTH,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBG,MAAM,EAAE,YAAY;UACpBjB,KAAK,EAAE;QACT,CAAE;QAAAe,QAAA,EAAC;MAEH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLnD,OAAA;QAAK8C,KAAK,EAAE;UACVX,OAAO,EAAE,MAAM;UACfkB,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE;QACP,CAAE;QAAAT,QAAA,gBACA7C,OAAA;UAAK8C,KAAK,EAAE;YACVJ,OAAO,EAAE,MAAM;YACfb,UAAU,EAAE,OAAO;YACnBG,YAAY,EAAE,KAAK;YACnBD,MAAM,EAAE;UACV,CAAE;UAAAc,QAAA,gBACA7C,OAAA;YAAA6C,QAAA,EAAQ;UAAmC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDnD,OAAA;YAAI8C,KAAK,EAAE;cAAEC,MAAM,EAAE,cAAc;cAAEuB,WAAW,EAAE;YAAS,CAAE;YAAAzB,QAAA,gBAC3D7C,OAAA;cAAA6C,QAAA,EAAI;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BnD,OAAA;cAAA6C,QAAA,EAAI;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjCnD,OAAA;cAAA6C,QAAA,EAAI;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnD,OAAA;UAAK8C,KAAK,EAAE;YACVJ,OAAO,EAAE,MAAM;YACfb,UAAU,EAAE,OAAO;YACnBG,YAAY,EAAE,KAAK;YACnBD,MAAM,EAAE;UACV,CAAE;UAAAc,QAAA,gBACA7C,OAAA;YAAA6C,QAAA,EAAQ;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCnD,OAAA;YAAG8C,KAAK,EAAE;cAAEC,MAAM,EAAE,cAAc;cAAEJ,QAAQ,EAAE,QAAQ;cAAEb,KAAK,EAAE;YAAU,CAAE;YAAAe,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACoB,EAAA,GAjSIpE,kBAAqD;AAmS3D,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}