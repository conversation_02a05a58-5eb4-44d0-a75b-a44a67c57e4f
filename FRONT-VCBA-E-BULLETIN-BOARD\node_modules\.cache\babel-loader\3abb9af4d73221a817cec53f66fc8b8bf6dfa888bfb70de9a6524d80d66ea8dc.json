{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\";\nimport React from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Smart content handling - no truncation, use scrolling for long content\n  const getContentLength = content => {\n    return content.length;\n  };\n  const isLongContent = getContentLength(announcement.content) > 400;\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '16px',\n      padding: isLongContent ? '1.5rem' : '2rem',\n      margin: '1rem 0',\n      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.08)',\n      border: isUrgent ? '3px solid #e74c3c' : '1px solid rgba(52, 152, 219, 0.15)',\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      // Ensure it fits on screen\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-30px',\n        right: '-30px',\n        width: '120px',\n        height: '120px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}15, ${getCategoryColor()}08)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '12px',\n        marginBottom: '1rem',\n        fontSize: '1.4rem',\n        fontWeight: 'bold',\n        textAlign: 'center',\n        textTransform: 'uppercase',\n        letterSpacing: '1px',\n        boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)',\n        position: 'relative',\n        zIndex: 1,\n        flexShrink: 0\n      },\n      children: announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: '4rem',\n        fontWeight: '700',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.2',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n      },\n      children: announcement.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '3rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(350px, 1fr))',\n        gap: '2rem',\n        maxHeight: '450px',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [images.slice(0, 3).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '20px',\n          overflow: 'hidden',\n          boxShadow: '0 15px 35px rgba(0, 0, 0, 0.15)',\n          border: '3px solid white',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: images.length === 1 ? '350px' : '250px',\n            objectFit: 'cover',\n            display: 'block',\n            transition: 'transform 0.3s ease'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: 0,\n            left: 0,\n            right: 0,\n            height: '60px',\n            background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.3))',\n            pointerEvents: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 13\n      }, this)), images.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',\n          borderRadius: '20px',\n          fontSize: '2.2rem',\n          fontWeight: '600',\n          color: '#6c757d',\n          border: '3px solid white',\n          boxShadow: '0 15px 35px rgba(0, 0, 0, 0.1)'\n        },\n        children: [\"+\", images.length - 3, \" more images\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '2.8rem',\n        lineHeight: '1.7',\n        margin: '2.5rem 0',\n        color: '#34495e',\n        position: 'relative',\n        zIndex: 1,\n        background: 'rgba(255, 255, 255, 0.8)',\n        padding: '2rem',\n        borderRadius: '16px',\n        border: '1px solid rgba(52, 152, 219, 0.1)'\n      },\n      children: truncateContent(announcement.content)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '3rem',\n        padding: '2rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '16px',\n        border: '1px solid rgba(52, 152, 219, 0.1)',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '3rem',\n          flexWrap: 'wrap'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '25px',\n            fontWeight: '700',\n            fontSize: '1.8rem',\n            boxShadow: `0 8px 20px ${getCategoryColor()}40`,\n            textTransform: 'uppercase',\n            letterSpacing: '1px'\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.8rem',\n            background: 'rgba(52, 152, 219, 0.1)',\n            padding: '1.5rem',\n            borderRadius: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: '#2c3e50',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '2rem'\n              },\n              children: \"\\uD83D\\uDCC5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.6rem',\n              opacity: 0.8,\n              color: '#7f8c8d',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '1.8rem'\n              },\n              children: \"\\uD83D\\uDD52\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.8rem',\n          background: 'rgba(46, 204, 113, 0.1)',\n          padding: '1.5rem',\n          borderRadius: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7,\n            fontWeight: '500'\n          },\n          children: \"Posted by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '700',\n            color: '#27ae60'\n          },\n          children: announcement.author_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "getImageUrl", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "getContentLength", "content", "length", "is<PERSON>ong<PERSON><PERSON>nt", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_path", "imageUrl", "push", "url", "alt", "title", "image_url", "attachments", "for<PERSON>ach", "attachment", "index", "file_path", "match", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "maxHeight", "display", "flexDirection", "children", "top", "right", "width", "height", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "flexShrink", "lineHeight", "textShadow", "gridTemplateColumns", "gap", "slice", "map", "image", "src", "objectFit", "transition", "onError", "e", "currentTarget", "bottom", "left", "pointerEvents", "alignItems", "justifyContent", "truncate<PERSON><PERSON><PERSON>", "marginTop", "flexWrap", "category_name", "created_at", "opacity", "author_name", "reaction_count", "comment_count", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Smart content handling - no truncation, use scrolling for long content\n  const getContentLength = (content: string) => {\n    return content.length;\n  };\n\n  const isLongContent = getContentLength(announcement.content) > 400;\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  return (\n    <div style={{\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '16px',\n      padding: isLongContent ? '1.5rem' : '2rem',\n      margin: '1rem 0',\n      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.08)',\n      border: isUrgent ? '3px solid #e74c3c' : '1px solid rgba(52, 152, 219, 0.15)',\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh', // Ensure it fits on screen\n      display: 'flex',\n      flexDirection: 'column'\n    }}>\n      {/* Background decoration - smaller for better content visibility */}\n      <div style={{\n        position: 'absolute',\n        top: '-30px',\n        right: '-30px',\n        width: '120px',\n        height: '120px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}15, ${getCategoryColor()}08)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }} />\n\n      {/* Alert indicator for urgent announcements - smaller */}\n      {isUrgent && (\n        <div style={{\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '0.8rem 1.5rem',\n          borderRadius: '12px',\n          marginBottom: '1rem',\n          fontSize: '1.4rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 4px 15px rgba(231, 76, 60, 0.3)',\n          position: 'relative',\n          zIndex: 1,\n          flexShrink: 0\n        }}>\n          {announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED'}\n        </div>\n      )}\n\n      {/* Announcement title */}\n      <h2 style={{\n        fontSize: '4rem',\n        fontWeight: '700',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.2',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n      }}>\n        {announcement.title}\n      </h2>\n\n      {/* Announcement images */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '3rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(350px, 1fr))',\n          gap: '2rem',\n          maxHeight: '450px',\n          position: 'relative',\n          zIndex: 1\n        }}>\n          {images.slice(0, 3).map((image, index) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '20px',\n                overflow: 'hidden',\n                boxShadow: '0 15px 35px rgba(0, 0, 0, 0.15)',\n                border: '3px solid white',\n                position: 'relative'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: images.length === 1 ? '350px' : '250px',\n                  objectFit: 'cover',\n                  display: 'block',\n                  transition: 'transform 0.3s ease'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n              {/* Image overlay gradient */}\n              <div style={{\n                position: 'absolute',\n                bottom: 0,\n                left: 0,\n                right: 0,\n                height: '60px',\n                background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.3))',\n                pointerEvents: 'none'\n              }} />\n            </div>\n          ))}\n          {images.length > 3 && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',\n              borderRadius: '20px',\n              fontSize: '2.2rem',\n              fontWeight: '600',\n              color: '#6c757d',\n              border: '3px solid white',\n              boxShadow: '0 15px 35px rgba(0, 0, 0, 0.1)'\n            }}>\n              +{images.length - 3} more images\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Announcement content */}\n      <div style={{\n        fontSize: '2.8rem',\n        lineHeight: '1.7',\n        margin: '2.5rem 0',\n        color: '#34495e',\n        position: 'relative',\n        zIndex: 1,\n        background: 'rgba(255, 255, 255, 0.8)',\n        padding: '2rem',\n        borderRadius: '16px',\n        border: '1px solid rgba(52, 152, 219, 0.1)'\n      }}>\n        {truncateContent(announcement.content)}\n      </div>\n\n      {/* Announcement metadata */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginTop: '3rem',\n        padding: '2rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '16px',\n        border: '1px solid rgba(52, 152, 219, 0.1)',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: '3rem', flexWrap: 'wrap' }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <div style={{\n              background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '25px',\n              fontWeight: '700',\n              fontSize: '1.8rem',\n              boxShadow: `0 8px 20px ${getCategoryColor()}40`,\n              textTransform: 'uppercase',\n              letterSpacing: '1px'\n            }}>\n              {announcement.category_name}\n            </div>\n          )}\n\n          {/* Date and time */}\n          <div style={{\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.8rem',\n            background: 'rgba(52, 152, 219, 0.1)',\n            padding: '1.5rem',\n            borderRadius: '12px'\n          }}>\n            <span style={{\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: '#2c3e50',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <span style={{ fontSize: '2rem' }}>📅</span>\n              {formatDate(announcement.created_at)}\n            </span>\n            <span style={{\n              fontSize: '1.6rem',\n              opacity: 0.8,\n              color: '#7f8c8d',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            }}>\n              <span style={{ fontSize: '1.8rem' }}>🕒</span>\n              {formatTime(announcement.created_at)}\n            </span>\n          </div>\n        </div>\n\n        {/* Author information */}\n        {announcement.author_name && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '1.8rem',\n            background: 'rgba(46, 204, 113, 0.1)',\n            padding: '1.5rem',\n            borderRadius: '12px'\n          }}>\n            <span style={{ opacity: 0.7, fontWeight: '500' }}>Posted by:</span>\n            <span style={{ fontWeight: '700', color: '#27ae60' }}>\n              {announcement.author_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{announcement.reaction_count} reactions</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{announcement.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAC1E;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,OAAe,IAAK;IAC5C,OAAOA,OAAO,CAACC,MAAM;EACvB,CAAC;EAED,MAAMC,aAAa,GAAGH,gBAAgB,CAACd,YAAY,CAACe,OAAO,CAAC,GAAG,GAAG;;EAElE;EACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIlB,YAAY,CAACmB,cAAc,EAAE;MAC/B,OAAOnB,YAAY,CAACmB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGpB,YAAY,CAACqB,QAAQ,IAAIrB,YAAY,CAACsB,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAIxB,YAAY,CAACyB,UAAU,EAAE;MAC3B,MAAMC,QAAQ,GAAG9B,WAAW,CAACI,YAAY,CAACyB,UAAU,CAAC;MACrD,IAAIC,QAAQ,EAAE;QACZF,MAAM,CAACG,IAAI,CAAC;UACVC,GAAG,EAAEF,QAAQ;UACbG,GAAG,EAAE,GAAG7B,YAAY,CAAC8B,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAI9B,YAAY,CAAC+B,SAAS,IAAI,CAAC/B,YAAY,CAACyB,UAAU,EAAE;MACtDD,MAAM,CAACG,IAAI,CAAC;QACVC,GAAG,EAAE5B,YAAY,CAAC+B,SAAS;QAC3BF,GAAG,EAAE,GAAG7B,YAAY,CAAC8B,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI9B,YAAY,CAACgC,WAAW,EAAE;MAC5BhC,YAAY,CAACgC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,SAAS,IAAIF,UAAU,CAACE,SAAS,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACrF,MAAMX,QAAQ,GAAG9B,WAAW,CAACsC,UAAU,CAACE,SAAS,CAAC;UAClD,IAAIV,QAAQ,EAAE;YACZF,MAAM,CAACG,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAG7B,YAAY,CAAC8B,KAAK,YAAYK,KAAK,GAAG,CAAC;YACjD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;EAEtC,oBACEzB,OAAA;IAAKwC,KAAK,EAAE;MACVC,UAAU,EAAE,mDAAmD;MAC/DC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAExB,aAAa,GAAG,QAAQ,GAAG,MAAM;MAC1CyB,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAExB,QAAQ,GAAG,mBAAmB,GAAG,oCAAoC;MAC7EyB,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,MAAM;MAAE;MACnBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEApD,OAAA;MAAKwC,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBM,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACff,UAAU,EAAE,0BAA0BrB,gBAAgB,CAAC,CAAC,OAAOA,gBAAgB,CAAC,CAAC,KAAK;QACtFsB,YAAY,EAAE,KAAK;QACnBe,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGJvC,QAAQ,iBACPtB,OAAA;MAAKwC,KAAK,EAAE;QACVC,UAAU,EAAE,2CAA2C;QACvDqB,KAAK,EAAE,OAAO;QACdnB,OAAO,EAAE,eAAe;QACxBD,YAAY,EAAE,MAAM;QACpBqB,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,QAAQ;QACnBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,KAAK;QACpBvB,SAAS,EAAE,mCAAmC;QAC9CE,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE,CAAC;QACTY,UAAU,EAAE;MACd,CAAE;MAAAjB,QAAA,EACClD,YAAY,CAACqB,QAAQ,GAAG,iBAAiB,GAAG;IAAQ;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CACN,eAGD7D,OAAA;MAAIwC,KAAK,EAAE;QACTwB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBrB,MAAM,EAAE,YAAY;QACpBkB,KAAK,EAAE,SAAS;QAChBQ,UAAU,EAAE,KAAK;QACjBvB,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE,CAAC;QACTc,UAAU,EAAE;MACd,CAAE;MAAAnB,QAAA,EACClD,YAAY,CAAC8B;IAAK;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAGJnC,MAAM,CAACR,MAAM,GAAG,CAAC,iBAChBlB,OAAA;MAAKwC,KAAK,EAAE;QACVuB,YAAY,EAAE,MAAM;QACpBb,OAAO,EAAE,MAAM;QACfsB,mBAAmB,EAAE9C,MAAM,CAACR,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzFuD,GAAG,EAAE,MAAM;QACXxB,SAAS,EAAE,OAAO;QAClBF,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,GACC1B,MAAM,CAACgD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEvC,KAAK,kBACnCrC,OAAA;QAEEwC,KAAK,EAAE;UACLE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE,iCAAiC;UAC5CC,MAAM,EAAE,iBAAiB;UACzBC,QAAQ,EAAE;QACZ,CAAE;QAAAK,QAAA,gBAEFpD,OAAA;UACE6E,GAAG,EAAED,KAAK,CAAC9C,GAAI;UACfC,GAAG,EAAE6C,KAAK,CAAC7C,GAAI;UACfS,KAAK,EAAE;YACLe,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE9B,MAAM,CAACR,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,OAAO;YAC/C4D,SAAS,EAAE,OAAO;YAClB5B,OAAO,EAAE,OAAO;YAChB6B,UAAU,EAAE;UACd,CAAE;UACFC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAAC1C,KAAK,CAACU,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEF7D,OAAA;UAAKwC,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpBoC,MAAM,EAAE,CAAC;YACTC,IAAI,EAAE,CAAC;YACP9B,KAAK,EAAE,CAAC;YACRE,MAAM,EAAE,MAAM;YACdf,UAAU,EAAE,kDAAkD;YAC9D4C,aAAa,EAAE;UACjB;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,GAhCAxB,KAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiCP,CACN,CAAC,EACDnC,MAAM,CAACR,MAAM,GAAG,CAAC,iBAChBlB,OAAA;QAAKwC,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfoC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxB9C,UAAU,EAAE,2CAA2C;UACvDC,YAAY,EAAE,MAAM;UACpBsB,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBH,KAAK,EAAE,SAAS;UAChBhB,MAAM,EAAE,iBAAiB;UACzBD,SAAS,EAAE;QACb,CAAE;QAAAO,QAAA,GAAC,GACA,EAAC1B,MAAM,CAACR,MAAM,GAAG,CAAC,EAAC,cACtB;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD7D,OAAA;MAAKwC,KAAK,EAAE;QACVwB,QAAQ,EAAE,QAAQ;QAClBM,UAAU,EAAE,KAAK;QACjB1B,MAAM,EAAE,UAAU;QAClBkB,KAAK,EAAE,SAAS;QAChBf,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE,CAAC;QACThB,UAAU,EAAE,0BAA0B;QACtCE,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE;MACV,CAAE;MAAAM,QAAA,EACCoC,eAAe,CAACtF,YAAY,CAACe,OAAO;IAAC;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAGN7D,OAAA;MAAKwC,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfqC,cAAc,EAAE,eAAe;QAC/BD,UAAU,EAAE,QAAQ;QACpBG,SAAS,EAAE,MAAM;QACjB9C,OAAO,EAAE,MAAM;QACfF,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE,mCAAmC;QAC3CC,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACApD,OAAA;QAAKwC,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEoC,UAAU,EAAE,QAAQ;UAAEb,GAAG,EAAE,MAAM;UAAEiB,QAAQ,EAAE;QAAO,CAAE;QAAAtC,QAAA,GAElFlD,YAAY,CAACyF,aAAa,iBACzB3F,OAAA;UAAKwC,KAAK,EAAE;YACVC,UAAU,EAAE,2BAA2BrB,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrF0C,KAAK,EAAE,OAAO;YACdnB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBuB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE,QAAQ;YAClBnB,SAAS,EAAE,cAAczB,gBAAgB,CAAC,CAAC,IAAI;YAC/C+C,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE;UACjB,CAAE;UAAAhB,QAAA,EACClD,YAAY,CAACyF;QAAa;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN,eAGD7D,OAAA;UAAKwC,KAAK,EAAE;YACVU,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBsB,GAAG,EAAE,QAAQ;YACbhC,UAAU,EAAE,yBAAyB;YACrCE,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE;UAChB,CAAE;UAAAU,QAAA,gBACApD,OAAA;YAAMwC,KAAK,EAAE;cACXwB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBH,KAAK,EAAE,SAAS;cAChBZ,OAAO,EAAE,MAAM;cACfoC,UAAU,EAAE,QAAQ;cACpBb,GAAG,EAAE;YACP,CAAE;YAAArB,QAAA,gBACApD,OAAA;cAAMwC,KAAK,EAAE;gBAAEwB,QAAQ,EAAE;cAAO,CAAE;cAAAZ,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC3C1D,UAAU,CAACD,YAAY,CAAC0F,UAAU,CAAC;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACP7D,OAAA;YAAMwC,KAAK,EAAE;cACXwB,QAAQ,EAAE,QAAQ;cAClB6B,OAAO,EAAE,GAAG;cACZ/B,KAAK,EAAE,SAAS;cAChBZ,OAAO,EAAE,MAAM;cACfoC,UAAU,EAAE,QAAQ;cACpBb,GAAG,EAAE;YACP,CAAE;YAAArB,QAAA,gBACApD,OAAA;cAAMwC,KAAK,EAAE;gBAAEwB,QAAQ,EAAE;cAAS,CAAE;cAAAZ,QAAA,EAAC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7CjD,UAAU,CAACV,YAAY,CAAC0F,UAAU,CAAC;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3D,YAAY,CAAC4F,WAAW,iBACvB9F,OAAA;QAAKwC,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfoC,UAAU,EAAE,QAAQ;UACpBb,GAAG,EAAE,MAAM;UACXT,QAAQ,EAAE,QAAQ;UAClBvB,UAAU,EAAE,yBAAyB;UACrCE,OAAO,EAAE,QAAQ;UACjBD,YAAY,EAAE;QAChB,CAAE;QAAAU,QAAA,gBACApD,OAAA;UAAMwC,KAAK,EAAE;YAAEqD,OAAO,EAAE,GAAG;YAAE5B,UAAU,EAAE;UAAM,CAAE;UAAAb,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnE7D,OAAA;UAAMwC,KAAK,EAAE;YAAEyB,UAAU,EAAE,KAAK;YAAEH,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EAClDlD,YAAY,CAAC4F;QAAW;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAC3D,YAAY,CAAC6F,cAAc,IAAI7F,YAAY,CAAC8F,aAAa,kBACzDhG,OAAA;MAAKwC,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjB9C,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBQ,OAAO,EAAE,MAAM;QACfuB,GAAG,EAAE,MAAM;QACXT,QAAQ,EAAE;MACZ,CAAE;MAAAZ,QAAA,GACClD,YAAY,CAAC6F,cAAc,IAAI7F,YAAY,CAAC6F,cAAc,GAAG,CAAC,iBAC7D/F,OAAA;QAAKwC,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEoC,UAAU,EAAE,QAAQ;UAAEb,GAAG,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACnEpD,OAAA;UAAAoD,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf7D,OAAA;UAAAoD,QAAA,GAAOlD,YAAY,CAAC6F,cAAc,EAAC,YAAU;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACN,EACA3D,YAAY,CAAC8F,aAAa,IAAI9F,YAAY,CAAC8F,aAAa,GAAG,CAAC,iBAC3DhG,OAAA;QAAKwC,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEoC,UAAU,EAAE,QAAQ;UAAEb,GAAG,EAAE;QAAS,CAAE;QAAArB,QAAA,gBACnEpD,OAAA;UAAAoD,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf7D,OAAA;UAAAoD,QAAA,GAAOlD,YAAY,CAAC8F,aAAa,EAAC,WAAS;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACoC,EAAA,GAlVIhG,cAA6C;AAoVnD,eAAeA,cAAc;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}