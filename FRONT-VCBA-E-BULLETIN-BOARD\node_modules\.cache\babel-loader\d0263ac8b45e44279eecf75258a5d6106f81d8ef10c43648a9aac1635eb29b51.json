{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVCalendarEvent.tsx\";\nimport React from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVCalendarEvent = ({\n  event\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Default red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // Truncate description if too long\n  const truncateDescription = (description, maxLength = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get event images\n  const getEventImages = () => {\n    const images = [];\n\n    // Check if event has images (from API response)\n    if (event.images && Array.isArray(event.images)) {\n      event.images.forEach((img, index) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getEventImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: isUpcoming() ? 'linear-gradient(135deg, #fff5f5 0%, #ffffff 100%)' : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '24px',\n      padding: '3rem',\n      margin: '2rem 0',\n      boxShadow: isUpcoming() ? '0 25px 70px rgba(231, 76, 60, 0.15)' : '0 20px 60px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming() ? '4px solid #e74c3c' : `3px solid ${getCategoryColor()}40`,\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), isUpcoming() && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '2rem',\n        right: '2rem',\n        background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '20px',\n        fontSize: '1.6rem',\n        fontWeight: 'bold',\n        textTransform: 'uppercase',\n        letterSpacing: '1px',\n        boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n        zIndex: 2\n      },\n      children: \"UPCOMING\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        marginBottom: '2rem',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          padding: '1rem',\n          borderRadius: '20px',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)'\n        },\n        children: getEventIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: [event.is_holiday && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.8rem',\n            fontWeight: '700',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 20px rgba(243, 156, 18, 0.3)'\n          },\n          children: \"HOLIDAY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), event.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.8rem',\n            fontWeight: '700',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 20px rgba(231, 76, 60, 0.3)'\n          },\n          children: \"IMPORTANT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: '4rem',\n        fontWeight: '700',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.2',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n      },\n      children: event.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        margin: '2rem 0',\n        padding: '2rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '20px',\n        border: '2px solid rgba(231, 76, 60, 0.2)',\n        position: 'relative',\n        zIndex: 1,\n        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n          color: 'white',\n          padding: '1.5rem',\n          borderRadius: '20px',\n          boxShadow: `0 8px 20px ${getCategoryColor()}40`\n        },\n        children: \"\\uD83D\\uDCC5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem',\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2.8rem',\n            fontWeight: '700',\n            color: '#2c3e50'\n          },\n          children: formatDateRange()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2.2rem',\n            fontWeight: '600',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n            background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '15px',\n            display: 'inline-block'\n          },\n          children: getDaysUntilEvent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1rem',\n        maxHeight: '300px'\n      },\n      children: images.slice(0, 2).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '12px',\n          overflow: 'hidden',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 9\n    }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-description\",\n      children: truncateDescription(event.description)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: getCategoryColor(),\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            fontWeight: '600',\n            fontSize: '1.6rem'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), event.subcategory_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: event.subcategory_color || '#95a5a6',\n            color: 'white',\n            padding: '0.6rem 1.2rem',\n            borderRadius: '20px',\n            fontSize: '1.4rem'\n          },\n          children: event.subcategory_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this), event.is_recurring && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.6rem',\n            color: '#8e44ad'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [event.recurrence_pattern === 'yearly' && 'Yearly', event.recurrence_pattern === 'monthly' && 'Monthly', event.recurrence_pattern === 'weekly' && 'Weekly']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Organized by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: event.created_by_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), (event.reaction_count || event.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [event.reaction_count && event.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 13\n      }, this), event.comment_count && event.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n};\n_c = TVCalendarEvent;\nexport default TVCalendarEvent;\nvar _c;\n$RefreshReg$(_c, \"TVCalendarEvent\");", "map": {"version": 3, "names": ["React", "getImageUrl", "jsxDEV", "_jsxDEV", "TVCalendarEvent", "event", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatDateRange", "startDate", "event_date", "end_date", "endDate", "startFormatted", "endFormatted", "getDaysUntilEvent", "today", "eventDate", "diffTime", "getTime", "diffDays", "Math", "ceil", "abs", "getCategoryColor", "category_color", "getEventIcon", "is_holiday", "is_recurring", "is_alert", "isUpcoming", "truncateDescription", "description", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "getEventImages", "images", "Array", "isArray", "for<PERSON>ach", "img", "index", "file_path", "imageUrl", "push", "url", "alt", "title", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "children", "top", "right", "width", "height", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "fontWeight", "textTransform", "letterSpacing", "display", "alignItems", "gap", "marginBottom", "flexWrap", "lineHeight", "textShadow", "flexDirection", "flex", "gridTemplateColumns", "maxHeight", "slice", "map", "image", "src", "objectFit", "onError", "e", "currentTarget", "className", "category_name", "subcategory_name", "subcategory_color", "recurrence_pattern", "created_by_name", "opacity", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVCalendarEvent.tsx"], "sourcesContent": ["import React from 'react';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVCalendarEventProps {\n  event: CalendarEvent;\n}\n\nconst TVCalendarEvent: React.FC<TVCalendarEventProps> = ({ event }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    \n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    \n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Default red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // Truncate description if too long\n  const truncateDescription = (description: string, maxLength: number = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get event images\n  const getEventImages = () => {\n    const images: { url: string; alt: string }[] = [];\n\n    // Check if event has images (from API response)\n    if ((event as any).images && Array.isArray((event as any).images)) {\n      (event as any).images.forEach((img: any, index: number) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getEventImages();\n\n  return (\n    <div style={{\n      background: isUpcoming()\n        ? 'linear-gradient(135deg, #fff5f5 0%, #ffffff 100%)'\n        : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '24px',\n      padding: '3rem',\n      margin: '2rem 0',\n      boxShadow: isUpcoming()\n        ? '0 25px 70px rgba(231, 76, 60, 0.15)'\n        : '0 20px 60px rgba(0, 0, 0, 0.1)',\n      border: isUpcoming()\n        ? '4px solid #e74c3c'\n        : `3px solid ${getCategoryColor()}40`,\n      position: 'relative',\n      overflow: 'hidden'\n    }}>\n      {/* Background decoration */}\n      <div style={{\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }} />\n\n      {/* Upcoming event indicator */}\n      {isUpcoming() && (\n        <div style={{\n          position: 'absolute',\n          top: '2rem',\n          right: '2rem',\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '20px',\n          fontSize: '1.6rem',\n          fontWeight: 'bold',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n          zIndex: 2\n        }}>\n          UPCOMING\n        </div>\n      )}\n      {/* Event type indicator */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        marginBottom: '2rem',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        <div style={{\n          fontSize: '4rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          padding: '1rem',\n          borderRadius: '20px',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)'\n        }}>\n          {getEventIcon()}\n        </div>\n\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {event.is_holiday && (\n            <span style={{\n              background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '20px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 8px 20px rgba(243, 156, 18, 0.3)'\n            }}>\n              HOLIDAY\n            </span>\n          )}\n          {event.is_alert && (\n            <span style={{\n              background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '20px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 8px 20px rgba(231, 76, 60, 0.3)'\n            }}>\n              IMPORTANT\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Event title */}\n      <h2 style={{\n        fontSize: '4rem',\n        fontWeight: '700',\n        margin: '0 0 2rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.2',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'\n      }}>\n        {event.title}\n      </h2>\n\n      {/* Event date with countdown */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        margin: '2rem 0',\n        padding: '2rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '20px',\n        border: '2px solid rgba(231, 76, 60, 0.2)',\n        position: 'relative',\n        zIndex: 1,\n        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)'\n      }}>\n        <div style={{\n          fontSize: '4rem',\n          background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n          color: 'white',\n          padding: '1.5rem',\n          borderRadius: '20px',\n          boxShadow: `0 8px 20px ${getCategoryColor()}40`\n        }}>\n          📅\n        </div>\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', flex: 1 }}>\n          <span style={{\n            fontSize: '2.8rem',\n            fontWeight: '700',\n            color: '#2c3e50'\n          }}>\n            {formatDateRange()}\n          </span>\n          <span style={{\n            fontSize: '2.2rem',\n            fontWeight: '600',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n            background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '15px',\n            display: 'inline-block'\n          }}>\n            {getDaysUntilEvent()}\n          </span>\n        </div>\n      </div>\n\n      {/* Event images (placeholder for future implementation) */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem',\n          maxHeight: '300px'\n        }}>\n          {images.slice(0, 2).map((image: any, index: number) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '12px',\n                overflow: 'hidden',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Event description */}\n      {event.description && (\n        <div className=\"tv-event-description\">\n          {truncateDescription(event.description)}\n        </div>\n      )}\n\n      {/* Event metadata */}\n      <div className=\"tv-event-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {event.category_name && (\n            <span \n              style={{\n                background: getCategoryColor(),\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '25px',\n                fontWeight: '600',\n                fontSize: '1.6rem'\n              }}\n            >\n              {event.category_name}\n            </span>\n          )}\n\n          {/* Subcategory */}\n          {event.subcategory_name && (\n            <span style={{\n              background: event.subcategory_color || '#95a5a6',\n              color: 'white',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              fontSize: '1.4rem'\n            }}>\n              {event.subcategory_name}\n            </span>\n          )}\n\n          {/* Recurring indicator */}\n          {event.is_recurring && (\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              gap: '0.5rem',\n              fontSize: '1.6rem',\n              color: '#8e44ad'\n            }}>\n              <span>🔄</span>\n              <span>\n                {event.recurrence_pattern === 'yearly' && 'Yearly'}\n                {event.recurrence_pattern === 'monthly' && 'Monthly'}\n                {event.recurrence_pattern === 'weekly' && 'Weekly'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Created by information */}\n        {event.created_by_name && (\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Organized by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {event.created_by_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(event.reaction_count || event.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {event.reaction_count && event.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{event.reaction_count} reactions</span>\n            </div>\n          )}\n          {event.comment_count && event.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{event.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVCalendarEvent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EACrE;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAE5C,IAAIZ,KAAK,CAACa,QAAQ,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAIV,IAAI,CAACJ,KAAK,CAACa,QAAQ,CAAC;MACxC,MAAME,cAAc,GAAGJ,SAAS,CAACN,kBAAkB,CAAC,OAAO,EAAE;QAC3DG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,MAAMO,YAAY,GAAGF,OAAO,CAACT,kBAAkB,CAAC,OAAO,EAAE;QACvDG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdF,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAO,GAAGQ,cAAc,MAAMC,YAAY,EAAE;IAC9C;IAEA,OAAOf,UAAU,CAACD,KAAK,CAACY,UAAU,CAAC;EACrC,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAC5C,MAAMQ,QAAQ,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,UAAU;IACrC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAMA,QAAQ,OAAO;IAC9C,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW;IACvC,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,WAAW;EACzC,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI1B,KAAK,CAAC2B,cAAc,EAAE;MACxB,OAAO3B,KAAK,CAAC2B,cAAc;IAC7B;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5B,KAAK,CAAC6B,UAAU,EAAE,OAAO,IAAI;IACjC,IAAI7B,KAAK,CAAC8B,YAAY,EAAE,OAAO,IAAI;IACnC,IAAI9B,KAAK,CAAC+B,QAAQ,EAAE,OAAO,IAAI;IAC/B,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMd,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAC5C,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3F,OAAOC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAGA,CAACC,WAAmB,EAAEC,SAAiB,GAAG,GAAG,KAAK;IAC5E,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,WAAW;IACvE,OAAOA,WAAW,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EAC3D,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAsC,GAAG,EAAE;;IAEjD;IACA,IAAKxC,KAAK,CAASwC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAE1C,KAAK,CAASwC,MAAM,CAAC,EAAE;MAChExC,KAAK,CAASwC,MAAM,CAACG,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAK;QACzD,IAAID,GAAG,CAACE,SAAS,EAAE;UACjB,MAAMC,QAAQ,GAAGnD,WAAW,CAACgD,GAAG,CAACE,SAAS,CAAC;UAC3C,IAAIC,QAAQ,EAAE;YACZP,MAAM,CAACQ,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGlD,KAAK,CAACmD,KAAK,YAAYN,KAAK,GAAG,CAAC;YAC1C,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOL,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,cAAc,CAAC,CAAC;EAE/B,oBACEzC,OAAA;IAAKsD,KAAK,EAAE;MACVC,UAAU,EAAErB,UAAU,CAAC,CAAC,GACpB,mDAAmD,GACnD,mDAAmD;MACvDsB,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAEzB,UAAU,CAAC,CAAC,GACnB,qCAAqC,GACrC,gCAAgC;MACpC0B,MAAM,EAAE1B,UAAU,CAAC,CAAC,GAChB,mBAAmB,GACnB,aAAaN,gBAAgB,CAAC,CAAC,IAAI;MACvCiC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEA/D,OAAA;MAAKsD,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACfZ,UAAU,EAAE,0BAA0B3B,gBAAgB,CAAC,CAAC,OAAOA,gBAAgB,CAAC,CAAC,KAAK;QACtF4B,YAAY,EAAE,KAAK;QACnBY,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGJtC,UAAU,CAAC,CAAC,iBACXlC,OAAA;MAAKsD,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbV,UAAU,EAAE,2CAA2C;QACvDkB,KAAK,EAAE,OAAO;QACdhB,OAAO,EAAE,WAAW;QACpBD,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,KAAK;QACpBlB,SAAS,EAAE,mCAAmC;QAC9CS,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,EAAC;IAEH;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDxE,OAAA;MAAKsD,KAAK,EAAE;QACVwB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE,MAAM;QACpBpB,QAAQ,EAAE,UAAU;QACpBO,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACA/D,OAAA;QAAKsD,KAAK,EAAE;UACVoB,QAAQ,EAAE,MAAM;UAChBnB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAE,MAAM;UACfD,YAAY,EAAE,MAAM;UACpBG,SAAS,EAAE;QACb,CAAE;QAAAI,QAAA,EACCjC,YAAY,CAAC;MAAC;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAENxE,OAAA;QAAKsD,KAAK,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE,MAAM;UAAEE,QAAQ,EAAE;QAAO,CAAE;QAAAnB,QAAA,GAC5D7D,KAAK,CAAC6B,UAAU,iBACf/B,OAAA;UAAMsD,KAAK,EAAE;YACXC,UAAU,EAAE,2CAA2C;YACvDkB,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBkB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBlB,SAAS,EAAE;UACb,CAAE;UAAAI,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAtE,KAAK,CAAC+B,QAAQ,iBACbjC,OAAA;UAAMsD,KAAK,EAAE;YACXC,UAAU,EAAE,2CAA2C;YACvDkB,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBkB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBlB,SAAS,EAAE;UACb,CAAE;UAAAI,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAIsD,KAAK,EAAE;QACToB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBjB,MAAM,EAAE,YAAY;QACpBe,KAAK,EAAE,SAAS;QAChBU,UAAU,EAAE,KAAK;QACjBtB,QAAQ,EAAE,UAAU;QACpBO,MAAM,EAAE,CAAC;QACTgB,UAAU,EAAE;MACd,CAAE;MAAArB,QAAA,EACC7D,KAAK,CAACmD;IAAK;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGLxE,OAAA;MAAKsD,KAAK,EAAE;QACVwB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXtB,MAAM,EAAE,QAAQ;QAChBD,OAAO,EAAE,MAAM;QACfF,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE,kCAAkC;QAC1CC,QAAQ,EAAE,UAAU;QACpBO,MAAM,EAAE,CAAC;QACTT,SAAS,EAAE;MACb,CAAE;MAAAI,QAAA,gBACA/D,OAAA;QAAKsD,KAAK,EAAE;UACVoB,QAAQ,EAAE,MAAM;UAChBnB,UAAU,EAAE,2BAA2B3B,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;UACrF6C,KAAK,EAAE,OAAO;UACdhB,OAAO,EAAE,QAAQ;UACjBD,YAAY,EAAE,MAAM;UACpBG,SAAS,EAAE,cAAc/B,gBAAgB,CAAC,CAAC;QAC7C,CAAE;QAAAmC,QAAA,EAAC;MAEH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNxE,OAAA;QAAKsD,KAAK,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEO,aAAa,EAAE,QAAQ;UAAEL,GAAG,EAAE,MAAM;UAAEM,IAAI,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC7E/D,OAAA;UAAMsD,KAAK,EAAE;YACXoB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBF,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,EACCnD,eAAe,CAAC;QAAC;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACPxE,OAAA;UAAMsD,KAAK,EAAE;YACXoB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBF,KAAK,EAAEvC,UAAU,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YAC3CqB,UAAU,EAAErB,UAAU,CAAC,CAAC,GAAG,wBAAwB,GAAG,0BAA0B;YAChFuB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBsB,OAAO,EAAE;UACX,CAAE;UAAAf,QAAA,EACC5C,iBAAiB,CAAC;QAAC;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL9B,MAAM,CAACJ,MAAM,GAAG,CAAC,iBAChBtC,OAAA;MAAKsD,KAAK,EAAE;QACV2B,YAAY,EAAE,MAAM;QACpBH,OAAO,EAAE,MAAM;QACfS,mBAAmB,EAAE7C,MAAM,CAACJ,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzF0C,GAAG,EAAE,MAAM;QACXQ,SAAS,EAAE;MACb,CAAE;MAAAzB,QAAA,EACCrB,MAAM,CAAC+C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAU,EAAE5C,KAAa,kBAChD/C,OAAA;QAEEsD,KAAK,EAAE;UACLE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE;QACb,CAAE;QAAAI,QAAA,eAEF/D,OAAA;UACE4F,GAAG,EAAED,KAAK,CAACxC,GAAI;UACfC,GAAG,EAAEuC,KAAK,CAACvC,GAAI;UACfE,KAAK,EAAE;YACLY,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACf0B,SAAS,EAAE,OAAO;YAClBf,OAAO,EAAE;UACX,CAAE;UACFgB,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAAC1C,KAAK,CAACwB,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnBGzB,KAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAtE,KAAK,CAACkC,WAAW,iBAChBpC,OAAA;MAAKiG,SAAS,EAAC,sBAAsB;MAAAlC,QAAA,EAClC5B,mBAAmB,CAACjC,KAAK,CAACkC,WAAW;IAAC;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN,eAGDxE,OAAA;MAAKiG,SAAS,EAAC,eAAe;MAAAlC,QAAA,gBAC5B/D,OAAA;QAAKsD,KAAK,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAjB,QAAA,GAEhE7D,KAAK,CAACgG,aAAa,iBAClBlG,OAAA;UACEsD,KAAK,EAAE;YACLC,UAAU,EAAE3B,gBAAgB,CAAC,CAAC;YAC9B6C,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBmB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EAED7D,KAAK,CAACgG;QAAa;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EAGAtE,KAAK,CAACiG,gBAAgB,iBACrBnG,OAAA;UAAMsD,KAAK,EAAE;YACXC,UAAU,EAAErD,KAAK,CAACkG,iBAAiB,IAAI,SAAS;YAChD3B,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBkB,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EACC7D,KAAK,CAACiG;QAAgB;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP,EAGAtE,KAAK,CAAC8B,YAAY,iBACjBhC,OAAA;UAAKsD,KAAK,EAAE;YACVwB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbN,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,gBACA/D,OAAA;YAAA+D,QAAA,EAAM;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxE,OAAA;YAAA+D,QAAA,GACG7D,KAAK,CAACmG,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EACjDnG,KAAK,CAACmG,kBAAkB,KAAK,SAAS,IAAI,SAAS,EACnDnG,KAAK,CAACmG,kBAAkB,KAAK,QAAQ,IAAI,QAAQ;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLtE,KAAK,CAACoG,eAAe,iBACpBtG,OAAA;QAAKsD,KAAK,EAAE;UACVwB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXN,QAAQ,EAAE;QACZ,CAAE;QAAAX,QAAA,gBACA/D,OAAA;UAAMsD,KAAK,EAAE;YAAEiD,OAAO,EAAE;UAAI,CAAE;UAAAxC,QAAA,EAAC;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDxE,OAAA;UAAMsD,KAAK,EAAE;YAAEqB,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAChC7D,KAAK,CAACoG;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACtE,KAAK,CAACsG,cAAc,IAAItG,KAAK,CAACuG,aAAa,kBAC3CzG,OAAA;MAAKsD,KAAK,EAAE;QACVoD,SAAS,EAAE,MAAM;QACjBjD,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBsB,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,MAAM;QACXN,QAAQ,EAAE;MACZ,CAAE;MAAAX,QAAA,GACC7D,KAAK,CAACsG,cAAc,IAAItG,KAAK,CAACsG,cAAc,GAAG,CAAC,iBAC/CxG,OAAA;QAAKsD,KAAK,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAjB,QAAA,gBACnE/D,OAAA;UAAA+D,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfxE,OAAA;UAAA+D,QAAA,GAAO7D,KAAK,CAACsG,cAAc,EAAC,YAAU;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACN,EACAtE,KAAK,CAACuG,aAAa,IAAIvG,KAAK,CAACuG,aAAa,GAAG,CAAC,iBAC7CzG,OAAA;QAAKsD,KAAK,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAjB,QAAA,gBACnE/D,OAAA;UAAA+D,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfxE,OAAA;UAAA+D,QAAA,GAAO7D,KAAK,CAACuG,aAAa,EAAC,WAAS;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACmC,EAAA,GAhZI1G,eAA+C;AAkZrD,eAAeA,eAAe;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}