{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\";\nimport React from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Truncate content if too long for TV display\n  const truncateContent = (content, maxLength = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '24px',\n      padding: '3rem',\n      margin: '2rem 0',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #e74c3c' : '2px solid rgba(52, 152, 219, 0.2)',\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n        color: 'white',\n        padding: '1.5rem 2.5rem',\n        borderRadius: '16px',\n        marginBottom: '2.5rem',\n        fontSize: '2.2rem',\n        fontWeight: 'bold',\n        textAlign: 'center',\n        textTransform: 'uppercase',\n        letterSpacing: '2px',\n        boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED ANNOUNCEMENT'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"tv-announcement-title\",\n      children: announcement.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1rem',\n        maxHeight: '400px'\n      },\n      children: [images.slice(0, 3).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '12px',\n          overflow: 'hidden',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: images.length === 1 ? '300px' : '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 13\n      }, this)), images.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: 'rgba(0, 0, 0, 0.1)',\n          borderRadius: '12px',\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          color: '#6c757d'\n        },\n        children: [\"+\", images.length - 3, \" more\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-announcement-content\",\n      children: truncateContent(announcement.content)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-announcement-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tv-announcement-category\",\n          style: {\n            backgroundColor: getCategoryColor()\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.6rem',\n              fontWeight: '600'\n            },\n            children: [\"\\uD83D\\uDCC5 \", formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '1.4rem',\n              opacity: 0.8\n            },\n            children: [\"\\uD83D\\uDD52 \", formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Posted by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: announcement.author_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [announcement.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "getImageUrl", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "truncate<PERSON><PERSON><PERSON>", "content", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_path", "imageUrl", "push", "url", "alt", "title", "image_url", "attachments", "for<PERSON>ach", "attachment", "index", "file_path", "match", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "children", "top", "right", "width", "height", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "className", "display", "gridTemplateColumns", "gap", "maxHeight", "slice", "map", "image", "src", "objectFit", "onError", "e", "currentTarget", "alignItems", "justifyContent", "category_name", "backgroundColor", "flexDirection", "created_at", "opacity", "author_name", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Truncate content if too long for TV display\n  const truncateContent = (content: string, maxLength: number = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Default blue\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  return (\n    <div style={{\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '24px',\n      padding: '3rem',\n      margin: '2rem 0',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #e74c3c' : '2px solid rgba(52, 152, 219, 0.2)',\n      position: 'relative',\n      overflow: 'hidden'\n    }}>\n      {/* Background decoration */}\n      <div style={{\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }} />\n\n      {/* Alert indicator for urgent announcements */}\n      {isUrgent && (\n        <div style={{\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1.5rem 2.5rem',\n          borderRadius: '16px',\n          marginBottom: '2.5rem',\n          fontSize: '2.2rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '2px',\n          boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n          position: 'relative',\n          zIndex: 1\n        }}>\n          {announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED ANNOUNCEMENT'}\n        </div>\n      )}\n\n      {/* Announcement title */}\n      <h2 className=\"tv-announcement-title\">\n        {announcement.title}\n      </h2>\n\n      {/* Announcement images */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem',\n          maxHeight: '400px'\n        }}>\n          {images.slice(0, 3).map((image, index) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '12px',\n                overflow: 'hidden',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: images.length === 1 ? '300px' : '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n          {images.length > 3 && (\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              background: 'rgba(0, 0, 0, 0.1)',\n              borderRadius: '12px',\n              fontSize: '1.8rem',\n              fontWeight: '600',\n              color: '#6c757d'\n            }}>\n              +{images.length - 3} more\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Announcement content */}\n      <div className=\"tv-announcement-content\">\n        {truncateContent(announcement.content)}\n      </div>\n\n      {/* Announcement metadata */}\n      <div className=\"tv-announcement-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <span \n              className=\"tv-announcement-category\"\n              style={{ backgroundColor: getCategoryColor() }}\n            >\n              {announcement.category_name}\n            </span>\n          )}\n\n          {/* Date and time */}\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n            <span style={{ fontSize: '1.6rem', fontWeight: '600' }}>\n              📅 {formatDate(announcement.created_at)}\n            </span>\n            <span style={{ fontSize: '1.4rem', opacity: 0.8 }}>\n              🕒 {formatTime(announcement.created_at)}\n            </span>\n          </div>\n        </div>\n\n        {/* Author information */}\n        {announcement.author_name && (\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Posted by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {announcement.author_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{announcement.reaction_count} reactions</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{announcement.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAC1E;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,OAAe,EAAEC,SAAiB,GAAG,GAAG,KAAK;IACpE,IAAID,OAAO,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,OAAO;IAC/C,OAAOA,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EACvD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,YAAY,CAACqB,cAAc,EAAE;MAC/B,OAAOrB,YAAY,CAACqB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGtB,YAAY,CAACuB,QAAQ,IAAIvB,YAAY,CAACwB,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI1B,YAAY,CAAC2B,UAAU,EAAE;MAC3B,MAAMC,QAAQ,GAAGhC,WAAW,CAACI,YAAY,CAAC2B,UAAU,CAAC;MACrD,IAAIC,QAAQ,EAAE;QACZF,MAAM,CAACG,IAAI,CAAC;UACVC,GAAG,EAAEF,QAAQ;UACbG,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIhC,YAAY,CAACiC,SAAS,IAAI,CAACjC,YAAY,CAAC2B,UAAU,EAAE;MACtDD,MAAM,CAACG,IAAI,CAAC;QACVC,GAAG,EAAE9B,YAAY,CAACiC,SAAS;QAC3BF,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIhC,YAAY,CAACkC,WAAW,EAAE;MAC5BlC,YAAY,CAACkC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,SAAS,IAAIF,UAAU,CAACE,SAAS,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACrF,MAAMX,QAAQ,GAAGhC,WAAW,CAACwC,UAAU,CAACE,SAAS,CAAC;UAClD,IAAIV,QAAQ,EAAE;YACZF,MAAM,CAACG,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAG/B,YAAY,CAACgC,KAAK,YAAYK,KAAK,GAAG,CAAC;YACjD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;EAEtC,oBACE3B,OAAA;IAAK0C,KAAK,EAAE;MACVC,UAAU,EAAE,mDAAmD;MAC/DC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAExB,QAAQ,GAAG,mBAAmB,GAAG,mCAAmC;MAC5EyB,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAnD,OAAA;MAAK0C,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACfZ,UAAU,EAAE,0BAA0BrB,gBAAgB,CAAC,CAAC,OAAOA,gBAAgB,CAAC,CAAC,KAAK;QACtFsB,YAAY,EAAE,KAAK;QACnBY,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGJpC,QAAQ,iBACPxB,OAAA;MAAK0C,KAAK,EAAE;QACVC,UAAU,EAAE,2CAA2C;QACvDkB,KAAK,EAAE,OAAO;QACdhB,OAAO,EAAE,eAAe;QACxBD,YAAY,EAAE,MAAM;QACpBkB,YAAY,EAAE,QAAQ;QACtBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,QAAQ;QACnBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,KAAK;QACpBpB,SAAS,EAAE,mCAAmC;QAC9CE,QAAQ,EAAE,UAAU;QACpBO,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,EACCjD,YAAY,CAACuB,QAAQ,GAAG,iBAAiB,GAAG;IAAqB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACN,eAGD5D,OAAA;MAAIoE,SAAS,EAAC,uBAAuB;MAAAjB,QAAA,EAClCjD,YAAY,CAACgC;IAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,EAGJhC,MAAM,CAACT,MAAM,GAAG,CAAC,iBAChBnB,OAAA;MAAK0C,KAAK,EAAE;QACVoB,YAAY,EAAE,MAAM;QACpBO,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAE1C,MAAM,CAACT,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzFoD,GAAG,EAAE,MAAM;QACXC,SAAS,EAAE;MACb,CAAE;MAAArB,QAAA,GACCvB,MAAM,CAAC6C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEpC,KAAK,kBACnCvC,OAAA;QAEE0C,KAAK,EAAE;UACLE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE;QACb,CAAE;QAAAI,QAAA,eAEFnD,OAAA;UACE4E,GAAG,EAAED,KAAK,CAAC3C,GAAI;UACfC,GAAG,EAAE0C,KAAK,CAAC1C,GAAI;UACfS,KAAK,EAAE;YACLY,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE3B,MAAM,CAACT,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,OAAO;YAC/C0D,SAAS,EAAE,OAAO;YAClBR,OAAO,EAAE;UACX,CAAE;UACFS,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAACtC,KAAK,CAAC2B,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnBGrB,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN,CAAC,EACDhC,MAAM,CAACT,MAAM,GAAG,CAAC,iBAChBnB,OAAA;QAAK0C,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBvC,UAAU,EAAE,oBAAoB;UAChCC,YAAY,EAAE,MAAM;UACpBmB,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBH,KAAK,EAAE;QACT,CAAE;QAAAV,QAAA,GAAC,GACA,EAACvB,MAAM,CAACT,MAAM,GAAG,CAAC,EAAC,OACtB;MAAA;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD5D,OAAA;MAAKoE,SAAS,EAAC,yBAAyB;MAAAjB,QAAA,EACrCnC,eAAe,CAACd,YAAY,CAACe,OAAO;IAAC;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAGN5D,OAAA;MAAKoE,SAAS,EAAC,sBAAsB;MAAAjB,QAAA,gBACnCnD,OAAA;QAAK0C,KAAK,EAAE;UAAE2B,OAAO,EAAE,MAAM;UAAEY,UAAU,EAAE,QAAQ;UAAEV,GAAG,EAAE;QAAO,CAAE;QAAApB,QAAA,GAEhEjD,YAAY,CAACiF,aAAa,iBACzBnF,OAAA;UACEoE,SAAS,EAAC,0BAA0B;UACpC1B,KAAK,EAAE;YAAE0C,eAAe,EAAE9D,gBAAgB,CAAC;UAAE,CAAE;UAAA6B,QAAA,EAE9CjD,YAAY,CAACiF;QAAa;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CACP,eAGD5D,OAAA;UAAK0C,KAAK,EAAE;YAAE2B,OAAO,EAAE,MAAM;YAAEgB,aAAa,EAAE,QAAQ;YAAEd,GAAG,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACtEnD,OAAA;YAAM0C,KAAK,EAAE;cAAEqB,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAb,QAAA,GAAC,eACnD,EAAChD,UAAU,CAACD,YAAY,CAACoF,UAAU,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACP5D,OAAA;YAAM0C,KAAK,EAAE;cAAEqB,QAAQ,EAAE,QAAQ;cAAEwB,OAAO,EAAE;YAAI,CAAE;YAAApC,QAAA,GAAC,eAC9C,EAACvC,UAAU,CAACV,YAAY,CAACoF,UAAU,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL1D,YAAY,CAACsF,WAAW,iBACvBxF,OAAA;QAAK0C,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBV,GAAG,EAAE,MAAM;UACXR,QAAQ,EAAE;QACZ,CAAE;QAAAZ,QAAA,gBACAnD,OAAA;UAAM0C,KAAK,EAAE;YAAE6C,OAAO,EAAE;UAAI,CAAE;UAAApC,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChD5D,OAAA;UAAM0C,KAAK,EAAE;YAAEsB,UAAU,EAAE;UAAM,CAAE;UAAAb,QAAA,EAChCjD,YAAY,CAACsF;QAAW;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAC1D,YAAY,CAACuF,cAAc,IAAIvF,YAAY,CAACwF,aAAa,kBACzD1F,OAAA;MAAK0C,KAAK,EAAE;QACViD,SAAS,EAAE,MAAM;QACjB9C,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpByB,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,MAAM;QACXR,QAAQ,EAAE;MACZ,CAAE;MAAAZ,QAAA,GACCjD,YAAY,CAACuF,cAAc,IAAIvF,YAAY,CAACuF,cAAc,GAAG,CAAC,iBAC7DzF,OAAA;QAAK0C,KAAK,EAAE;UAAE2B,OAAO,EAAE,MAAM;UAAEY,UAAU,EAAE,QAAQ;UAAEV,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnEnD,OAAA;UAAAmD,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf5D,OAAA;UAAAmD,QAAA,GAAOjD,YAAY,CAACuF,cAAc,EAAC,YAAU;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACN,EACA1D,YAAY,CAACwF,aAAa,IAAIxF,YAAY,CAACwF,aAAa,GAAG,CAAC,iBAC3D1F,OAAA;QAAK0C,KAAK,EAAE;UAAE2B,OAAO,EAAE,MAAM;UAAEY,UAAU,EAAE,QAAQ;UAAEV,GAAG,EAAE;QAAS,CAAE;QAAApB,QAAA,gBACnEnD,OAAA;UAAAmD,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACf5D,OAAA;UAAAmD,QAAA,GAAOjD,YAAY,CAACwF,aAAa,EAAC,WAAS;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACgC,EAAA,GA5PI3F,cAA6C;AA8PnD,eAAeA,cAAc;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}