import React, { useState, useEffect } from 'react';
import { tvControlService, TVDisplaySettings } from '../../../services/tvControlService';
import { useAnnouncements } from '../../../hooks/useAnnouncements';
import { useCalendar } from '../../../hooks/useCalendar';
import { Eye, EyeOff, Filter, RefreshCw } from 'lucide-react';

interface TVContentManagerProps {
  settings: TVDisplaySettings;
}

const TVContentManager: React.FC<TVContentManagerProps> = ({ settings }) => {
  const [selectedCategories, setSelectedCategories] = useState<number[]>(settings.announcementCategories);
  const [selectedEventCategories, setSelectedEventCategories] = useState<number[]>(settings.eventCategories);

  // Fetch current content
  const {
    announcements,
    loading: announcementsLoading,
    refresh: refreshAnnouncements
  } = useAnnouncements({
    status: 'published',
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'DESC'
  }, true);

  const {
    events,
    loading: eventsLoading,
    refresh: refreshEvents
  } = useCalendar(new Date());

  const handleCategoryToggle = (categoryId: number, isEvent: boolean = false) => {
    if (isEvent) {
      const newCategories = selectedEventCategories.includes(categoryId)
        ? selectedEventCategories.filter(id => id !== categoryId)
        : [...selectedEventCategories, categoryId];
      
      setSelectedEventCategories(newCategories);
      tvControlService.updateSettings({
        eventCategories: newCategories
      });
    } else {
      const newCategories = selectedCategories.includes(categoryId)
        ? selectedCategories.filter(id => id !== categoryId)
        : [...selectedCategories, categoryId];
      
      setSelectedCategories(newCategories);
      tvControlService.updateSettings({
        announcementCategories: newCategories
      });
    }
  };

  const handleRefreshAll = () => {
    refreshAnnouncements();
    refreshEvents();
    tvControlService.refresh();
  };

  // Get unique categories from announcements
  const announcementCategories = Array.from(
    new Set(announcements.map(a => ({ id: a.category_id, name: a.category_name, color: a.category_color })))
  ).filter(cat => cat.name);

  // Get unique categories from events
  const eventCategories = Array.from(
    new Set(events.map(e => ({ id: e.category_id, name: e.category_name, color: e.category_color })))
  ).filter(cat => cat.name && cat.id);

  // Filter content based on settings
  const filteredAnnouncements = announcements.filter(announcement => {
    if (!settings.showAnnouncements) return false;
    if (selectedCategories.length === 0) return true;
    return selectedCategories.includes(announcement.category_id);
  }).slice(0, settings.maxAnnouncements);

  const filteredEvents = events.filter(event => {
    if (!settings.showCalendarEvents) return false;
    if (selectedEventCategories.length === 0) return true;
    return event.category_id && selectedEventCategories.includes(event.category_id);
  }).slice(0, settings.maxEvents);

  const cardStyle = {
    background: 'white',
    border: '1px solid #e9ecef',
    borderRadius: '8px',
    padding: '1rem',
    marginBottom: '1rem'
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <h2 style={{
          fontSize: '1.8rem',
          fontWeight: '600',
          margin: 0,
          color: '#2c3e50'
        }}>
          Content Management
        </h2>

        <button
          onClick={handleRefreshAll}
          style={{
            background: '#17a2b8',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '6px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}
        >
          <RefreshCw size={16} />
          Refresh Content
        </button>
      </div>

      {/* Content Summary */}
      <div style={{
        background: '#f8f9fa',
        borderRadius: '8px',
        padding: '1.5rem',
        marginBottom: '2rem',
        border: '1px solid #e9ecef'
      }}>
        <h3 style={{ margin: '0 0 1rem 0', color: '#2c3e50' }}>Currently Displayed</h3>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{
            background: 'white',
            padding: '1rem',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3498db' }}>
              {filteredAnnouncements.length}
            </div>
            <div style={{ color: '#6c757d' }}>Announcements</div>
          </div>
          <div style={{
            background: 'white',
            padding: '1rem',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#e74c3c' }}>
              {filteredEvents.length}
            </div>
            <div style={{ color: '#6c757d' }}>Calendar Events</div>
          </div>
          <div style={{
            background: 'white',
            padding: '1rem',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#28a745' }}>
              {filteredAnnouncements.length + filteredEvents.length}
            </div>
            <div style={{ color: '#6c757d' }}>Total Slides</div>
          </div>
        </div>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '2rem'
      }}>
        {/* Announcement Categories */}
        <div>
          <h3 style={{
            fontSize: '1.4rem',
            fontWeight: '600',
            margin: '0 0 1rem 0',
            color: '#2c3e50',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <Filter size={20} />
            Announcement Categories
          </h3>

          {announcementCategories.length === 0 ? (
            <div style={{
              ...cardStyle,
              textAlign: 'center',
              color: '#6c757d'
            }}>
              No announcement categories found
            </div>
          ) : (
            announcementCategories.map((category) => (
              <div key={category.id} style={cardStyle}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <div
                      style={{
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        background: category.color || '#3498db'
                      }}
                    />
                    <span style={{ fontWeight: '500' }}>{category.name}</span>
                  </div>
                  
                  <button
                    onClick={() => handleCategoryToggle(category.id)}
                    style={{
                      background: selectedCategories.includes(category.id) ? '#28a745' : '#6c757d',
                      color: 'white',
                      border: 'none',
                      padding: '0.5rem',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {selectedCategories.includes(category.id) ? <Eye size={16} /> : <EyeOff size={16} />}
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Event Categories */}
        <div>
          <h3 style={{
            fontSize: '1.4rem',
            fontWeight: '600',
            margin: '0 0 1rem 0',
            color: '#2c3e50',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <Filter size={20} />
            Event Categories
          </h3>

          {eventCategories.length === 0 ? (
            <div style={{
              ...cardStyle,
              textAlign: 'center',
              color: '#6c757d'
            }}>
              No event categories found
            </div>
          ) : (
            eventCategories.map((category) => (
              <div key={category.id} style={cardStyle}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <div
                      style={{
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        background: category.color || '#e74c3c'
                      }}
                    />
                    <span style={{ fontWeight: '500' }}>{category.name}</span>
                  </div>
                  
                  <button
                    onClick={() => handleCategoryToggle(category.id!, true)}
                    style={{
                      background: selectedEventCategories.includes(category.id!) ? '#28a745' : '#6c757d',
                      color: 'white',
                      border: 'none',
                      padding: '0.5rem',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {selectedEventCategories.includes(category.id!) ? <Eye size={16} /> : <EyeOff size={16} />}
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Help */}
      <div style={{
        marginTop: '2rem',
        background: '#e8f5e8',
        border: '1px solid #c3e6c3',
        borderRadius: '8px',
        padding: '1.5rem'
      }}>
        <h4 style={{ margin: '0 0 1rem 0', color: '#155724' }}>💡 Content Filtering</h4>
        <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#155724' }}>
          <li>Toggle categories on/off to control what appears on the TV display</li>
          <li>When no categories are selected, all content is shown</li>
          <li>Changes apply immediately to the live display</li>
          <li>Use this to focus on specific types of announcements or events</li>
        </ul>
      </div>
    </div>
  );
};

export default TVContentManager;
