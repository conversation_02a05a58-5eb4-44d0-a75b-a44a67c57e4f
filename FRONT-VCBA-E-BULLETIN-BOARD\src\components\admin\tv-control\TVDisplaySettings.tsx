import React, { useState } from 'react';
import { tvControlService, TVDisplaySettings as TVSettings } from '../../../services/tvControlService';
import { Save, RotateCcw } from 'lucide-react';

interface TVDisplaySettingsProps {
  settings: TVSettings;
}

const TVDisplaySettings: React.FC<TVDisplaySettingsProps> = ({ settings }) => {
  const [localSettings, setLocalSettings] = useState<TVSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  const handleSettingChange = (key: keyof TVSettings, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    setHasChanges(JSON.stringify(newSettings) !== JSON.stringify(settings));
  };

  const handleSave = () => {
    tvControlService.updateSettings(localSettings);
    setHasChanges(false);
  };

  const handleReset = () => {
    setLocalSettings(settings);
    setHasChanges(false);
  };

  const inputStyle = {
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #ddd',
    borderRadius: '6px',
    fontSize: '1rem'
  };

  const labelStyle = {
    display: 'block',
    marginBottom: '0.5rem',
    fontWeight: '500',
    color: '#2c3e50'
  };

  const sectionStyle = {
    marginBottom: '2rem',
    padding: '1.5rem',
    background: '#f8f9fa',
    borderRadius: '8px',
    border: '1px solid #e9ecef'
  };

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <h2 style={{
          fontSize: '1.8rem',
          fontWeight: '600',
          margin: 0,
          color: '#2c3e50'
        }}>
          Display Settings
        </h2>

        {hasChanges && (
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button
              onClick={handleReset}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '6px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <RotateCcw size={16} />
              Reset
            </button>
            <button
              onClick={handleSave}
              style={{
                background: '#28a745',
                color: 'white',
                border: 'none',
                padding: '0.75rem 1.5rem',
                borderRadius: '6px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <Save size={16} />
              Save Changes
            </button>
          </div>
        )}
      </div>

      {/* General Settings */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 1.5rem 0', color: '#2c3e50' }}>General Settings</h3>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          <div>
            <label style={labelStyle}>
              Display Enabled
            </label>
            <select
              value={localSettings.isEnabled ? 'true' : 'false'}
              onChange={(e) => handleSettingChange('isEnabled', e.target.value === 'true')}
              style={inputStyle}
            >
              <option value="true">Enabled</option>
              <option value="false">Disabled</option>
            </select>
          </div>

          <div>
            <label style={labelStyle}>
              Auto-Play
            </label>
            <select
              value={localSettings.autoPlay ? 'true' : 'false'}
              onChange={(e) => handleSettingChange('autoPlay', e.target.value === 'true')}
              style={inputStyle}
            >
              <option value="true">Enabled</option>
              <option value="false">Disabled</option>
            </select>
          </div>

          <div>
            <label style={labelStyle}>
              Slide Interval (seconds)
            </label>
            <input
              type="number"
              min="5"
              max="300"
              value={localSettings.slideInterval / 1000}
              onChange={(e) => handleSettingChange('slideInterval', parseInt(e.target.value) * 1000)}
              style={inputStyle}
            />
          </div>

          <div>
            <label style={labelStyle}>
              Transition Type
            </label>
            <select
              value={localSettings.transitionType}
              onChange={(e) => handleSettingChange('transitionType', e.target.value)}
              style={inputStyle}
            >
              <option value="slide">Slide</option>
              <option value="fade">Fade</option>
              <option value="none">None</option>
            </select>
          </div>
        </div>
      </div>

      {/* Content Settings */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 1.5rem 0', color: '#2c3e50' }}>Content Settings</h3>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          <div>
            <label style={labelStyle}>
              Show Announcements
            </label>
            <select
              value={localSettings.showAnnouncements ? 'true' : 'false'}
              onChange={(e) => handleSettingChange('showAnnouncements', e.target.value === 'true')}
              style={inputStyle}
            >
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>

          <div>
            <label style={labelStyle}>
              Show Calendar Events
            </label>
            <select
              value={localSettings.showCalendarEvents ? 'true' : 'false'}
              onChange={(e) => handleSettingChange('showCalendarEvents', e.target.value === 'true')}
              style={inputStyle}
            >
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>

          <div>
            <label style={labelStyle}>
              Max Announcements
            </label>
            <input
              type="number"
              min="1"
              max="50"
              value={localSettings.maxAnnouncements}
              onChange={(e) => handleSettingChange('maxAnnouncements', parseInt(e.target.value))}
              style={inputStyle}
            />
          </div>

          <div>
            <label style={labelStyle}>
              Max Calendar Events
            </label>
            <input
              type="number"
              min="1"
              max="20"
              value={localSettings.maxEvents}
              onChange={(e) => handleSettingChange('maxEvents', parseInt(e.target.value))}
              style={inputStyle}
            />
          </div>
        </div>
      </div>

      {/* Preview Settings */}
      <div style={sectionStyle}>
        <h3 style={{ margin: '0 0 1.5rem 0', color: '#2c3e50' }}>Preview</h3>
        
        <div style={{
          background: 'white',
          border: '2px solid #dee2e6',
          borderRadius: '8px',
          padding: '2rem',
          textAlign: 'center'
        }}>
          <div style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            marginBottom: '1rem',
            color: '#2c3e50'
          }}>
            Current Settings Summary
          </div>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            textAlign: 'left'
          }}>
            <div>
              <strong>Status:</strong> {localSettings.isEnabled ? 'Enabled' : 'Disabled'}
            </div>
            <div>
              <strong>Auto-Play:</strong> {localSettings.autoPlay ? 'On' : 'Off'}
            </div>
            <div>
              <strong>Slide Duration:</strong> {localSettings.slideInterval / 1000}s
            </div>
            <div>
              <strong>Transition:</strong> {localSettings.transitionType}
            </div>
            <div>
              <strong>Announcements:</strong> {localSettings.showAnnouncements ? `Yes (${localSettings.maxAnnouncements})` : 'No'}
            </div>
            <div>
              <strong>Events:</strong> {localSettings.showCalendarEvents ? `Yes (${localSettings.maxEvents})` : 'No'}
            </div>
          </div>
        </div>
      </div>

      {/* Help Section */}
      <div style={{
        background: '#e3f2fd',
        border: '1px solid #bbdefb',
        borderRadius: '8px',
        padding: '1.5rem'
      }}>
        <h4 style={{ margin: '0 0 1rem 0', color: '#1976d2' }}>💡 Tips</h4>
        <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#1565c0' }}>
          <li>Changes are applied immediately to the TV display</li>
          <li>Recommended slide interval: 10-20 seconds for optimal readability</li>
          <li>Disable auto-play for manual control during presentations</li>
          <li>Use fade transitions for a more professional look</li>
        </ul>
      </div>
    </div>
  );
};

export default TVDisplaySettings;
