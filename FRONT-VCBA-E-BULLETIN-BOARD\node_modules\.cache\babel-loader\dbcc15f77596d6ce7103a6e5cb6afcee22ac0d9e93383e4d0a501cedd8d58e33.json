{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVCalendarEvent.tsx\";\nimport React from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVCalendarEvent = ({\n  event\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Default red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // Smart description handling\n  const getDescriptionLength = description => {\n    return description ? description.length : 0;\n  };\n  const isLongDescription = event.description && getDescriptionLength(event.description) > 300;\n\n  // Get event images\n  const getEventImages = () => {\n    const images = [];\n\n    // Check if event has images (from API response)\n    if (event.images && Array.isArray(event.images)) {\n      event.images.forEach((img, index) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getEventImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: isUpcoming() ? 'linear-gradient(135deg, #fff5f5 0%, #ffffff 100%)' : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '16px',\n      padding: isLongDescription ? '1.5rem' : '2rem',\n      margin: '1rem 0',\n      boxShadow: isUpcoming() ? '0 8px 25px rgba(231, 76, 60, 0.12)' : '0 8px 25px rgba(0, 0, 0, 0.08)',\n      border: isUpcoming() ? '3px solid #e74c3c' : `2px solid ${getCategoryColor()}30`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), isUpcoming() && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '2rem',\n        right: '2rem',\n        background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '20px',\n        fontSize: '1.6rem',\n        fontWeight: 'bold',\n        textTransform: 'uppercase',\n        letterSpacing: '1px',\n        boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n        zIndex: 2\n      },\n      children: \"UPCOMING\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        marginBottom: '2rem',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          padding: '1rem',\n          borderRadius: '20px',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)'\n        },\n        children: getEventIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap'\n        },\n        children: [event.is_holiday && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.8rem',\n            fontWeight: '700',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 20px rgba(243, 156, 18, 0.3)'\n          },\n          children: \"HOLIDAY\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this), event.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '20px',\n            fontSize: '1.8rem',\n            fontWeight: '700',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 8px 20px rgba(231, 76, 60, 0.3)'\n          },\n          children: \"IMPORTANT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      style: {\n        fontSize: isLongDescription ? '2.2rem' : '2.8rem',\n        fontWeight: '700',\n        margin: '0 0 1rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.3',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n        flexShrink: 0,\n        wordWrap: 'break-word',\n        hyphens: 'auto'\n      },\n      children: event.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        margin: '2rem 0',\n        padding: '2rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '20px',\n        border: '2px solid rgba(231, 76, 60, 0.2)',\n        position: 'relative',\n        zIndex: 1,\n        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n          color: 'white',\n          padding: '1.5rem',\n          borderRadius: '20px',\n          boxShadow: `0 8px 20px ${getCategoryColor()}40`\n        },\n        children: \"\\uD83D\\uDCC5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem',\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2.8rem',\n            fontWeight: '700',\n            color: '#2c3e50'\n          },\n          children: formatDateRange()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2.2rem',\n            fontWeight: '600',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n            background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '15px',\n            display: 'inline-block'\n          },\n          children: getDaysUntilEvent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1rem',\n        maxHeight: '300px'\n      },\n      children: images.slice(0, 2).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '12px',\n          overflow: 'hidden',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 9\n    }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-description\",\n      children: truncateDescription(event.description)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: getCategoryColor(),\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            fontWeight: '600',\n            fontSize: '1.6rem'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), event.subcategory_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: event.subcategory_color || '#95a5a6',\n            color: 'white',\n            padding: '0.6rem 1.2rem',\n            borderRadius: '20px',\n            fontSize: '1.4rem'\n          },\n          children: event.subcategory_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), event.is_recurring && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.6rem',\n            color: '#8e44ad'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [event.recurrence_pattern === 'yearly' && 'Yearly', event.recurrence_pattern === 'monthly' && 'Monthly', event.recurrence_pattern === 'weekly' && 'Weekly']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Organized by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: event.created_by_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), (event.reaction_count || event.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [event.reaction_count && event.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 13\n      }, this), event.comment_count && event.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_c = TVCalendarEvent;\nexport default TVCalendarEvent;\nvar _c;\n$RefreshReg$(_c, \"TVCalendarEvent\");", "map": {"version": 3, "names": ["React", "getImageUrl", "jsxDEV", "_jsxDEV", "TVCalendarEvent", "event", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatDateRange", "startDate", "event_date", "end_date", "endDate", "startFormatted", "endFormatted", "getDaysUntilEvent", "today", "eventDate", "diffTime", "getTime", "diffDays", "Math", "ceil", "abs", "getCategoryColor", "category_color", "getEventIcon", "is_holiday", "is_recurring", "is_alert", "isUpcoming", "getDescriptionLength", "description", "length", "isLongDescription", "getEventImages", "images", "Array", "isArray", "for<PERSON>ach", "img", "index", "file_path", "imageUrl", "push", "url", "alt", "title", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "maxHeight", "display", "flexDirection", "children", "top", "right", "width", "height", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "fontSize", "fontWeight", "textTransform", "letterSpacing", "alignItems", "gap", "marginBottom", "flexWrap", "lineHeight", "textShadow", "flexShrink", "wordWrap", "hyphens", "flex", "gridTemplateColumns", "slice", "map", "image", "src", "objectFit", "onError", "e", "currentTarget", "className", "truncateDescription", "category_name", "subcategory_name", "subcategory_color", "recurrence_pattern", "created_by_name", "opacity", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVCalendarEvent.tsx"], "sourcesContent": ["import React from 'react';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVCalendarEventProps {\n  event: CalendarEvent;\n}\n\nconst TVCalendarEvent: React.FC<TVCalendarEventProps> = ({ event }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    \n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    \n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Default red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // Smart description handling\n  const getDescriptionLength = (description: string) => {\n    return description ? description.length : 0;\n  };\n\n  const isLongDescription = event.description && getDescriptionLength(event.description) > 300;\n\n  // Get event images\n  const getEventImages = () => {\n    const images: { url: string; alt: string }[] = [];\n\n    // Check if event has images (from API response)\n    if ((event as any).images && Array.isArray((event as any).images)) {\n      (event as any).images.forEach((img: any, index: number) => {\n        if (img.file_path) {\n          const imageUrl = getImageUrl(img.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${event.title} - Image ${index + 1}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getEventImages();\n\n  return (\n    <div style={{\n      background: isUpcoming()\n        ? 'linear-gradient(135deg, #fff5f5 0%, #ffffff 100%)'\n        : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',\n      borderRadius: '16px',\n      padding: isLongDescription ? '1.5rem' : '2rem',\n      margin: '1rem 0',\n      boxShadow: isUpcoming()\n        ? '0 8px 25px rgba(231, 76, 60, 0.12)'\n        : '0 8px 25px rgba(0, 0, 0, 0.08)',\n      border: isUpcoming()\n        ? '3px solid #e74c3c'\n        : `2px solid ${getCategoryColor()}30`,\n      position: 'relative',\n      overflow: 'hidden',\n      maxHeight: '85vh',\n      display: 'flex',\n      flexDirection: 'column'\n    }}>\n      {/* Background decoration */}\n      <div style={{\n        position: 'absolute',\n        top: '-50px',\n        right: '-50px',\n        width: '200px',\n        height: '200px',\n        background: `linear-gradient(45deg, ${getCategoryColor()}20, ${getCategoryColor()}10)`,\n        borderRadius: '50%',\n        zIndex: 0\n      }} />\n\n      {/* Upcoming event indicator */}\n      {isUpcoming() && (\n        <div style={{\n          position: 'absolute',\n          top: '2rem',\n          right: '2rem',\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '20px',\n          fontSize: '1.6rem',\n          fontWeight: 'bold',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)',\n          zIndex: 2\n        }}>\n          UPCOMING\n        </div>\n      )}\n      {/* Event type indicator */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        marginBottom: '2rem',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        <div style={{\n          fontSize: '4rem',\n          background: 'rgba(255, 255, 255, 0.9)',\n          padding: '1rem',\n          borderRadius: '20px',\n          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)'\n        }}>\n          {getEventIcon()}\n        </div>\n\n        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n          {event.is_holiday && (\n            <span style={{\n              background: 'linear-gradient(135deg, #f39c12, #e67e22)',\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '20px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 8px 20px rgba(243, 156, 18, 0.3)'\n            }}>\n              HOLIDAY\n            </span>\n          )}\n          {event.is_alert && (\n            <span style={{\n              background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n              color: 'white',\n              padding: '1rem 2rem',\n              borderRadius: '20px',\n              fontSize: '1.8rem',\n              fontWeight: '700',\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              boxShadow: '0 8px 20px rgba(231, 76, 60, 0.3)'\n            }}>\n              IMPORTANT\n            </span>\n          )}\n        </div>\n      </div>\n\n      {/* Event title - responsive sizing */}\n      <h2 style={{\n        fontSize: isLongDescription ? '2.2rem' : '2.8rem',\n        fontWeight: '700',\n        margin: '0 0 1rem 0',\n        color: '#2c3e50',\n        lineHeight: '1.3',\n        position: 'relative',\n        zIndex: 1,\n        textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',\n        flexShrink: 0,\n        wordWrap: 'break-word',\n        hyphens: 'auto'\n      }}>\n        {event.title}\n      </h2>\n\n      {/* Event date with countdown */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '2rem',\n        margin: '2rem 0',\n        padding: '2rem',\n        background: 'rgba(255, 255, 255, 0.9)',\n        borderRadius: '20px',\n        border: '2px solid rgba(231, 76, 60, 0.2)',\n        position: 'relative',\n        zIndex: 1,\n        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)'\n      }}>\n        <div style={{\n          fontSize: '4rem',\n          background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n          color: 'white',\n          padding: '1.5rem',\n          borderRadius: '20px',\n          boxShadow: `0 8px 20px ${getCategoryColor()}40`\n        }}>\n          📅\n        </div>\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', flex: 1 }}>\n          <span style={{\n            fontSize: '2.8rem',\n            fontWeight: '700',\n            color: '#2c3e50'\n          }}>\n            {formatDateRange()}\n          </span>\n          <span style={{\n            fontSize: '2.2rem',\n            fontWeight: '600',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d',\n            background: isUpcoming() ? 'rgba(39, 174, 96, 0.1)' : 'rgba(127, 140, 141, 0.1)',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '15px',\n            display: 'inline-block'\n          }}>\n            {getDaysUntilEvent()}\n          </span>\n        </div>\n      </div>\n\n      {/* Event images (placeholder for future implementation) */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem',\n          maxHeight: '300px'\n        }}>\n          {images.slice(0, 2).map((image: any, index: number) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '12px',\n                overflow: 'hidden',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Event description */}\n      {event.description && (\n        <div className=\"tv-event-description\">\n          {truncateDescription(event.description)}\n        </div>\n      )}\n\n      {/* Event metadata */}\n      <div className=\"tv-event-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {event.category_name && (\n            <span \n              style={{\n                background: getCategoryColor(),\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '25px',\n                fontWeight: '600',\n                fontSize: '1.6rem'\n              }}\n            >\n              {event.category_name}\n            </span>\n          )}\n\n          {/* Subcategory */}\n          {event.subcategory_name && (\n            <span style={{\n              background: event.subcategory_color || '#95a5a6',\n              color: 'white',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              fontSize: '1.4rem'\n            }}>\n              {event.subcategory_name}\n            </span>\n          )}\n\n          {/* Recurring indicator */}\n          {event.is_recurring && (\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              gap: '0.5rem',\n              fontSize: '1.6rem',\n              color: '#8e44ad'\n            }}>\n              <span>🔄</span>\n              <span>\n                {event.recurrence_pattern === 'yearly' && 'Yearly'}\n                {event.recurrence_pattern === 'monthly' && 'Monthly'}\n                {event.recurrence_pattern === 'weekly' && 'Weekly'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Created by information */}\n        {event.created_by_name && (\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Organized by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {event.created_by_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(event.reaction_count || event.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {event.reaction_count && event.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{event.reaction_count} reactions</span>\n            </div>\n          )}\n          {event.comment_count && event.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{event.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVCalendarEvent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EACrE;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAE5C,IAAIZ,KAAK,CAACa,QAAQ,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAIV,IAAI,CAACJ,KAAK,CAACa,QAAQ,CAAC;MACxC,MAAME,cAAc,GAAGJ,SAAS,CAACN,kBAAkB,CAAC,OAAO,EAAE;QAC3DG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,MAAMO,YAAY,GAAGF,OAAO,CAACT,kBAAkB,CAAC,OAAO,EAAE;QACvDG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdF,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAO,GAAGQ,cAAc,MAAMC,YAAY,EAAE;IAC9C;IAEA,OAAOf,UAAU,CAACD,KAAK,CAACY,UAAU,CAAC;EACrC,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAC5C,MAAMQ,QAAQ,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,UAAU;IACrC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAMA,QAAQ,OAAO;IAC9C,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW;IACvC,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,WAAW;EACzC,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI1B,KAAK,CAAC2B,cAAc,EAAE;MACxB,OAAO3B,KAAK,CAAC2B,cAAc;IAC7B;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5B,KAAK,CAAC6B,UAAU,EAAE,OAAO,IAAI;IACjC,IAAI7B,KAAK,CAAC8B,YAAY,EAAE,OAAO,IAAI;IACnC,IAAI9B,KAAK,CAAC+B,QAAQ,EAAE,OAAO,IAAI;IAC/B,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMd,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAC5C,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3F,OAAOC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMW,oBAAoB,GAAIC,WAAmB,IAAK;IACpD,OAAOA,WAAW,GAAGA,WAAW,CAACC,MAAM,GAAG,CAAC;EAC7C,CAAC;EAED,MAAMC,iBAAiB,GAAGpC,KAAK,CAACkC,WAAW,IAAID,oBAAoB,CAACjC,KAAK,CAACkC,WAAW,CAAC,GAAG,GAAG;;EAE5F;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAsC,GAAG,EAAE;;IAEjD;IACA,IAAKtC,KAAK,CAASsC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAAExC,KAAK,CAASsC,MAAM,CAAC,EAAE;MAChEtC,KAAK,CAASsC,MAAM,CAACG,OAAO,CAAC,CAACC,GAAQ,EAAEC,KAAa,KAAK;QACzD,IAAID,GAAG,CAACE,SAAS,EAAE;UACjB,MAAMC,QAAQ,GAAGjD,WAAW,CAAC8C,GAAG,CAACE,SAAS,CAAC;UAC3C,IAAIC,QAAQ,EAAE;YACZP,MAAM,CAACQ,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGhD,KAAK,CAACiD,KAAK,YAAYN,KAAK,GAAG,CAAC;YAC1C,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOL,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,cAAc,CAAC,CAAC;EAE/B,oBACEvC,OAAA;IAAKoD,KAAK,EAAE;MACVC,UAAU,EAAEnB,UAAU,CAAC,CAAC,GACpB,mDAAmD,GACnD,mDAAmD;MACvDoB,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAEjB,iBAAiB,GAAG,QAAQ,GAAG,MAAM;MAC9CkB,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAEvB,UAAU,CAAC,CAAC,GACnB,oCAAoC,GACpC,gCAAgC;MACpCwB,MAAM,EAAExB,UAAU,CAAC,CAAC,GAChB,mBAAmB,GACnB,aAAaN,gBAAgB,CAAC,CAAC,IAAI;MACvC+B,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEAhE,OAAA;MAAKoD,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBM,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACff,UAAU,EAAE,0BAA0BzB,gBAAgB,CAAC,CAAC,OAAOA,gBAAgB,CAAC,CAAC,KAAK;QACtF0B,YAAY,EAAE,KAAK;QACnBe,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGJvC,UAAU,CAAC,CAAC,iBACXlC,OAAA;MAAKoD,KAAK,EAAE;QACVO,QAAQ,EAAE,UAAU;QACpBM,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbb,UAAU,EAAE,2CAA2C;QACvDqB,KAAK,EAAE,OAAO;QACdnB,OAAO,EAAE,WAAW;QACpBD,YAAY,EAAE,MAAM;QACpBqB,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,MAAM;QAClBC,aAAa,EAAE,WAAW;QAC1BC,aAAa,EAAE,KAAK;QACpBrB,SAAS,EAAE,mCAAmC;QAC9CY,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,EAAC;IAEH;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDzE,OAAA;MAAKoD,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfiB,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE,MAAM;QACpBtB,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,gBACAhE,OAAA;QAAKoD,KAAK,EAAE;UACVuB,QAAQ,EAAE,MAAM;UAChBtB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAE,MAAM;UACfD,YAAY,EAAE,MAAM;UACpBG,SAAS,EAAE;QACb,CAAE;QAAAO,QAAA,EACClC,YAAY,CAAC;MAAC;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAENzE,OAAA;QAAKoD,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEkB,GAAG,EAAE,MAAM;UAAEE,QAAQ,EAAE;QAAO,CAAE;QAAAlB,QAAA,GAC5D9D,KAAK,CAAC6B,UAAU,iBACf/B,OAAA;UAAMoD,KAAK,EAAE;YACXC,UAAU,EAAE,2CAA2C;YACvDqB,KAAK,EAAE,OAAO;YACdnB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBqB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBrB,SAAS,EAAE;UACb,CAAE;UAAAO,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACAvE,KAAK,CAAC+B,QAAQ,iBACbjC,OAAA;UAAMoD,KAAK,EAAE;YACXC,UAAU,EAAE,2CAA2C;YACvDqB,KAAK,EAAE,OAAO;YACdnB,OAAO,EAAE,WAAW;YACpBD,YAAY,EAAE,MAAM;YACpBqB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBrB,SAAS,EAAE;UACb,CAAE;UAAAO,QAAA,EAAC;QAEH;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzE,OAAA;MAAIoD,KAAK,EAAE;QACTuB,QAAQ,EAAErC,iBAAiB,GAAG,QAAQ,GAAG,QAAQ;QACjDsC,UAAU,EAAE,KAAK;QACjBpB,MAAM,EAAE,YAAY;QACpBkB,KAAK,EAAE,SAAS;QAChBS,UAAU,EAAE,KAAK;QACjBxB,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE,CAAC;QACTe,UAAU,EAAE,8BAA8B;QAC1CC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE,YAAY;QACtBC,OAAO,EAAE;MACX,CAAE;MAAAvB,QAAA,EACC9D,KAAK,CAACiD;IAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGLzE,OAAA;MAAKoD,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfiB,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXxB,MAAM,EAAE,QAAQ;QAChBD,OAAO,EAAE,MAAM;QACfF,UAAU,EAAE,0BAA0B;QACtCC,YAAY,EAAE,MAAM;QACpBI,MAAM,EAAE,kCAAkC;QAC1CC,QAAQ,EAAE,UAAU;QACpBU,MAAM,EAAE,CAAC;QACTZ,SAAS,EAAE;MACb,CAAE;MAAAO,QAAA,gBACAhE,OAAA;QAAKoD,KAAK,EAAE;UACVuB,QAAQ,EAAE,MAAM;UAChBtB,UAAU,EAAE,2BAA2BzB,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;UACrF8C,KAAK,EAAE,OAAO;UACdnB,OAAO,EAAE,QAAQ;UACjBD,YAAY,EAAE,MAAM;UACpBG,SAAS,EAAE,cAAc7B,gBAAgB,CAAC,CAAC;QAC7C,CAAE;QAAAoC,QAAA,EAAC;MAEH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNzE,OAAA;QAAKoD,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEiB,GAAG,EAAE,MAAM;UAAEQ,IAAI,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBAC7EhE,OAAA;UAAMoD,KAAK,EAAE;YACXuB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBF,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,EACCpD,eAAe,CAAC;QAAC;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACPzE,OAAA;UAAMoD,KAAK,EAAE;YACXuB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBF,KAAK,EAAExC,UAAU,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YAC3CmB,UAAU,EAAEnB,UAAU,CAAC,CAAC,GAAG,wBAAwB,GAAG,0BAA0B;YAChFqB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBQ,OAAO,EAAE;UACX,CAAE;UAAAE,QAAA,EACC7C,iBAAiB,CAAC;QAAC;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjC,MAAM,CAACH,MAAM,GAAG,CAAC,iBAChBrC,OAAA;MAAKoD,KAAK,EAAE;QACV6B,YAAY,EAAE,MAAM;QACpBnB,OAAO,EAAE,MAAM;QACf2B,mBAAmB,EAAEjD,MAAM,CAACH,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzF2C,GAAG,EAAE,MAAM;QACXnB,SAAS,EAAE;MACb,CAAE;MAAAG,QAAA,EACCxB,MAAM,CAACkD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAU,EAAE/C,KAAa,kBAChD7C,OAAA;QAEEoD,KAAK,EAAE;UACLE,YAAY,EAAE,MAAM;UACpBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE;QACb,CAAE;QAAAO,QAAA,eAEFhE,OAAA;UACE6F,GAAG,EAAED,KAAK,CAAC3C,GAAI;UACfC,GAAG,EAAE0C,KAAK,CAAC1C,GAAI;UACfE,KAAK,EAAE;YACLe,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACf0B,SAAS,EAAE,OAAO;YAClBhC,OAAO,EAAE;UACX,CAAE;UACFiC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACU,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnBG5B,KAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAvE,KAAK,CAACkC,WAAW,iBAChBpC,OAAA;MAAKkG,SAAS,EAAC,sBAAsB;MAAAlC,QAAA,EAClCmC,mBAAmB,CAACjG,KAAK,CAACkC,WAAW;IAAC;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN,eAGDzE,OAAA;MAAKkG,SAAS,EAAC,eAAe;MAAAlC,QAAA,gBAC5BhE,OAAA;QAAKoD,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAhB,QAAA,GAEhE9D,KAAK,CAACkG,aAAa,iBAClBpG,OAAA;UACEoD,KAAK,EAAE;YACLC,UAAU,EAAEzB,gBAAgB,CAAC,CAAC;YAC9B8C,KAAK,EAAE,OAAO;YACdnB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBsB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EAED9D,KAAK,CAACkG;QAAa;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EAGAvE,KAAK,CAACmG,gBAAgB,iBACrBrG,OAAA;UAAMoD,KAAK,EAAE;YACXC,UAAU,EAAEnD,KAAK,CAACoG,iBAAiB,IAAI,SAAS;YAChD5B,KAAK,EAAE,OAAO;YACdnB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBqB,QAAQ,EAAE;UACZ,CAAE;UAAAX,QAAA,EACC9D,KAAK,CAACmG;QAAgB;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP,EAGAvE,KAAK,CAAC8B,YAAY,iBACjBhC,OAAA;UAAKoD,KAAK,EAAE;YACVU,OAAO,EAAE,MAAM;YACfiB,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbL,QAAQ,EAAE,QAAQ;YAClBD,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,gBACAhE,OAAA;YAAAgE,QAAA,EAAM;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfzE,OAAA;YAAAgE,QAAA,GACG9D,KAAK,CAACqG,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EACjDrG,KAAK,CAACqG,kBAAkB,KAAK,SAAS,IAAI,SAAS,EACnDrG,KAAK,CAACqG,kBAAkB,KAAK,QAAQ,IAAI,QAAQ;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLvE,KAAK,CAACsG,eAAe,iBACpBxG,OAAA;QAAKoD,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfiB,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXL,QAAQ,EAAE;QACZ,CAAE;QAAAX,QAAA,gBACAhE,OAAA;UAAMoD,KAAK,EAAE;YAAEqD,OAAO,EAAE;UAAI,CAAE;UAAAzC,QAAA,EAAC;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDzE,OAAA;UAAMoD,KAAK,EAAE;YAAEwB,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAChC9D,KAAK,CAACsG;QAAe;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACvE,KAAK,CAACwG,cAAc,IAAIxG,KAAK,CAACyG,aAAa,kBAC3C3G,OAAA;MAAKoD,KAAK,EAAE;QACVwD,SAAS,EAAE,MAAM;QACjBrD,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBQ,OAAO,EAAE,MAAM;QACfkB,GAAG,EAAE,MAAM;QACXL,QAAQ,EAAE;MACZ,CAAE;MAAAX,QAAA,GACC9D,KAAK,CAACwG,cAAc,IAAIxG,KAAK,CAACwG,cAAc,GAAG,CAAC,iBAC/C1G,OAAA;QAAKoD,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAhB,QAAA,gBACnEhE,OAAA;UAAAgE,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfzE,OAAA;UAAAgE,QAAA,GAAO9D,KAAK,CAACwG,cAAc,EAAC,YAAU;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACN,EACAvE,KAAK,CAACyG,aAAa,IAAIzG,KAAK,CAACyG,aAAa,GAAG,CAAC,iBAC7C3G,OAAA;QAAKoD,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAhB,QAAA,gBACnEhE,OAAA;UAAAgE,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfzE,OAAA;UAAAgE,QAAA,GAAO9D,KAAK,CAACyG,aAAa,EAAC,WAAS;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACoC,EAAA,GAvZI5G,eAA+C;AAyZrD,eAAeA,eAAe;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}