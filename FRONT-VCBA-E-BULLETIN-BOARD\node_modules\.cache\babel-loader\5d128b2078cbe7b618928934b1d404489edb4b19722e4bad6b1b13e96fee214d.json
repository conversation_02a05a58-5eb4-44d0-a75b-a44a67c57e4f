{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\tv\\\\TVDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TVDisplay = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Listen for real-time settings updates via localStorage\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'tv_display_settings') {\n        // Settings changed, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings changed:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_display_settings_updated') {\n        // Settings update signal, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings updated:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_emergency_broadcast') {\n        // Emergency broadcast signal\n        const newSettings = tvControlService.getSettings();\n        console.log('Emergency broadcast detected:', newSettings);\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      }\n    };\n\n    // Listen for storage changes from other tabs/windows\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also check for settings changes periodically (for same-tab updates)\n    const settingsCheckInterval = setInterval(() => {\n      const currentSettings = tvControlService.getSettings();\n      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {\n        console.log('Settings changed:', currentSettings);\n        setSettings(currentSettings);\n        // Force re-render for emergency messages\n        setRefreshKey(prev => prev + 1);\n      }\n    }, 500); // Check every 500ms for faster emergency response\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(settingsCheckInterval);\n    };\n  }, [settings]);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach(command => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = command => {\n    var _slideshowRef$current, _slideshowRef$current2;\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if ((_slideshowRef$current = slideshowRef.current) !== null && _slideshowRef$current !== void 0 && _slideshowRef$current.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if ((_slideshowRef$current2 = slideshowRef.current) !== null && _slideshowRef$current2 !== void 0 && _slideshowRef$current2.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 || event.category_id && settings.eventCategories.includes(event.category_id);\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime()).slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides = [];\n\n    // Add announcements\n    if (settings.showAnnouncements && announcements && announcements.length > 0) {\n      const filteredAnnouncements = announcements.filter(announcement => {\n        const matchesCategory = settings.announcementCategories.length === 0 || settings.announcementCategories.includes(announcement.category_id);\n        return matchesCategory;\n      }).slice(0, settings.maxAnnouncements);\n      filteredAnnouncements.forEach(announcement => slides.push(/*#__PURE__*/_jsxDEV(TVAnnouncement, {\n        announcement: announcement\n      }, `announcement-${announcement.announcement_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach(event => slides.push(/*#__PURE__*/_jsxDEV(TVCalendarEvent, {\n        event: event\n      }, `event-${event.calendar_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this)));\n    }\n    return slides;\n  };\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      margin: 0,\n      padding: 0,\n      fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n      background: 'linear-gradient(135deg, #22c55e 0%, #fbbf24 100%)',\n      // Green to Yellow gradient\n      minHeight: '100vh',\n      overflowX: 'hidden',\n      color: '#ffffff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'rgba(255, 255, 255, 0.95)',\n        padding: '1rem 2rem',\n        textAlign: 'center',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        color: '#2c3e50',\n        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'\n      },\n      children: formatDateTime()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"tv-content\",\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading latest announcements and events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this), hasError && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '2rem'\n          },\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Unable to load content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            marginTop: '1rem',\n            opacity: 0.8\n          },\n          children: \"Please check your internet connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this), settings.emergencyActive && settings.emergencyMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(220, 53, 69, 0.95)',\n          color: 'white',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999,\n          animation: 'emergency-flash 2s infinite'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '8rem',\n            marginBottom: '2rem',\n            animation: 'emergency-pulse 1s infinite'\n          },\n          children: \"\\uD83D\\uDEA8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            fontWeight: 'bold',\n            textAlign: 'center',\n            marginBottom: '2rem',\n            textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'\n          },\n          children: \"EMERGENCY ALERT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            textAlign: 'center',\n            lineHeight: '1.4',\n            maxWidth: '80%',\n            background: 'rgba(0, 0, 0, 0.3)',\n            padding: '2rem',\n            borderRadius: '20px'\n          },\n          children: settings.emergencyMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this), !isLoading && !hasError && !settings.emergencyActive && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: slides.length > 0 ? /*#__PURE__*/_jsxDEV(TVSlideshow, {\n          ref: slideshowRef,\n          autoPlayInterval: settings.slideInterval,\n          showProgress: true,\n          isPlaying: isPlaying && settings.autoPlay,\n          onSlideChange: setCurrentSlide,\n          children: slides\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-no-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '6rem',\n              marginBottom: '3rem'\n            },\n            children: \"\\uD83D\\uDCE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No announcements or events to display\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              marginTop: '2rem',\n              opacity: 0.7\n            },\n            children: \"Check back later for updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 15\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      httpEquiv: \"refresh\",\n      content: \"600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(TVDisplay, \"KDYFlrHSu63QwnfhujBG3li3Uf4=\", false, function () {\n  return [useAnnouncements, useCalendar];\n});\n_c = TVDisplay;\nexport default TVDisplay;\nvar _c;\n$RefreshReg$(_c, \"TVDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAnnouncements", "useCalendar", "tvControlService", "TVAnnouncement", "TVCalendarEvent", "TVSlideshow", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TVDisplay", "_s", "currentTime", "setCurrentTime", "Date", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "settings", "setSettings", "getSettings", "isPlaying", "setIsPlaying", "currentSlide", "setCurrentSlide", "slideshowRef", "currentDate", "announcements", "loading", "announcementsLoading", "error", "announcementsError", "refresh", "refreshAnnouncements", "status", "page", "limit", "maxAnnouncements", "sort_by", "sort_order", "events", "eventsLoading", "eventsError", "refreshEvents", "unsubscribe", "onSettingsChange", "handleStorageChange", "e", "key", "newSettings", "console", "log", "prev", "window", "addEventListener", "settingsCheckInterval", "setInterval", "currentSettings", "JSON", "stringify", "removeEventListener", "clearInterval", "sendHeartbeat", "localStorage", "setItem", "now", "toString", "updateStatus", "isOnline", "totalSlides", "createSlideContent", "length", "lastRefresh", "toISOString", "heartbeatInterval", "checkCommands", "commands", "getStoredCommands", "for<PERSON>ach", "command", "handleControlCommand", "clearProcessedCommands", "commandInterval", "_slideshowRef$current", "_slideshowRef$current2", "action", "current", "nextSlide", "prevSlide", "timeInterval", "refreshInterval", "reloadInterval", "location", "reload", "formatDateTime", "options", "weekday", "year", "month", "day", "hour", "minute", "toLocaleDateString", "getUpcomingEvents", "showCalendarEvents", "today", "thirtyDaysFromNow", "setDate", "getDate", "filter", "event", "eventDate", "event_date", "matchesCategory", "eventCategories", "category_id", "includes", "is_active", "sort", "a", "b", "getTime", "slice", "maxEvents", "slides", "showAnnouncements", "filteredAnnouncements", "announcement", "announcementCategories", "push", "announcement_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "upcomingEvents", "calendar_id", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "style", "margin", "padding", "fontFamily", "background", "minHeight", "overflowX", "color", "children", "textAlign", "fontSize", "fontWeight", "borderBottom", "boxShadow", "className", "marginBottom", "marginTop", "opacity", "emergencyActive", "emergencyMessage", "position", "top", "left", "right", "bottom", "display", "flexDirection", "alignItems", "justifyContent", "zIndex", "animation", "textShadow", "lineHeight", "max<PERSON><PERSON><PERSON>", "borderRadius", "ref", "autoPlayInterval", "slideInterval", "showProgress", "autoPlay", "onSlideChange", "httpEquiv", "content", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/tv/TVDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService, TVDisplaySettings, TVControlCommand } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\n\nconst TVDisplay: React.FC = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState<TVDisplaySettings>(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef<any>(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Listen for real-time settings updates via localStorage\n  useEffect(() => {\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'tv_display_settings') {\n        // Settings changed, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings changed:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_display_settings_updated') {\n        // Settings update signal, reload them\n        const newSettings = tvControlService.getSettings();\n        console.log('Storage event - settings updated:', newSettings);\n        setSettings(newSettings);\n      } else if (e.key === 'tv_emergency_broadcast') {\n        // Emergency broadcast signal\n        const newSettings = tvControlService.getSettings();\n        console.log('Emergency broadcast detected:', newSettings);\n        setSettings(newSettings);\n        setRefreshKey(prev => prev + 1);\n      }\n    };\n\n    // Listen for storage changes from other tabs/windows\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also check for settings changes periodically (for same-tab updates)\n    const settingsCheckInterval = setInterval(() => {\n      const currentSettings = tvControlService.getSettings();\n      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {\n        console.log('Settings changed:', currentSettings);\n        setSettings(currentSettings);\n        // Force re-render for emergency messages\n        setRefreshKey(prev => prev + 1);\n      }\n    }, 500); // Check every 500ms for faster emergency response\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(settingsCheckInterval);\n    };\n  }, [settings]);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach((command: TVControlCommand) => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = (command: TVControlCommand) => {\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if (slideshowRef.current?.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if (slideshowRef.current?.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options: Intl.DateTimeFormatOptions = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 ||\n        (event.category_id && settings.eventCategories.includes(event.category_id));\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())\n      .slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides: React.ReactNode[] = [];\n\n    // Add announcements\n    if (settings.showAnnouncements && announcements && announcements.length > 0) {\n      const filteredAnnouncements = announcements.filter(announcement => {\n        const matchesCategory = settings.announcementCategories.length === 0 ||\n          settings.announcementCategories.includes(announcement.category_id);\n        return matchesCategory;\n      }).slice(0, settings.maxAnnouncements);\n\n      filteredAnnouncements.forEach((announcement) => (\n        slides.push(\n          <TVAnnouncement\n            key={`announcement-${announcement.announcement_id}-${refreshKey}`}\n            announcement={announcement}\n          />\n        )\n      ));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event) => (\n        slides.push(\n          <TVCalendarEvent\n            key={`event-${event.calendar_id}-${refreshKey}`}\n            event={event}\n          />\n        )\n      ));\n    }\n\n    return slides;\n  };\n\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n\n  return (\n    <div style={{\n      margin: 0,\n      padding: 0,\n      fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n      background: 'linear-gradient(135deg, #22c55e 0%, #fbbf24 100%)', // Green to Yellow gradient\n      minHeight: '100vh',\n      overflowX: 'hidden',\n      color: '#ffffff'\n    }}>\n      {/* Current date and time */}\n      <div style={{\n        background: 'rgba(255, 255, 255, 0.95)',\n        padding: '1rem 2rem',\n        textAlign: 'center',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        color: '#2c3e50',\n        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'\n      }}>\n        {formatDateTime()}\n      </div>\n\n      {/* Main content area */}\n      <main className=\"tv-content\">\n        {/* Loading state */}\n        {isLoading && (\n          <div className=\"tv-loading\">\n            <div className=\"tv-loading-spinner\"></div>\n            <div>Loading latest announcements and events...</div>\n          </div>\n        )}\n\n        {/* Error state */}\n        {hasError && !isLoading && (\n          <div className=\"tv-error\">\n            <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>⚠️</div>\n            <div>Unable to load content</div>\n            <div style={{ fontSize: '2rem', marginTop: '1rem', opacity: 0.8 }}>\n              Please check your internet connection\n            </div>\n          </div>\n        )}\n\n        {/* Emergency Message Override */}\n        {settings.emergencyActive && settings.emergencyMessage && (\n          <div style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(220, 53, 69, 0.95)',\n            color: 'white',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 9999,\n            animation: 'emergency-flash 2s infinite'\n          }}>\n            <div style={{\n              fontSize: '8rem',\n              marginBottom: '2rem',\n              animation: 'emergency-pulse 1s infinite'\n            }}>\n              🚨\n            </div>\n            <div style={{\n              fontSize: '4rem',\n              fontWeight: 'bold',\n              textAlign: 'center',\n              marginBottom: '2rem',\n              textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'\n            }}>\n              EMERGENCY ALERT\n            </div>\n            <div style={{\n              fontSize: '3rem',\n              textAlign: 'center',\n              lineHeight: '1.4',\n              maxWidth: '80%',\n              background: 'rgba(0, 0, 0, 0.3)',\n              padding: '2rem',\n              borderRadius: '20px'\n            }}>\n              {settings.emergencyMessage}\n            </div>\n          </div>\n        )}\n\n        {/* Content slideshow */}\n        {!isLoading && !hasError && !settings.emergencyActive && (\n          <>\n            {slides.length > 0 ? (\n              <TVSlideshow\n                ref={slideshowRef}\n                autoPlayInterval={settings.slideInterval}\n                showProgress={true}\n                isPlaying={isPlaying && settings.autoPlay}\n                onSlideChange={setCurrentSlide}\n              >\n                {slides}\n              </TVSlideshow>\n            ) : (\n              <div className=\"tv-no-content\">\n                <div style={{ fontSize: '6rem', marginBottom: '3rem' }}>📢</div>\n                <div>No announcements or events to display</div>\n                <div style={{ fontSize: '2rem', marginTop: '2rem', opacity: 0.7 }}>\n                  Check back later for updates\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </main>\n\n\n\n      {/* Meta refresh as backup */}\n      <meta httpEquiv=\"refresh\" content=\"600\" />\n    </div>\n  );\n};\n\nexport default TVDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAA6C,iCAAiC;AACvG,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAoBK,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,YAAY,GAAGzB,MAAM,CAAM,IAAI,CAAC;;EAEtC;EACA,MAAM0B,WAAW,GAAG,IAAIX,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAM;IACJY,aAAa;IACbC,OAAO,EAAEC,oBAAoB;IAC7BC,KAAK,EAAEC,kBAAkB;IACzBC,OAAO,EAAEC;EACX,CAAC,GAAGhC,gBAAgB,CAAC;IACnBiC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAElB,QAAQ,CAACmB,gBAAgB;IAChCC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA,MAAM;IACJC,MAAM;IACNZ,OAAO,EAAEa,aAAa;IACtBX,KAAK,EAAEY,WAAW;IAClBV,OAAO,EAAEW;EACX,CAAC,GAAGzC,WAAW,CAACwB,WAAW,CAAC;;EAE5B;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM6C,WAAW,GAAGzC,gBAAgB,CAAC0C,gBAAgB,CAAC1B,WAAW,CAAC;IAClE,OAAOyB,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAM+C,mBAAmB,GAAIC,CAAe,IAAK;MAC/C,IAAIA,CAAC,CAACC,GAAG,KAAK,qBAAqB,EAAE;QACnC;QACA,MAAMC,WAAW,GAAG9C,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD8B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,WAAW,CAAC;QAC7D9B,WAAW,CAAC8B,WAAW,CAAC;MAC1B,CAAC,MAAM,IAAIF,CAAC,CAACC,GAAG,KAAK,6BAA6B,EAAE;QAClD;QACA,MAAMC,WAAW,GAAG9C,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD8B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,WAAW,CAAC;QAC7D9B,WAAW,CAAC8B,WAAW,CAAC;MAC1B,CAAC,MAAM,IAAIF,CAAC,CAACC,GAAG,KAAK,wBAAwB,EAAE;QAC7C;QACA,MAAMC,WAAW,GAAG9C,gBAAgB,CAACiB,WAAW,CAAC,CAAC;QAClD8B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEF,WAAW,CAAC;QACzD9B,WAAW,CAAC8B,WAAW,CAAC;QACxBhC,aAAa,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC;IACF,CAAC;;IAED;IACAC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAER,mBAAmB,CAAC;;IAEvD;IACA,MAAMS,qBAAqB,GAAGC,WAAW,CAAC,MAAM;MAC9C,MAAMC,eAAe,GAAGtD,gBAAgB,CAACiB,WAAW,CAAC,CAAC;MACtD,IAAIsC,IAAI,CAACC,SAAS,CAACF,eAAe,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACzC,QAAQ,CAAC,EAAE;QAChEgC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEM,eAAe,CAAC;QACjDtC,WAAW,CAACsC,eAAe,CAAC;QAC5B;QACAxC,aAAa,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXC,MAAM,CAACO,mBAAmB,CAAC,SAAS,EAAEd,mBAAmB,CAAC;MAC1De,aAAa,CAACN,qBAAqB,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAACrC,QAAQ,CAAC,CAAC;;EAEd;EACAnB,SAAS,CAAC,MAAM;IACd,MAAM+D,aAAa,GAAGA,CAAA,KAAM;MAC1BC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEjD,IAAI,CAACkD,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MACnE/D,gBAAgB,CAACgE,YAAY,CAAC;QAC5BC,QAAQ,EAAE,IAAI;QACd/C,SAAS;QACTE,YAAY;QACZ8C,WAAW,EAAEC,kBAAkB,CAAC,CAAC,CAACC,MAAM;QACxCC,WAAW,EAAE,IAAIzD,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IAEDX,aAAa,CAAC,CAAC;IACf,MAAMY,iBAAiB,GAAGlB,WAAW,CAACM,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE5D,OAAO,MAAMD,aAAa,CAACa,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAACrD,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAE7B;EACAxB,SAAS,CAAC,MAAM;IACd,MAAM4E,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,QAAQ,GAAGzE,gBAAgB,CAAC0E,iBAAiB,CAAC,CAAC;MACrD,IAAID,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;QACvBK,QAAQ,CAACE,OAAO,CAAEC,OAAyB,IAAK;UAC9CC,oBAAoB,CAACD,OAAO,CAAC;QAC/B,CAAC,CAAC;QACF5E,gBAAgB,CAAC8E,sBAAsB,CAAC,CAAC;MAC3C;IACF,CAAC;IAED,MAAMC,eAAe,GAAG1B,WAAW,CAACmB,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,OAAO,MAAMd,aAAa,CAACqB,eAAe,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMF,oBAAoB,GAAID,OAAyB,IAAK;IAAA,IAAAI,qBAAA,EAAAC,sBAAA;IAC1D,QAAQL,OAAO,CAACM,MAAM;MACpB,KAAK,MAAM;QACT/D,YAAY,CAAC,IAAI,CAAC;QAClB;MACF,KAAK,OAAO;QACVA,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,KAAK,MAAM;QACT,KAAA6D,qBAAA,GAAI1D,YAAY,CAAC6D,OAAO,cAAAH,qBAAA,eAApBA,qBAAA,CAAsBI,SAAS,EAAE;UACnC9D,YAAY,CAAC6D,OAAO,CAACC,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,UAAU;QACb,KAAAH,sBAAA,GAAI3D,YAAY,CAAC6D,OAAO,cAAAF,sBAAA,eAApBA,sBAAA,CAAsBI,SAAS,EAAE;UACnC/D,YAAY,CAAC6D,OAAO,CAACE,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,SAAS;QACZvD,oBAAoB,CAAC,CAAC;QACtBU,aAAa,CAAC,CAAC;QACf1B,aAAa,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/B;MACF,KAAK,WAAW;QACd;QACA;IACJ;EACF,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACd,MAAM0F,YAAY,GAAGjC,WAAW,CAAC,MAAM;MACrC1C,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM8C,aAAa,CAAC4B,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1F,SAAS,CAAC,MAAM;IACd,MAAM2F,eAAe,GAAGlC,WAAW,CAAC,MAAM;MACxCvB,oBAAoB,CAAC,CAAC;MACtBU,aAAa,CAAC,CAAC;MACf1B,aAAa,CAACmC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMS,aAAa,CAAC6B,eAAe,CAAC;EAC7C,CAAC,EAAE,CAACzD,oBAAoB,EAAEU,aAAa,CAAC,CAAC;;EAEzC;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM4F,cAAc,GAAGnC,WAAW,CAAC,MAAM;MACvCH,MAAM,CAACuC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMhC,aAAa,CAAC8B,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAmC,GAAG;MAC1CC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD,OAAOxF,WAAW,CAACyF,kBAAkB,CAAC,OAAO,EAAEP,OAAO,CAAC;EACzD,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACrF,QAAQ,CAACsF,kBAAkB,EAAE,OAAO,EAAE;IAE3C,MAAMC,KAAK,GAAG,IAAI1F,IAAI,CAAC,CAAC;IACxB,MAAM2F,iBAAiB,GAAG,IAAI3F,IAAI,CAAC,CAAC;IACpC2F,iBAAiB,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE/C,OAAOpE,MAAM,CAACqE,MAAM,CAACC,KAAK,IAAI;MAC5B,MAAMC,SAAS,GAAG,IAAIhG,IAAI,CAAC+F,KAAK,CAACE,UAAU,CAAC;MAC5C,MAAMC,eAAe,GAAG/F,QAAQ,CAACgG,eAAe,CAAC3C,MAAM,KAAK,CAAC,IAC1DuC,KAAK,CAACK,WAAW,IAAIjG,QAAQ,CAACgG,eAAe,CAACE,QAAQ,CAACN,KAAK,CAACK,WAAW,CAAE;MAC7E,OAAOJ,SAAS,IAAIN,KAAK,IAAIM,SAAS,IAAIL,iBAAiB,IAAII,KAAK,CAACO,SAAS,IAAIJ,eAAe;IACnG,CAAC,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIzG,IAAI,CAACwG,CAAC,CAACP,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,GAAG,IAAI1G,IAAI,CAACyG,CAAC,CAACR,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CACnFC,KAAK,CAAC,CAAC,EAAExG,QAAQ,CAACyG,SAAS,CAAC;EACjC,CAAC;;EAED;EACA,MAAMrD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMsD,MAAyB,GAAG,EAAE;;IAEpC;IACA,IAAI1G,QAAQ,CAAC2G,iBAAiB,IAAIlG,aAAa,IAAIA,aAAa,CAAC4C,MAAM,GAAG,CAAC,EAAE;MAC3E,MAAMuD,qBAAqB,GAAGnG,aAAa,CAACkF,MAAM,CAACkB,YAAY,IAAI;QACjE,MAAMd,eAAe,GAAG/F,QAAQ,CAAC8G,sBAAsB,CAACzD,MAAM,KAAK,CAAC,IAClErD,QAAQ,CAAC8G,sBAAsB,CAACZ,QAAQ,CAACW,YAAY,CAACZ,WAAW,CAAC;QACpE,OAAOF,eAAe;MACxB,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,EAAExG,QAAQ,CAACmB,gBAAgB,CAAC;MAEtCyF,qBAAqB,CAAChD,OAAO,CAAEiD,YAAY,IACzCH,MAAM,CAACK,IAAI,cACTzH,OAAA,CAACJ,cAAc;QAEb2H,YAAY,EAAEA;MAAa,GADtB,gBAAgBA,YAAY,CAACG,eAAe,IAAIlH,UAAU,EAAE;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CACH,CACD,CAAC;IACJ;;IAEA;IACA,MAAMC,cAAc,GAAGhC,iBAAiB,CAAC,CAAC;IAC1C,IAAIgC,cAAc,CAAChE,MAAM,GAAG,CAAC,EAAE;MAC7BgE,cAAc,CAACzD,OAAO,CAAEgC,KAAK,IAC3Bc,MAAM,CAACK,IAAI,cACTzH,OAAA,CAACH,eAAe;QAEdyG,KAAK,EAAEA;MAAM,GADR,SAASA,KAAK,CAAC0B,WAAW,IAAIxH,UAAU,EAAE;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhD,CACH,CACD,CAAC;IACJ;IAEA,OAAOV,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGtD,kBAAkB,CAAC,CAAC;EACnC,MAAMmE,SAAS,GAAG5G,oBAAoB,IAAIY,aAAa;EACvD,MAAMiG,QAAQ,GAAG3G,kBAAkB,IAAIW,WAAW;EAElD,oBACElC,OAAA;IAAKmI,KAAK,EAAE;MACVC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE,iDAAiD;MAC7DC,UAAU,EAAE,mDAAmD;MAAE;MACjEC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEA3I,OAAA;MAAKmI,KAAK,EAAE;QACVI,UAAU,EAAE,2BAA2B;QACvCF,OAAO,EAAE,WAAW;QACpBO,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,KAAK;QACjBJ,KAAK,EAAE,SAAS;QAChBK,YAAY,EAAE,8BAA8B;QAC5CC,SAAS,EAAE;MACb,CAAE;MAAAL,QAAA,EACCrD,cAAc,CAAC;IAAC;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGN9H,OAAA;MAAMiJ,SAAS,EAAC,YAAY;MAAAN,QAAA,GAEzBV,SAAS,iBACRjI,OAAA;QAAKiJ,SAAS,EAAC,YAAY;QAAAN,QAAA,gBACzB3I,OAAA;UAAKiJ,SAAS,EAAC;QAAoB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C9H,OAAA;UAAA2I,QAAA,EAAK;QAA0C;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,EAGAI,QAAQ,IAAI,CAACD,SAAS,iBACrBjI,OAAA;QAAKiJ,SAAS,EAAC,UAAU;QAAAN,QAAA,gBACvB3I,OAAA;UAAKmI,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEK,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChE9H,OAAA;UAAA2I,QAAA,EAAK;QAAsB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjC9H,OAAA;UAAKmI,KAAK,EAAE;YAAEU,QAAQ,EAAE,MAAM;YAAEM,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAT,QAAA,EAAC;QAEnE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGApH,QAAQ,CAAC2I,eAAe,IAAI3I,QAAQ,CAAC4I,gBAAgB,iBACpDtJ,OAAA;QAAKmI,KAAK,EAAE;UACVoB,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTpB,UAAU,EAAE,yBAAyB;UACrCG,KAAK,EAAE,OAAO;UACdkB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;QACb,CAAE;QAAAtB,QAAA,gBACA3I,OAAA;UAAKmI,KAAK,EAAE;YACVU,QAAQ,EAAE,MAAM;YAChBK,YAAY,EAAE,MAAM;YACpBe,SAAS,EAAE;UACb,CAAE;UAAAtB,QAAA,EAAC;QAEH;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN9H,OAAA;UAAKmI,KAAK,EAAE;YACVU,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBF,SAAS,EAAE,QAAQ;YACnBM,YAAY,EAAE,MAAM;YACpBgB,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EAAC;QAEH;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN9H,OAAA;UAAKmI,KAAK,EAAE;YACVU,QAAQ,EAAE,MAAM;YAChBD,SAAS,EAAE,QAAQ;YACnBuB,UAAU,EAAE,KAAK;YACjBC,QAAQ,EAAE,KAAK;YACf7B,UAAU,EAAE,oBAAoB;YAChCF,OAAO,EAAE,MAAM;YACfgC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,EACCjI,QAAQ,CAAC4I;QAAgB;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACG,SAAS,IAAI,CAACC,QAAQ,IAAI,CAACxH,QAAQ,CAAC2I,eAAe,iBACnDrJ,OAAA,CAAAE,SAAA;QAAAyI,QAAA,EACGvB,MAAM,CAACrD,MAAM,GAAG,CAAC,gBAChB/D,OAAA,CAACF,WAAW;UACVwK,GAAG,EAAErJ,YAAa;UAClBsJ,gBAAgB,EAAE7J,QAAQ,CAAC8J,aAAc;UACzCC,YAAY,EAAE,IAAK;UACnB5J,SAAS,EAAEA,SAAS,IAAIH,QAAQ,CAACgK,QAAS;UAC1CC,aAAa,EAAE3J,eAAgB;UAAA2H,QAAA,EAE9BvB;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAEd9H,OAAA;UAAKiJ,SAAS,EAAC,eAAe;UAAAN,QAAA,gBAC5B3I,OAAA;YAAKmI,KAAK,EAAE;cAAEU,QAAQ,EAAE,MAAM;cAAEK,YAAY,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChE9H,OAAA;YAAA2I,QAAA,EAAK;UAAqC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChD9H,OAAA;YAAKmI,KAAK,EAAE;cAAEU,QAAQ,EAAE,MAAM;cAAEM,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAAT,QAAA,EAAC;UAEnE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN,gBACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAKP9H,OAAA;MAAM4K,SAAS,EAAC,SAAS;MAACC,OAAO,EAAC;IAAK;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAAC1H,EAAA,CAnXID,SAAmB;EAAA,QAiBnBV,gBAAgB,EAchBC,WAAW;AAAA;AAAAoL,EAAA,GA/BX3K,SAAmB;AAqXzB,eAAeA,SAAS;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}