{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVAnnouncement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVAnnouncement = ({\n  announcement\n}) => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Smart content truncation for TV display (no scrolling available)\n  const truncateContent = (content, maxLength = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n  const isLongContent = announcement.content.length > 200;\n\n  // Get category color - keep original button colors\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Keep original blue for buttons\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n    return images;\n  };\n  const images = getAnnouncementImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex(prev => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f9fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #e74c3c' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      height: 'auto',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block',\n      // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [isUrgent && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n          color: 'white',\n          padding: '1rem 2rem',\n          borderRadius: '15px',\n          marginBottom: '1.5rem',\n          fontSize: '1.8rem',\n          fontWeight: 'bold',\n          textAlign: 'center',\n          textTransform: 'uppercase',\n          letterSpacing: '1px',\n          boxShadow: '0 6px 20px rgba(231, 76, 60, 0.4)'\n        },\n        children: announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: images.length > 0 ? '3.2rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#1f2937',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        },\n        children: announcement.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: images.length > 0 ? '2.2rem' : '2.8rem',\n          lineHeight: '1.6',\n          color: '#374151',\n          background: 'rgba(255, 255, 255, 0.8)',\n          padding: images.length > 0 ? '1.5rem' : '2rem',\n          borderRadius: '15px',\n          border: '2px solid rgba(39, 174, 96, 0.2)',\n          wordWrap: 'break-word',\n          flex: 1,\n          marginBottom: '1.5rem',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        },\n        children: truncateContent(announcement.content, images.length > 0 ? isLongContent ? 300 : 400 : 600)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        },\n        children: [announcement.category_name && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '20px',\n            fontWeight: '700',\n            fontSize: '1.4rem',\n            boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            alignSelf: 'flex-start'\n          },\n          children: announcement.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Date: \", formatDate(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Time: \", formatTime(announcement.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), announcement.author_name && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#27ae60',\n              fontWeight: '600'\n            },\n            children: [\"By: \", announcement.author_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '0 0 40%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        position: 'relative',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '100%',\n          height: '100%',\n          borderRadius: '0px',\n          // Removed border radius to show full image\n          overflow: 'hidden',\n          boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n          border: '4px solid #3498db',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: images[currentImageIndex].url,\n          alt: images[currentImageIndex].alt,\n          style: {\n            width: '100%',\n            height: '100%',\n            objectFit: 'cover',\n            display: 'block',\n            transition: 'opacity 0.5s ease-in-out'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            bottom: '1rem',\n            right: '1rem',\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '1.2rem',\n            fontWeight: '600'\n          },\n          children: [currentImageIndex + 1, \" / \", images.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 15\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '1rem',\n            right: '1rem',\n            background: 'rgba(39, 174, 96, 0.8)',\n            color: 'white',\n            padding: '0.5rem',\n            borderRadius: '50%',\n            fontSize: '0.8rem',\n            fontWeight: 'bold'\n          },\n          children: \"AUTO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this), (announcement.reaction_count || announcement.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [announcement.reaction_count && announcement.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            color: '#e74c3c'\n          },\n          children: \"LIKES:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: announcement.reaction_count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 13\n      }, this), announcement.comment_count && announcement.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            color: '#3498db'\n          },\n          children: \"COMMENTS:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: announcement.comment_count\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(TVAnnouncement, \"iwdYV/csWqs0gMEM0R8yiwCHnVs=\");\n_c = TVAnnouncement;\nexport default TVAnnouncement;\nvar _c;\n$RefreshReg$(_c, \"TVAnnouncement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getImageUrl", "jsxDEV", "_jsxDEV", "TVAnnouncement", "announcement", "_s", "currentImageIndex", "setCurrentImageIndex", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatTime", "toLocaleTimeString", "hour", "minute", "truncate<PERSON><PERSON><PERSON>", "content", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "is<PERSON>ong<PERSON><PERSON>nt", "getCategoryColor", "category_color", "is<PERSON><PERSON>", "is_alert", "is_pinned", "getAnnouncementImages", "images", "image_path", "imageUrl", "push", "url", "alt", "title", "image_url", "attachments", "for<PERSON>ach", "attachment", "index", "file_path", "match", "interval", "setInterval", "prev", "clearInterval", "style", "background", "borderRadius", "padding", "margin", "boxShadow", "border", "position", "overflow", "height", "maxHeight", "display", "gap", "alignItems", "children", "flex", "flexDirection", "justifyContent", "zIndex", "color", "marginBottom", "fontSize", "fontWeight", "textAlign", "textTransform", "letterSpacing", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lineHeight", "textShadow", "wordWrap", "category_name", "alignSelf", "flexWrap", "created_at", "author_name", "minHeight", "width", "src", "objectFit", "transition", "onError", "e", "currentTarget", "bottom", "right", "top", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVAnnouncement.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport type { Announcement } from '../../types/announcement.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVAnnouncementProps {\n  announcement: Announcement;\n}\n\nconst TVAnnouncement: React.FC<TVAnnouncementProps> = ({ announcement }) => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format the time for display\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Smart content truncation for TV display (no scrolling available)\n  const truncateContent = (content: string, maxLength: number = 300) => {\n    if (content.length <= maxLength) return content;\n    return content.substring(0, maxLength).trim() + '...';\n  };\n\n  const isLongContent = announcement.content.length > 200;\n\n  // Get category color - keep original button colors\n  const getCategoryColor = () => {\n    if (announcement.category_color) {\n      return announcement.category_color;\n    }\n    return '#3498db'; // Keep original blue for buttons\n  };\n\n  // Determine if announcement is urgent/alert\n  const isUrgent = announcement.is_alert || announcement.is_pinned;\n\n  // Get announcement images\n  const getAnnouncementImages = () => {\n    const images = [];\n\n    // Primary image from image_path\n    if (announcement.image_path) {\n      const imageUrl = getImageUrl(announcement.image_path);\n      if (imageUrl) {\n        images.push({\n          url: imageUrl,\n          alt: `${announcement.title} - Image`\n        });\n      }\n    }\n\n    // Primary image from image_url (fallback)\n    if (announcement.image_url && !announcement.image_path) {\n      images.push({\n        url: announcement.image_url,\n        alt: `${announcement.title} - Image`\n      });\n    }\n\n    // Additional images from attachments\n    if (announcement.attachments) {\n      announcement.attachments.forEach((attachment, index) => {\n        if (attachment.file_path && attachment.file_path.match(/\\.(jpg|jpeg|png|gif|webp)$/i)) {\n          const imageUrl = getImageUrl(attachment.file_path);\n          if (imageUrl) {\n            images.push({\n              url: imageUrl,\n              alt: `${announcement.title} - Image ${index + 2}`\n            });\n          }\n        }\n      });\n    }\n\n    return images;\n  };\n\n  const images = getAnnouncementImages();\n\n  // Auto-rotate images if multiple images exist\n  useEffect(() => {\n    if (images.length > 1) {\n      const interval = setInterval(() => {\n        setCurrentImageIndex((prev) => (prev + 1) % images.length);\n      }, 4000); // Change image every 4 seconds\n\n      return () => clearInterval(interval);\n    }\n  }, [images.length]);\n\n  return (\n    <div style={{\n      background: 'linear-gradient(135deg, #ffffff 0%, #f9fdf4 100%)',\n      borderRadius: '20px',\n      padding: '2rem',\n      margin: '1.5rem 0',\n      boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',\n      border: isUrgent ? '4px solid #e74c3c' : `3px solid ${getCategoryColor()}`,\n      position: 'relative',\n      overflow: 'hidden',\n      height: 'auto',\n      maxHeight: '85vh',\n      display: images.length > 0 ? 'flex' : 'block', // Conditional layout\n      gap: images.length > 0 ? '2rem' : '0',\n      alignItems: images.length > 0 ? 'stretch' : 'normal'\n    }}>\n      {/* Content Section - Full width if no images, 60% if images exist */}\n      <div style={{\n        flex: images.length > 0 ? '0 0 60%' : '1',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        position: 'relative',\n        zIndex: 1\n      }}>\n        {/* Alert indicator - keep original red color */}\n        {isUrgent && (\n          <div style={{\n            background: 'linear-gradient(135deg, #e74c3c, #c0392b)',\n            color: 'white',\n            padding: '1rem 2rem',\n            borderRadius: '15px',\n            marginBottom: '1.5rem',\n            fontSize: '1.8rem',\n            fontWeight: 'bold',\n            textAlign: 'center',\n            textTransform: 'uppercase',\n            letterSpacing: '1px',\n            boxShadow: '0 6px 20px rgba(231, 76, 60, 0.4)'\n          }}>\n            {announcement.is_alert ? 'IMPORTANT ALERT' : 'PINNED'}\n          </div>\n        )}\n\n        {/* Announcement title - Larger when no images */}\n        <h2 style={{\n          fontSize: images.length > 0 ? '3.2rem' : '4rem',\n          fontWeight: '700',\n          margin: '0 0 1.5rem 0',\n          color: '#1f2937',\n          lineHeight: '1.2',\n          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n          wordWrap: 'break-word',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        }}>\n          {announcement.title}\n        </h2>\n\n        {/* Announcement content - Larger when no images */}\n        <div style={{\n          fontSize: images.length > 0 ? '2.2rem' : '2.8rem',\n          lineHeight: '1.6',\n          color: '#374151',\n          background: 'rgba(255, 255, 255, 0.8)',\n          padding: images.length > 0 ? '1.5rem' : '2rem',\n          borderRadius: '15px',\n          border: '2px solid rgba(39, 174, 96, 0.2)',\n          wordWrap: 'break-word',\n          flex: 1,\n          marginBottom: '1.5rem',\n          textAlign: images.length > 0 ? 'left' : 'center'\n        }}>\n          {truncateContent(announcement.content, images.length > 0 ? (isLongContent ? 300 : 400) : 600)}\n        </div>\n\n        {/* Metadata */}\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '1rem'\n        }}>\n          {/* Category */}\n          {announcement.category_name && (\n            <div style={{\n              background: `linear-gradient(135deg, ${getCategoryColor()}, ${getCategoryColor()}dd)`,\n              color: 'white',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '20px',\n              fontWeight: '700',\n              fontSize: '1.4rem',\n              boxShadow: `0 4px 10px ${getCategoryColor()}40`,\n              textTransform: 'uppercase',\n              letterSpacing: '1px',\n              alignSelf: 'flex-start'\n            }}>\n              {announcement.category_name}\n            </div>\n          )}\n\n          {/* Date, time, and author */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1.5rem',\n            flexWrap: 'wrap',\n            fontSize: '1.4rem',\n            color: '#6b7280'\n          }}>\n            <span>Date: {formatDate(announcement.created_at)}</span>\n            <span>Time: {formatTime(announcement.created_at)}</span>\n            {announcement.author_name && (\n              <span style={{ color: '#27ae60', fontWeight: '600' }}>\n                By: {announcement.author_name}\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Right Side - Images (40% width) */}\n      {images.length > 0 && (\n        <div style={{\n          flex: '0 0 40%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          position: 'relative',\n          minHeight: '400px'\n        }}>\n          {/* Single Image or Current Image from Carousel */}\n          <div style={{\n            width: '100%',\n            height: '100%',\n            borderRadius: '0px', // Removed border radius to show full image\n            overflow: 'hidden',\n            boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',\n            border: '4px solid #3498db',\n            position: 'relative'\n          }}>\n            <img\n              src={images[currentImageIndex].url}\n              alt={images[currentImageIndex].alt}\n              style={{\n                width: '100%',\n                height: '100%',\n                objectFit: 'cover',\n                display: 'block',\n                transition: 'opacity 0.5s ease-in-out'\n              }}\n              onError={(e) => {\n                e.currentTarget.style.display = 'none';\n              }}\n            />\n\n            {/* Image counter for multiple images */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                bottom: '1rem',\n                right: '1rem',\n                background: 'rgba(0, 0, 0, 0.7)',\n                color: 'white',\n                padding: '0.5rem 1rem',\n                borderRadius: '20px',\n                fontSize: '1.2rem',\n                fontWeight: '600'\n              }}>\n                {currentImageIndex + 1} / {images.length}\n              </div>\n            )}\n\n            {/* Auto-rotation indicator */}\n            {images.length > 1 && (\n              <div style={{\n                position: 'absolute',\n                top: '1rem',\n                right: '1rem',\n                background: 'rgba(39, 174, 96, 0.8)',\n                color: 'white',\n                padding: '0.5rem',\n                borderRadius: '50%',\n                fontSize: '0.8rem',\n                fontWeight: 'bold'\n              }}>\n                AUTO\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Engagement indicators (if available) */}\n      {(announcement.reaction_count || announcement.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {announcement.reaction_count && announcement.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span style={{ fontWeight: 'bold', color: '#e74c3c' }}>LIKES:</span>\n              <span>{announcement.reaction_count}</span>\n            </div>\n          )}\n          {announcement.comment_count && announcement.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span style={{ fontWeight: 'bold', color: '#3498db' }}>COMMENTS:</span>\n              <span>{announcement.comment_count}</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVAnnouncement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAgB,OAAO;AAE1D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EAC7D;EACA,MAAMU,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIR,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,OAAe,EAAEC,SAAiB,GAAG,GAAG,KAAK;IACpE,IAAID,OAAO,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,OAAO;IAC/C,OAAOA,OAAO,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EACvD,CAAC;EAED,MAAMC,aAAa,GAAGvB,YAAY,CAACkB,OAAO,CAACE,MAAM,GAAG,GAAG;;EAEvD;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIxB,YAAY,CAACyB,cAAc,EAAE;MAC/B,OAAOzB,YAAY,CAACyB,cAAc;IACpC;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG1B,YAAY,CAAC2B,QAAQ,IAAI3B,YAAY,CAAC4B,SAAS;;EAEhE;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,MAAM,GAAG,EAAE;;IAEjB;IACA,IAAI9B,YAAY,CAAC+B,UAAU,EAAE;MAC3B,MAAMC,QAAQ,GAAGpC,WAAW,CAACI,YAAY,CAAC+B,UAAU,CAAC;MACrD,IAAIC,QAAQ,EAAE;QACZF,MAAM,CAACG,IAAI,CAAC;UACVC,GAAG,EAAEF,QAAQ;UACbG,GAAG,EAAE,GAAGnC,YAAY,CAACoC,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIpC,YAAY,CAACqC,SAAS,IAAI,CAACrC,YAAY,CAAC+B,UAAU,EAAE;MACtDD,MAAM,CAACG,IAAI,CAAC;QACVC,GAAG,EAAElC,YAAY,CAACqC,SAAS;QAC3BF,GAAG,EAAE,GAAGnC,YAAY,CAACoC,KAAK;MAC5B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIpC,YAAY,CAACsC,WAAW,EAAE;MAC5BtC,YAAY,CAACsC,WAAW,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACtD,IAAID,UAAU,CAACE,SAAS,IAAIF,UAAU,CAACE,SAAS,CAACC,KAAK,CAAC,6BAA6B,CAAC,EAAE;UACrF,MAAMX,QAAQ,GAAGpC,WAAW,CAAC4C,UAAU,CAACE,SAAS,CAAC;UAClD,IAAIV,QAAQ,EAAE;YACZF,MAAM,CAACG,IAAI,CAAC;cACVC,GAAG,EAAEF,QAAQ;cACbG,GAAG,EAAE,GAAGnC,YAAY,CAACoC,KAAK,YAAYK,KAAK,GAAG,CAAC;YACjD,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;IAEA,OAAOX,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAGD,qBAAqB,CAAC,CAAC;;EAEtC;EACAlC,SAAS,CAAC,MAAM;IACd,IAAImC,MAAM,CAACV,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMwB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjC1C,oBAAoB,CAAE2C,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIhB,MAAM,CAACV,MAAM,CAAC;MAC5D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM2B,aAAa,CAACH,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACd,MAAM,CAACV,MAAM,CAAC,CAAC;EAEnB,oBACEtB,OAAA;IAAKkD,KAAK,EAAE;MACVC,UAAU,EAAE,mDAAmD;MAC/DC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAE5B,QAAQ,GAAG,mBAAmB,GAAG,aAAaF,gBAAgB,CAAC,CAAC,EAAE;MAC1E+B,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MACjBC,OAAO,EAAE7B,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;MAAE;MAC/CwC,GAAG,EAAE9B,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG;MACrCyC,UAAU,EAAE/B,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;IAC9C,CAAE;IAAA0C,QAAA,gBAEAhE,OAAA;MAAKkD,KAAK,EAAE;QACVe,IAAI,EAAEjC,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG;QACzCuC,OAAO,EAAE,MAAM;QACfK,aAAa,EAAE,QAAQ;QACvBC,cAAc,EAAE,eAAe;QAC/BV,QAAQ,EAAE,UAAU;QACpBW,MAAM,EAAE;MACV,CAAE;MAAAJ,QAAA,GAECpC,QAAQ,iBACP5B,OAAA;QAAKkD,KAAK,EAAE;UACVC,UAAU,EAAE,2CAA2C;UACvDkB,KAAK,EAAE,OAAO;UACdhB,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,MAAM;UACpBkB,YAAY,EAAE,QAAQ;UACtBC,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,QAAQ;UACnBC,aAAa,EAAE,WAAW;UAC1BC,aAAa,EAAE,KAAK;UACpBpB,SAAS,EAAE;QACb,CAAE;QAAAS,QAAA,EACC9D,YAAY,CAAC2B,QAAQ,GAAG,iBAAiB,GAAG;MAAQ;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,eAGD/E,OAAA;QAAIkD,KAAK,EAAE;UACTqB,QAAQ,EAAEvC,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAC/CkD,UAAU,EAAE,KAAK;UACjBlB,MAAM,EAAE,cAAc;UACtBe,KAAK,EAAE,SAAS;UAChBW,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE,8BAA8B;UAC1CC,QAAQ,EAAE,YAAY;UACtBT,SAAS,EAAEzC,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAC1C,CAAE;QAAA0C,QAAA,EACC9D,YAAY,CAACoC;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGL/E,OAAA;QAAKkD,KAAK,EAAE;UACVqB,QAAQ,EAAEvC,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ;UACjD0D,UAAU,EAAE,KAAK;UACjBX,KAAK,EAAE,SAAS;UAChBlB,UAAU,EAAE,0BAA0B;UACtCE,OAAO,EAAErB,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAC9C8B,YAAY,EAAE,MAAM;UACpBI,MAAM,EAAE,kCAAkC;UAC1C0B,QAAQ,EAAE,YAAY;UACtBjB,IAAI,EAAE,CAAC;UACPK,YAAY,EAAE,QAAQ;UACtBG,SAAS,EAAEzC,MAAM,CAACV,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;QAC1C,CAAE;QAAA0C,QAAA,EACC7C,eAAe,CAACjB,YAAY,CAACkB,OAAO,EAAEY,MAAM,CAACV,MAAM,GAAG,CAAC,GAAIG,aAAa,GAAG,GAAG,GAAG,GAAG,GAAI,GAAG;MAAC;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1F,CAAC,eAGN/E,OAAA;QAAKkD,KAAK,EAAE;UACVW,OAAO,EAAE,MAAM;UACfK,aAAa,EAAE,QAAQ;UACvBJ,GAAG,EAAE;QACP,CAAE;QAAAE,QAAA,GAEC9D,YAAY,CAACiF,aAAa,iBACzBnF,OAAA;UAAKkD,KAAK,EAAE;YACVC,UAAU,EAAE,2BAA2BzB,gBAAgB,CAAC,CAAC,KAAKA,gBAAgB,CAAC,CAAC,KAAK;YACrF2C,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,eAAe;YACxBD,YAAY,EAAE,MAAM;YACpBoB,UAAU,EAAE,KAAK;YACjBD,QAAQ,EAAE,QAAQ;YAClBhB,SAAS,EAAE,cAAc7B,gBAAgB,CAAC,CAAC,IAAI;YAC/CgD,aAAa,EAAE,WAAW;YAC1BC,aAAa,EAAE,KAAK;YACpBS,SAAS,EAAE;UACb,CAAE;UAAApB,QAAA,EACC9D,YAAY,CAACiF;QAAa;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN,eAGD/E,OAAA;UAAKkD,KAAK,EAAE;YACVW,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBD,GAAG,EAAE,QAAQ;YACbuB,QAAQ,EAAE,MAAM;YAChBd,QAAQ,EAAE,QAAQ;YAClBF,KAAK,EAAE;UACT,CAAE;UAAAL,QAAA,gBACAhE,OAAA;YAAAgE,QAAA,GAAM,QAAM,EAAC1D,UAAU,CAACJ,YAAY,CAACoF,UAAU,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxD/E,OAAA;YAAAgE,QAAA,GAAM,QAAM,EAACjD,UAAU,CAACb,YAAY,CAACoF,UAAU,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACvD7E,YAAY,CAACqF,WAAW,iBACvBvF,OAAA;YAAMkD,KAAK,EAAE;cAAEmB,KAAK,EAAE,SAAS;cAAEG,UAAU,EAAE;YAAM,CAAE;YAAAR,QAAA,GAAC,MAChD,EAAC9D,YAAY,CAACqF,WAAW;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/C,MAAM,CAACV,MAAM,GAAG,CAAC,iBAChBtB,OAAA;MAAKkD,KAAK,EAAE;QACVe,IAAI,EAAE,SAAS;QACfJ,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBI,cAAc,EAAE,QAAQ;QACxBV,QAAQ,EAAE,UAAU;QACpB+B,SAAS,EAAE;MACb,CAAE;MAAAxB,QAAA,eAEAhE,OAAA;QAAKkD,KAAK,EAAE;UACVuC,KAAK,EAAE,MAAM;UACb9B,MAAM,EAAE,MAAM;UACdP,YAAY,EAAE,KAAK;UAAE;UACrBM,QAAQ,EAAE,QAAQ;UAClBH,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE,mBAAmB;UAC3BC,QAAQ,EAAE;QACZ,CAAE;QAAAO,QAAA,gBACAhE,OAAA;UACE0F,GAAG,EAAE1D,MAAM,CAAC5B,iBAAiB,CAAC,CAACgC,GAAI;UACnCC,GAAG,EAAEL,MAAM,CAAC5B,iBAAiB,CAAC,CAACiC,GAAI;UACnCa,KAAK,EAAE;YACLuC,KAAK,EAAE,MAAM;YACb9B,MAAM,EAAE,MAAM;YACdgC,SAAS,EAAE,OAAO;YAClB9B,OAAO,EAAE,OAAO;YAChB+B,UAAU,EAAE;UACd,CAAE;UACFC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACW,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGD/C,MAAM,CAACV,MAAM,GAAG,CAAC,iBAChBtB,OAAA;UAAKkD,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpBuC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACb9C,UAAU,EAAE,oBAAoB;YAChCkB,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,aAAa;YACtBD,YAAY,EAAE,MAAM;YACpBmB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,GACC5D,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAAC4B,MAAM,CAACV,MAAM;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN,EAGA/C,MAAM,CAACV,MAAM,GAAG,CAAC,iBAChBtB,OAAA;UAAKkD,KAAK,EAAE;YACVO,QAAQ,EAAE,UAAU;YACpByC,GAAG,EAAE,MAAM;YACXD,KAAK,EAAE,MAAM;YACb9C,UAAU,EAAE,wBAAwB;YACpCkB,KAAK,EAAE,OAAO;YACdhB,OAAO,EAAE,QAAQ;YACjBD,YAAY,EAAE,KAAK;YACnBmB,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAAC7E,YAAY,CAACiG,cAAc,IAAIjG,YAAY,CAACkG,aAAa,kBACzDpG,OAAA;MAAKkD,KAAK,EAAE;QACVmD,SAAS,EAAE,MAAM;QACjBhD,OAAO,EAAE,QAAQ;QACjBF,UAAU,EAAE,qBAAqB;QACjCC,YAAY,EAAE,MAAM;QACpBS,OAAO,EAAE,MAAM;QACfC,GAAG,EAAE,MAAM;QACXS,QAAQ,EAAE;MACZ,CAAE;MAAAP,QAAA,GACC9D,YAAY,CAACiG,cAAc,IAAIjG,YAAY,CAACiG,cAAc,GAAG,CAAC,iBAC7DnG,OAAA;QAAKkD,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnEhE,OAAA;UAAMkD,KAAK,EAAE;YAAEsB,UAAU,EAAE,MAAM;YAAEH,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,EAAC;QAAM;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpE/E,OAAA;UAAAgE,QAAA,EAAO9D,YAAY,CAACiG;QAAc;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACN,EACA7E,YAAY,CAACkG,aAAa,IAAIlG,YAAY,CAACkG,aAAa,GAAG,CAAC,iBAC3DpG,OAAA;QAAKkD,KAAK,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAED,GAAG,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACnEhE,OAAA;UAAMkD,KAAK,EAAE;YAAEsB,UAAU,EAAE,MAAM;YAAEH,KAAK,EAAE;UAAU,CAAE;UAAAL,QAAA,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvE/E,OAAA;UAAAgE,QAAA,EAAO9D,YAAY,CAACkG;QAAa;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAzTIF,cAA6C;AAAAqG,EAAA,GAA7CrG,cAA6C;AA2TnD,eAAeA,cAAc;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}