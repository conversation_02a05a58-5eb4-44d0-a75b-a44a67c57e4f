import React, { useState, useEffect, useRef } from 'react';
import { useAnnouncements } from '../../hooks/useAnnouncements';
import { useCalendar } from '../../hooks/useCalendar';
import { tvControlService, TVDisplaySettings, TVControlCommand } from '../../services/tvControlService';
import TVAnnouncement from '../../components/tv/TVAnnouncement';
import TVCalendarEvent from '../../components/tv/TVCalendarEvent';
import TVSlideshow from '../../components/tv/TVSlideshow';
import '../../styles/tv.css';

const TVDisplay: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [refreshKey, setRefreshKey] = useState(0);
  const [settings, setSettings] = useState<TVDisplaySettings>(tvControlService.getSettings());
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const slideshowRef = useRef<any>(null);

  // Get current date for calendar hook
  const currentDate = new Date();

  // Fetch announcements (published only, recent first)
  const {
    announcements,
    loading: announcementsLoading,
    error: announcementsError,
    refresh: refreshAnnouncements
  } = useAnnouncements({
    status: 'published',
    page: 1,
    limit: settings.maxAnnouncements,
    sort_by: 'created_at',
    sort_order: 'DESC'
  }, false); // Use student service (no auth required)

  // Fetch calendar events
  const {
    events,
    loading: eventsLoading,
    error: eventsError,
    refresh: refreshEvents
  } = useCalendar(currentDate);

  // Subscribe to settings changes
  useEffect(() => {
    const unsubscribe = tvControlService.onSettingsChange(setSettings);
    return unsubscribe;
  }, []);

  // Listen for real-time settings updates via localStorage
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      console.log('Storage change detected:', e.key, e.newValue);

      if (e.key === 'tv_display_settings') {
        // Settings changed, reload them
        const newSettings = tvControlService.getSettings();
        console.log('Storage event - settings changed:', newSettings);
        setSettings(newSettings);
      } else if (e.key === 'tv_display_settings_updated') {
        // Settings update signal, reload them
        const newSettings = tvControlService.getSettings();
        console.log('Storage event - settings updated:', newSettings);
        setSettings(newSettings);
      } else if (e.key === 'tv_emergency_broadcast') {
        // Emergency broadcast signal
        const newSettings = tvControlService.getSettings();
        console.log('Emergency broadcast detected via storage:', newSettings);
        setSettings(newSettings);
        setRefreshKey(prev => prev + 1);
      } else if (e.key === 'tv_emergency_active') {
        // Emergency active state changed
        const newSettings = tvControlService.getSettings();
        console.log('Emergency active state changed:', e.newValue, newSettings);
        setSettings(newSettings);
        setRefreshKey(prev => prev + 1);
      }
    };

    const handleEmergencyBroadcast = (e: CustomEvent) => {
      console.log('Custom emergency broadcast event:', e.detail);
      const newSettings = tvControlService.getSettings();
      setSettings(newSettings);
      setRefreshKey(prev => prev + 1);
    };

    const handleEmergencyCleared = () => {
      console.log('Emergency cleared event');
      const newSettings = tvControlService.getSettings();
      setSettings(newSettings);
      setRefreshKey(prev => prev + 1);
    };

    // Listen for storage changes from other tabs/windows
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('emergency-broadcast', handleEmergencyBroadcast as EventListener);
    window.addEventListener('emergency-cleared', handleEmergencyCleared);

    // Also check for settings changes periodically (for same-tab updates)
    const settingsCheckInterval = setInterval(() => {
      const currentSettings = tvControlService.getSettings();
      if (JSON.stringify(currentSettings) !== JSON.stringify(settings)) {
        console.log('Periodic check - Settings changed:', currentSettings);
        setSettings(currentSettings);
        // Force re-render for emergency messages
        setRefreshKey(prev => prev + 1);
      }
    }, 250); // Check every 250ms for faster emergency response

    // Emergency-specific check
    const emergencyCheckInterval = setInterval(() => {
      const emergencyActive = localStorage.getItem('tv_emergency_active') === 'true';
      const emergencyMessage = localStorage.getItem('tv_emergency_message');

      if (emergencyActive && emergencyMessage && !settings.emergencyActive) {
        console.log('Emergency detected via periodic check:', emergencyMessage);
        const newSettings = tvControlService.getSettings();
        setSettings(newSettings);
        setRefreshKey(prev => prev + 1);
      }
    }, 100); // Check every 100ms for emergency

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('emergency-broadcast', handleEmergencyBroadcast as EventListener);
      window.removeEventListener('emergency-cleared', handleEmergencyCleared);
      clearInterval(settingsCheckInterval);
      clearInterval(emergencyCheckInterval);
    };
  }, [settings]);

  // Send heartbeat to indicate TV is online
  useEffect(() => {
    const sendHeartbeat = () => {
      localStorage.setItem('tv_display_heartbeat', Date.now().toString());
      tvControlService.updateStatus({
        isOnline: true,
        isPlaying,
        currentSlide,
        totalSlides: createSlideContent().length,
        lastRefresh: new Date().toISOString()
      });
    };

    sendHeartbeat();
    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds

    return () => clearInterval(heartbeatInterval);
  }, [isPlaying, currentSlide]);

  // Listen for control commands
  useEffect(() => {
    const checkCommands = () => {
      const commands = tvControlService.getStoredCommands();
      if (commands.length > 0) {
        commands.forEach((command: TVControlCommand) => {
          handleControlCommand(command);
        });
        tvControlService.clearProcessedCommands();
      }
    };

    const commandInterval = setInterval(checkCommands, 1000); // Check every second
    return () => clearInterval(commandInterval);
  }, []);

  // Handle control commands
  const handleControlCommand = (command: TVControlCommand) => {
    switch (command.action) {
      case 'play':
        setIsPlaying(true);
        break;
      case 'pause':
        setIsPlaying(false);
        break;
      case 'next':
        if (slideshowRef.current?.nextSlide) {
          slideshowRef.current.nextSlide();
        }
        break;
      case 'previous':
        if (slideshowRef.current?.prevSlide) {
          slideshowRef.current.prevSlide();
        }
        break;
      case 'refresh':
        refreshAnnouncements();
        refreshEvents();
        setRefreshKey(prev => prev + 1);
        break;
      case 'emergency':
        // Emergency messages are handled through settings
        break;
    }
  };

  // Update current time every minute
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timeInterval);
  }, []);

  // Auto-refresh data every 2 minutes
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      refreshAnnouncements();
      refreshEvents();
      setRefreshKey(prev => prev + 1);
    }, 120000); // Refresh every 2 minutes

    return () => clearInterval(refreshInterval);
  }, [refreshAnnouncements, refreshEvents]);

  // Auto-reload page every 10 minutes as backup
  useEffect(() => {
    const reloadInterval = setInterval(() => {
      window.location.reload();
    }, 600000); // Reload every 10 minutes

    return () => clearInterval(reloadInterval);
  }, []);

  // Format current date and time
  const formatDateTime = () => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return currentTime.toLocaleDateString('en-US', options);
  };

  // Filter upcoming events (next 30 days)
  const getUpcomingEvents = () => {
    if (!settings.showCalendarEvents) return [];

    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);

    return events.filter(event => {
      const eventDate = new Date(event.event_date);
      const matchesCategory = settings.eventCategories.length === 0 ||
        (event.category_id && settings.eventCategories.includes(event.category_id));
      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;
    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())
      .slice(0, settings.maxEvents);
  };

  // Combine announcements and events for slideshow
  const createSlideContent = () => {
    const slides: React.ReactNode[] = [];

    // Add announcements
    if (settings.showAnnouncements && announcements && announcements.length > 0) {
      const filteredAnnouncements = announcements.filter(announcement => {
        const matchesCategory = settings.announcementCategories.length === 0 ||
          settings.announcementCategories.includes(announcement.category_id);
        return matchesCategory;
      }).slice(0, settings.maxAnnouncements);

      filteredAnnouncements.forEach((announcement) => (
        slides.push(
          <TVAnnouncement
            key={`announcement-${announcement.announcement_id}-${refreshKey}`}
            announcement={announcement}
          />
        )
      ));
    }

    // Add upcoming events
    const upcomingEvents = getUpcomingEvents();
    if (upcomingEvents.length > 0) {
      upcomingEvents.forEach((event) => (
        slides.push(
          <TVCalendarEvent
            key={`event-${event.calendar_id}-${refreshKey}`}
            event={event}
          />
        )
      ));
    }

    return slides;
  };

  const slides = createSlideContent();
  const isLoading = announcementsLoading || eventsLoading;
  const hasError = announcementsError || eventsError;

  return (
    <div style={{
      margin: 0,
      padding: 0,
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      background: 'linear-gradient(135deg, #22c55e 0%, #fbbf24 100%)', // Green to Yellow gradient
      minHeight: '100vh',
      overflowX: 'hidden',
      color: '#ffffff'
    }}>
      {/* Current date and time */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.95)',
        padding: '1rem 2rem',
        textAlign: 'center',
        fontSize: '1.8rem',
        fontWeight: '500',
        color: '#2c3e50',
        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)'
      }}>
        {formatDateTime()}
      </div>

      {/* Main content area */}
      <main className="tv-content">
        {/* Loading state */}
        {isLoading && (
          <div className="tv-loading">
            <div className="tv-loading-spinner"></div>
            <div>Loading latest announcements and events...</div>
          </div>
        )}

        {/* Error state */}
        {hasError && !isLoading && (
          <div className="tv-error">
            <div style={{ fontSize: '3rem', marginBottom: '2rem', fontWeight: 'bold', color: '#e74c3c' }}>ERROR</div>
            <div>Unable to load content</div>
            <div style={{ fontSize: '2rem', marginTop: '1rem', opacity: 0.8 }}>
              Please check your internet connection
            </div>
          </div>
        )}

        {/* Emergency Message Override */}
        {settings.emergencyActive && settings.emergencyMessage && (
          console.log('Rendering emergency message:', settings.emergencyMessage),
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(220, 53, 69, 0.95)',
            color: 'white',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            animation: 'emergency-flash 2s infinite'
          }}>
            <div style={{
              fontSize: '4rem',
              marginBottom: '2rem',
              animation: 'emergency-pulse 1s infinite',
              fontWeight: 'bold',
              color: '#ffffff'
            }}>
              EMERGENCY
            </div>
            <div style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: '2rem',
              textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
            }}>
              EMERGENCY ALERT
            </div>
            <div style={{
              fontSize: '3rem',
              textAlign: 'center',
              lineHeight: '1.4',
              maxWidth: '80%',
              background: 'rgba(0, 0, 0, 0.3)',
              padding: '2rem',
              borderRadius: '20px'
            }}>
              {settings.emergencyMessage}
            </div>
          </div>
        )}

        {/* Content slideshow */}
        {!isLoading && !hasError && !settings.emergencyActive && (
          <>
            {slides.length > 0 ? (
              <TVSlideshow
                ref={slideshowRef}
                autoPlayInterval={settings.slideInterval}
                showProgress={true}
                isPlaying={isPlaying && settings.autoPlay}
                onSlideChange={setCurrentSlide}
              >
                {slides}
              </TVSlideshow>
            ) : (
              <div className="tv-no-content">
                <div style={{ fontSize: '3rem', marginBottom: '3rem', fontWeight: 'bold', color: '#6c757d' }}>NO CONTENT</div>
                <div>No announcements or events to display</div>
                <div style={{ fontSize: '2rem', marginTop: '2rem', opacity: 0.7 }}>
                  Check back later for updates
                </div>
              </div>
            )}
          </>
        )}
      </main>



      {/* Meta refresh as backup */}
      <meta httpEquiv="refresh" content="600" />
    </div>
  );
};

export default TVDisplay;
