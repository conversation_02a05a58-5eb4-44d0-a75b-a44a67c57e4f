{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:39'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:39'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:39'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:39'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:48'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:49'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:49'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-08-04 00:52:55'
}
