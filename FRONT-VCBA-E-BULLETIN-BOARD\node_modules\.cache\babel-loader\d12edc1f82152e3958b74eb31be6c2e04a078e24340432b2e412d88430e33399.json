{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVSlideshow.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVSlideshow = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  children,\n  autoPlayInterval = 12000,\n  // 12 seconds default\n  showProgress = true,\n  className = '',\n  isPlaying = true,\n  onSlideChange\n}, ref) => {\n  _s();\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [progress, setProgress] = useState(0);\n  const totalSlides = children.length;\n\n  // Go to next slide\n  const nextSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    const newSlide = (currentSlide + 1) % totalSlides;\n    setCurrentSlide(newSlide);\n    setProgress(0);\n    onSlideChange === null || onSlideChange === void 0 ? void 0 : onSlideChange(newSlide);\n  }, [totalSlides, currentSlide, onSlideChange]);\n\n  // Go to previous slide\n  const prevSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    const newSlide = (currentSlide - 1 + totalSlides) % totalSlides;\n    setCurrentSlide(newSlide);\n    setProgress(0);\n    onSlideChange === null || onSlideChange === void 0 ? void 0 : onSlideChange(newSlide);\n  }, [totalSlides, currentSlide, onSlideChange]);\n\n  // Go to specific slide\n  const goToSlide = useCallback(index => {\n    if (index >= 0 && index < totalSlides) {\n      setCurrentSlide(index);\n      setProgress(0);\n      onSlideChange === null || onSlideChange === void 0 ? void 0 : onSlideChange(index);\n    }\n  }, [totalSlides, onSlideChange]);\n\n  // Expose methods to parent component\n  useImperativeHandle(ref, () => ({\n    nextSlide,\n    prevSlide,\n    goToSlide,\n    getCurrentSlide: () => currentSlide\n  }), [nextSlide, prevSlide, goToSlide, currentSlide]);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n    const interval = setInterval(() => {\n      nextSlide();\n    }, autoPlayInterval);\n    return () => clearInterval(interval);\n  }, [isPlaying, autoPlayInterval, nextSlide, totalSlides]);\n\n  // Progress bar animation\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        const increment = 100 / (autoPlayInterval / 100);\n        return prev + increment;\n      });\n    }, 100);\n    return () => clearInterval(progressInterval);\n  }, [currentSlide, isPlaying, autoPlayInterval, totalSlides]);\n\n  // Reset progress when slide changes\n  useEffect(() => {\n    setProgress(0);\n  }, [currentSlide]);\n\n  // Pause/resume on hover (optional for TV display)\n  const handleMouseEnter = () => {\n    // Hover pause disabled for TV display - controlled externally\n  };\n  const handleMouseLeave = () => {\n    // Hover resume disabled for TV display - controlled externally\n  };\n\n  // Handle keyboard navigation (for testing)\n  useEffect(() => {\n    const handleKeyPress = event => {\n      switch (event.key) {\n        case 'ArrowLeft':\n          prevSlide();\n          break;\n        case 'ArrowRight':\n          nextSlide();\n          break;\n        case ' ':\n          // Space key disabled for TV display - controlled externally\n          break;\n        default:\n          break;\n      }\n    };\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [nextSlide, prevSlide, isPlaying]);\n\n  // Don't render if no slides\n  if (totalSlides === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-no-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '4rem',\n          marginBottom: '2rem'\n        },\n        children: \"\\uD83D\\uDCED\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"No content available to display\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Single slide - no slideshow needed\n  if (totalSlides === 1) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `tv-slideshow ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-slide active\",\n        children: children[0]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `tv-slideshow ${className}`,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [children.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `tv-slide ${index === currentSlide ? 'active' : index === (currentSlide - 1 + totalSlides) % totalSlides ? 'prev' : ''}`,\n      style: {\n        zIndex: index === currentSlide ? 10 : 1\n      },\n      children: child\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this)), showProgress && totalSlides > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-progress\",\n      children: children.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `tv-progress-dot ${index === currentSlide ? 'active' : ''}`,\n        onClick: () => goToSlide(index),\n        style: {\n          cursor: 'pointer',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        children: index === currentSlide && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            height: '100%',\n            width: `${Math.min(progress, 100)}%`,\n            background: 'rgba(255, 255, 255, 0.8)',\n            borderRadius: '50%',\n            transition: 'width 0.1s linear'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 17\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '2rem',\n        right: '3rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        zIndex: 1000\n      },\n      children: [currentSlide + 1, \" / \", totalSlides]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '2rem',\n        left: '3rem',\n        background: isPlaying ? 'rgba(39, 174, 96, 0.8)' : 'rgba(231, 76, 60, 0.8)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.6rem',\n        fontWeight: '500',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: isPlaying ? '▶️' : '⏸️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: isPlaying ? 'Auto-playing' : 'Paused'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '6rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'none',\n        // Hidden for TV display\n        gap: '2rem',\n        zIndex: 1000\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: prevSlide,\n        style: {\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '50%',\n          width: '60px',\n          height: '60px',\n          fontSize: '2rem',\n          cursor: 'pointer'\n        },\n        children: \"\\u2190\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {/* Play/pause controlled externally */},\n        style: {\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '50%',\n          width: '60px',\n          height: '60px',\n          fontSize: '1.5rem',\n          cursor: 'pointer',\n          display: 'none' // Hidden for TV display\n        },\n        children: isPlaying ? '⏸️' : '▶️'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: nextSlide,\n        style: {\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          border: 'none',\n          borderRadius: '50%',\n          width: '60px',\n          height: '60px',\n          fontSize: '2rem',\n          cursor: 'pointer'\n        },\n        children: \"\\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n}, \"hVnI5EYRspKEokbVLFBgqqyFQ3E=\")), \"hVnI5EYRspKEokbVLFBgqqyFQ3E=\");\n_c2 = TVSlideshow;\nexport default TVSlideshow;\nvar _c, _c2;\n$RefreshReg$(_c, \"TVSlideshow$forwardRef\");\n$RefreshReg$(_c2, \"TVSlideshow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "forwardRef", "useImperativeHandle", "jsxDEV", "_jsxDEV", "TVSlideshow", "_s", "_c", "children", "autoPlayInterval", "showProgress", "className", "isPlaying", "onSlideChange", "ref", "currentSlide", "setCurrentSlide", "progress", "setProgress", "totalSlides", "length", "nextSlide", "newSlide", "prevSlide", "goToSlide", "index", "getCurrentSlide", "interval", "setInterval", "clearInterval", "progressInterval", "prev", "increment", "handleMouseEnter", "handleMouseLeave", "handleKeyPress", "event", "key", "window", "addEventListener", "removeEventListener", "style", "fontSize", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseEnter", "onMouseLeave", "map", "child", "zIndex", "_", "onClick", "cursor", "position", "overflow", "top", "left", "height", "width", "Math", "min", "background", "borderRadius", "transition", "right", "color", "padding", "fontWeight", "display", "alignItems", "gap", "bottom", "transform", "border", "_c2", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVSlideshow.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport '../../styles/tv.css';\n\ninterface TVSlideshowProps {\n  children: React.ReactNode[];\n  autoPlayInterval?: number; // in milliseconds\n  showProgress?: boolean;\n  className?: string;\n  isPlaying?: boolean;\n  onSlideChange?: (slideIndex: number) => void;\n}\n\ninterface TVSlideshowRef {\n  nextSlide: () => void;\n  prevSlide: () => void;\n  goToSlide: (index: number) => void;\n  getCurrentSlide: () => number;\n}\n\nconst TVSlideshow = forwardRef<TVSlideshowRef, TVSlideshowProps>(({\n  children,\n  autoPlayInterval = 12000, // 12 seconds default\n  showProgress = true,\n  className = '',\n  isPlaying = true,\n  onSlideChange\n}, ref) => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [progress, setProgress] = useState(0);\n\n  const totalSlides = children.length;\n\n  // Go to next slide\n  const nextSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    const newSlide = (currentSlide + 1) % totalSlides;\n    setCurrentSlide(newSlide);\n    setProgress(0);\n    onSlideChange?.(newSlide);\n  }, [totalSlides, currentSlide, onSlideChange]);\n\n  // Go to previous slide\n  const prevSlide = useCallback(() => {\n    if (totalSlides === 0) return;\n    const newSlide = (currentSlide - 1 + totalSlides) % totalSlides;\n    setCurrentSlide(newSlide);\n    setProgress(0);\n    onSlideChange?.(newSlide);\n  }, [totalSlides, currentSlide, onSlideChange]);\n\n  // Go to specific slide\n  const goToSlide = useCallback((index: number) => {\n    if (index >= 0 && index < totalSlides) {\n      setCurrentSlide(index);\n      setProgress(0);\n      onSlideChange?.(index);\n    }\n  }, [totalSlides, onSlideChange]);\n\n  // Expose methods to parent component\n  useImperativeHandle(ref, () => ({\n    nextSlide,\n    prevSlide,\n    goToSlide,\n    getCurrentSlide: () => currentSlide\n  }), [nextSlide, prevSlide, goToSlide, currentSlide]);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n\n    const interval = setInterval(() => {\n      nextSlide();\n    }, autoPlayInterval);\n\n    return () => clearInterval(interval);\n  }, [isPlaying, autoPlayInterval, nextSlide, totalSlides]);\n\n  // Progress bar animation\n  useEffect(() => {\n    if (!isPlaying || totalSlides <= 1) return;\n\n    const progressInterval = setInterval(() => {\n      setProgress((prev) => {\n        const increment = 100 / (autoPlayInterval / 100);\n        return prev + increment;\n      });\n    }, 100);\n\n    return () => clearInterval(progressInterval);\n  }, [currentSlide, isPlaying, autoPlayInterval, totalSlides]);\n\n  // Reset progress when slide changes\n  useEffect(() => {\n    setProgress(0);\n  }, [currentSlide]);\n\n  // Pause/resume on hover (optional for TV display)\n  const handleMouseEnter = () => {\n    // Hover pause disabled for TV display - controlled externally\n  };\n\n  const handleMouseLeave = () => {\n    // Hover resume disabled for TV display - controlled externally\n  };\n\n  // Handle keyboard navigation (for testing)\n  useEffect(() => {\n    const handleKeyPress = (event: KeyboardEvent) => {\n      switch (event.key) {\n        case 'ArrowLeft':\n          prevSlide();\n          break;\n        case 'ArrowRight':\n          nextSlide();\n          break;\n        case ' ':\n          // Space key disabled for TV display - controlled externally\n          break;\n        default:\n          break;\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [nextSlide, prevSlide, isPlaying]);\n\n  // Don't render if no slides\n  if (totalSlides === 0) {\n    return (\n      <div className=\"tv-no-content\">\n        <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>📭</div>\n        <div>No content available to display</div>\n      </div>\n    );\n  }\n\n  // Single slide - no slideshow needed\n  if (totalSlides === 1) {\n    return (\n      <div className={`tv-slideshow ${className}`}>\n        <div className=\"tv-slide active\">\n          {children[0]}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={`tv-slideshow ${className}`}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {/* Slides */}\n      {children.map((child, index) => (\n        <div\n          key={index}\n          className={`tv-slide ${\n            index === currentSlide \n              ? 'active' \n              : index === (currentSlide - 1 + totalSlides) % totalSlides \n                ? 'prev' \n                : ''\n          }`}\n          style={{\n            zIndex: index === currentSlide ? 10 : 1\n          }}\n        >\n          {child}\n        </div>\n      ))}\n\n      {/* Progress indicators */}\n      {showProgress && totalSlides > 1 && (\n        <div className=\"tv-progress\">\n          {children.map((_, index) => (\n            <div\n              key={index}\n              className={`tv-progress-dot ${index === currentSlide ? 'active' : ''}`}\n              onClick={() => goToSlide(index)}\n              style={{\n                cursor: 'pointer',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n            >\n              {/* Progress bar for current slide */}\n              {index === currentSlide && (\n                <div\n                  style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    height: '100%',\n                    width: `${Math.min(progress, 100)}%`,\n                    background: 'rgba(255, 255, 255, 0.8)',\n                    borderRadius: '50%',\n                    transition: 'width 0.1s linear'\n                  }}\n                />\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Slide counter */}\n      <div style={{\n        position: 'fixed',\n        top: '2rem',\n        right: '3rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.8rem',\n        fontWeight: '500',\n        zIndex: 1000\n      }}>\n        {currentSlide + 1} / {totalSlides}\n      </div>\n\n      {/* Auto-play indicator */}\n      <div style={{\n        position: 'fixed',\n        top: '2rem',\n        left: '3rem',\n        background: isPlaying ? 'rgba(39, 174, 96, 0.8)' : 'rgba(231, 76, 60, 0.8)',\n        color: 'white',\n        padding: '1rem 2rem',\n        borderRadius: '25px',\n        fontSize: '1.6rem',\n        fontWeight: '500',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      }}>\n        <span>{isPlaying ? '▶️' : '⏸️'}</span>\n        <span>{isPlaying ? 'Auto-playing' : 'Paused'}</span>\n      </div>\n\n      {/* Navigation arrows (hidden by default, can be shown for testing) */}\n      <div style={{\n        position: 'fixed',\n        bottom: '6rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'none', // Hidden for TV display\n        gap: '2rem',\n        zIndex: 1000\n      }}>\n        <button\n          onClick={prevSlide}\n          style={{\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '50%',\n            width: '60px',\n            height: '60px',\n            fontSize: '2rem',\n            cursor: 'pointer'\n          }}\n        >\n          ←\n        </button>\n        <button\n          onClick={() => {/* Play/pause controlled externally */}}\n          style={{\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '50%',\n            width: '60px',\n            height: '60px',\n            fontSize: '1.5rem',\n            cursor: 'pointer',\n            display: 'none' // Hidden for TV display\n          }}\n        >\n          {isPlaying ? '⏸️' : '▶️'}\n        </button>\n        <button\n          onClick={nextSlide}\n          style={{\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            border: 'none',\n            borderRadius: '50%',\n            width: '60px',\n            height: '60px',\n            fontSize: '2rem',\n            cursor: 'pointer'\n          }}\n        >\n          →\n        </button>\n      </div>\n    </div>\n  );\n});\n\nexport default TVSlideshow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAChG,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkB7B,MAAMC,WAAW,gBAAAC,EAAA,cAAGL,UAAU,CAAAM,EAAA,GAAAD,EAAA,CAAmC,CAAC;EAChEE,QAAQ;EACRC,gBAAgB,GAAG,KAAK;EAAE;EAC1BC,YAAY,GAAG,IAAI;EACnBC,SAAS,GAAG,EAAE;EACdC,SAAS,GAAG,IAAI;EAChBC;AACF,CAAC,EAAEC,GAAG,KAAK;EAAAR,EAAA;EACT,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;EAE3C,MAAMqB,WAAW,GAAGX,QAAQ,CAACY,MAAM;;EAEnC;EACA,MAAMC,SAAS,GAAGrB,WAAW,CAAC,MAAM;IAClC,IAAImB,WAAW,KAAK,CAAC,EAAE;IACvB,MAAMG,QAAQ,GAAG,CAACP,YAAY,GAAG,CAAC,IAAII,WAAW;IACjDH,eAAe,CAACM,QAAQ,CAAC;IACzBJ,WAAW,CAAC,CAAC,CAAC;IACdL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGS,QAAQ,CAAC;EAC3B,CAAC,EAAE,CAACH,WAAW,EAAEJ,YAAY,EAAEF,aAAa,CAAC,CAAC;;EAE9C;EACA,MAAMU,SAAS,GAAGvB,WAAW,CAAC,MAAM;IAClC,IAAImB,WAAW,KAAK,CAAC,EAAE;IACvB,MAAMG,QAAQ,GAAG,CAACP,YAAY,GAAG,CAAC,GAAGI,WAAW,IAAIA,WAAW;IAC/DH,eAAe,CAACM,QAAQ,CAAC;IACzBJ,WAAW,CAAC,CAAC,CAAC;IACdL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGS,QAAQ,CAAC;EAC3B,CAAC,EAAE,CAACH,WAAW,EAAEJ,YAAY,EAAEF,aAAa,CAAC,CAAC;;EAE9C;EACA,MAAMW,SAAS,GAAGxB,WAAW,CAAEyB,KAAa,IAAK;IAC/C,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGN,WAAW,EAAE;MACrCH,eAAe,CAACS,KAAK,CAAC;MACtBP,WAAW,CAAC,CAAC,CAAC;MACdL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGY,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACN,WAAW,EAAEN,aAAa,CAAC,CAAC;;EAEhC;EACAX,mBAAmB,CAACY,GAAG,EAAE,OAAO;IAC9BO,SAAS;IACTE,SAAS;IACTC,SAAS;IACTE,eAAe,EAAEA,CAAA,KAAMX;EACzB,CAAC,CAAC,EAAE,CAACM,SAAS,EAAEE,SAAS,EAAEC,SAAS,EAAET,YAAY,CAAC,CAAC;;EAEpD;EACAhB,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,SAAS,IAAIO,WAAW,IAAI,CAAC,EAAE;IAEpC,MAAMQ,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCP,SAAS,CAAC,CAAC;IACb,CAAC,EAAEZ,gBAAgB,CAAC;IAEpB,OAAO,MAAMoB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACf,SAAS,EAAEH,gBAAgB,EAAEY,SAAS,EAAEF,WAAW,CAAC,CAAC;;EAEzD;EACApB,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,SAAS,IAAIO,WAAW,IAAI,CAAC,EAAE;IAEpC,MAAMW,gBAAgB,GAAGF,WAAW,CAAC,MAAM;MACzCV,WAAW,CAAEa,IAAI,IAAK;QACpB,MAAMC,SAAS,GAAG,GAAG,IAAIvB,gBAAgB,GAAG,GAAG,CAAC;QAChD,OAAOsB,IAAI,GAAGC,SAAS;MACzB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMH,aAAa,CAACC,gBAAgB,CAAC;EAC9C,CAAC,EAAE,CAACf,YAAY,EAAEH,SAAS,EAAEH,gBAAgB,EAAEU,WAAW,CAAC,CAAC;;EAE5D;EACApB,SAAS,CAAC,MAAM;IACdmB,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAACH,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;EAAA,CACD;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;EAAA,CACD;;EAED;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMoC,cAAc,GAAIC,KAAoB,IAAK;MAC/C,QAAQA,KAAK,CAACC,GAAG;QACf,KAAK,WAAW;UACdd,SAAS,CAAC,CAAC;UACX;QACF,KAAK,YAAY;UACfF,SAAS,CAAC,CAAC;UACX;QACF,KAAK,GAAG;UACN;UACA;QACF;UACE;MACJ;IACF,CAAC;IAEDiB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,cAAc,CAAC;IAClD,OAAO,MAAMG,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEL,cAAc,CAAC;EACpE,CAAC,EAAE,CAACd,SAAS,EAAEE,SAAS,EAAEX,SAAS,CAAC,CAAC;;EAErC;EACA,IAAIO,WAAW,KAAK,CAAC,EAAE;IACrB,oBACEf,OAAA;MAAKO,SAAS,EAAC,eAAe;MAAAH,QAAA,gBAC5BJ,OAAA;QAAKqC,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAnC,QAAA,EAAC;MAAE;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChE3C,OAAA;QAAAI,QAAA,EAAK;MAA+B;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAEV;;EAEA;EACA,IAAI5B,WAAW,KAAK,CAAC,EAAE;IACrB,oBACEf,OAAA;MAAKO,SAAS,EAAE,gBAAgBA,SAAS,EAAG;MAAAH,QAAA,eAC1CJ,OAAA;QAAKO,SAAS,EAAC,iBAAiB;QAAAH,QAAA,EAC7BA,QAAQ,CAAC,CAAC;MAAC;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3C,OAAA;IACEO,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCqC,YAAY,EAAEf,gBAAiB;IAC/BgB,YAAY,EAAEf,gBAAiB;IAAA1B,QAAA,GAG9BA,QAAQ,CAAC0C,GAAG,CAAC,CAACC,KAAK,EAAE1B,KAAK,kBACzBrB,OAAA;MAEEO,SAAS,EAAE,YACTc,KAAK,KAAKV,YAAY,GAClB,QAAQ,GACRU,KAAK,KAAK,CAACV,YAAY,GAAG,CAAC,GAAGI,WAAW,IAAIA,WAAW,GACtD,MAAM,GACN,EAAE,EACP;MACHsB,KAAK,EAAE;QACLW,MAAM,EAAE3B,KAAK,KAAKV,YAAY,GAAG,EAAE,GAAG;MACxC,CAAE;MAAAP,QAAA,EAED2C;IAAK,GAZD1B,KAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAaP,CACN,CAAC,EAGDrC,YAAY,IAAIS,WAAW,GAAG,CAAC,iBAC9Bf,OAAA;MAAKO,SAAS,EAAC,aAAa;MAAAH,QAAA,EACzBA,QAAQ,CAAC0C,GAAG,CAAC,CAACG,CAAC,EAAE5B,KAAK,kBACrBrB,OAAA;QAEEO,SAAS,EAAE,mBAAmBc,KAAK,KAAKV,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;QACvEuC,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAACC,KAAK,CAAE;QAChCgB,KAAK,EAAE;UACLc,MAAM,EAAE,SAAS;UACjBC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE;QACZ,CAAE;QAAAjD,QAAA,EAGDiB,KAAK,KAAKV,YAAY,iBACrBX,OAAA;UACEqC,KAAK,EAAE;YACLe,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC9C,QAAQ,EAAE,GAAG,CAAC,GAAG;YACpC+C,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,KAAK;YACnBC,UAAU,EAAE;UACd;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACF,GAvBItB,KAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGD3C,OAAA;MAAKqC,KAAK,EAAE;QACVe,QAAQ,EAAE,OAAO;QACjBE,GAAG,EAAE,MAAM;QACXS,KAAK,EAAE,MAAM;QACbH,UAAU,EAAE,oBAAoB;QAChCI,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,WAAW;QACpBJ,YAAY,EAAE,MAAM;QACpBvB,QAAQ,EAAE,QAAQ;QAClB4B,UAAU,EAAE,KAAK;QACjBlB,MAAM,EAAE;MACV,CAAE;MAAA5C,QAAA,GACCO,YAAY,GAAG,CAAC,EAAC,KAAG,EAACI,WAAW;IAAA;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAGN3C,OAAA;MAAKqC,KAAK,EAAE;QACVe,QAAQ,EAAE,OAAO;QACjBE,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,MAAM;QACZK,UAAU,EAAEpD,SAAS,GAAG,wBAAwB,GAAG,wBAAwB;QAC3EwD,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,WAAW;QACpBJ,YAAY,EAAE,MAAM;QACpBvB,QAAQ,EAAE,QAAQ;QAClB4B,UAAU,EAAE,KAAK;QACjBlB,MAAM,EAAE,IAAI;QACZmB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAjE,QAAA,gBACAJ,OAAA;QAAAI,QAAA,EAAOI,SAAS,GAAG,IAAI,GAAG;MAAI;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACtC3C,OAAA;QAAAI,QAAA,EAAOI,SAAS,GAAG,cAAc,GAAG;MAAQ;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGN3C,OAAA;MAAKqC,KAAK,EAAE;QACVe,QAAQ,EAAE,OAAO;QACjBkB,MAAM,EAAE,MAAM;QACdf,IAAI,EAAE,KAAK;QACXgB,SAAS,EAAE,kBAAkB;QAC7BJ,OAAO,EAAE,MAAM;QAAE;QACjBE,GAAG,EAAE,MAAM;QACXrB,MAAM,EAAE;MACV,CAAE;MAAA5C,QAAA,gBACAJ,OAAA;QACEkD,OAAO,EAAE/B,SAAU;QACnBkB,KAAK,EAAE;UACLuB,UAAU,EAAE,oBAAoB;UAChCI,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdX,YAAY,EAAE,KAAK;UACnBJ,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdlB,QAAQ,EAAE,MAAM;UAChBa,MAAM,EAAE;QACV,CAAE;QAAA/C,QAAA,EACH;MAED;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3C,OAAA;QACEkD,OAAO,EAAEA,CAAA,KAAM,CAAC,uCAAwC;QACxDb,KAAK,EAAE;UACLuB,UAAU,EAAE,oBAAoB;UAChCI,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdX,YAAY,EAAE,KAAK;UACnBJ,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdlB,QAAQ,EAAE,QAAQ;UAClBa,MAAM,EAAE,SAAS;UACjBgB,OAAO,EAAE,MAAM,CAAC;QAClB,CAAE;QAAA/D,QAAA,EAEDI,SAAS,GAAG,IAAI,GAAG;MAAI;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACT3C,OAAA;QACEkD,OAAO,EAAEjC,SAAU;QACnBoB,KAAK,EAAE;UACLuB,UAAU,EAAE,oBAAoB;UAChCI,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdX,YAAY,EAAE,KAAK;UACnBJ,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdlB,QAAQ,EAAE,MAAM;UAChBa,MAAM,EAAE;QACV,CAAE;QAAA/C,QAAA,EACH;MAED;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC,kCAAC;AAAC8B,GAAA,GA5RGxE,WAAW;AA8RjB,eAAeA,WAAW;AAAC,IAAAE,EAAA,EAAAsE,GAAA;AAAAC,YAAA,CAAAvE,EAAA;AAAAuE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}