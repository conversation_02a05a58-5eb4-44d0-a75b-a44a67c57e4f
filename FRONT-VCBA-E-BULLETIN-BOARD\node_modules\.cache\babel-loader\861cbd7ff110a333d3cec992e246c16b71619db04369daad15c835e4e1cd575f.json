{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\tv\\\\TVDisplay.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TVDisplay = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach(command => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = command => {\n    var _slideshowRef$current, _slideshowRef$current2;\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if ((_slideshowRef$current = slideshowRef.current) !== null && _slideshowRef$current !== void 0 && _slideshowRef$current.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if ((_slideshowRef$current2 = slideshowRef.current) !== null && _slideshowRef$current2 !== void 0 && _slideshowRef$current2.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 || event.category_id && settings.eventCategories.includes(event.category_id);\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime()).slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides = [];\n\n    // Add announcements\n    if (announcements && announcements.length > 0) {\n      announcements.forEach((announcement, index) => slides.push(/*#__PURE__*/_jsxDEV(TVAnnouncement, {\n        announcement: announcement\n      }, `announcement-${announcement.announcement_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event, index) => slides.push(/*#__PURE__*/_jsxDEV(TVCalendarEvent, {\n        event: event\n      }, `event-${event.calendar_id}-${refreshKey}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this)));\n    }\n    return slides;\n  };\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tv-display\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"tv-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"VCBA E-Bulletin Board\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subtitle\",\n        children: \"Villamor College of Business and Arts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-datetime\",\n      children: formatDateTime()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"tv-content\",\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Loading latest announcements and events...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), hasError && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tv-error\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            marginBottom: '2rem'\n          },\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Unable to load content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            marginTop: '1rem',\n            opacity: 0.8\n          },\n          children: \"Please check your internet connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), !isLoading && !hasError && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: slides.length > 0 ? /*#__PURE__*/_jsxDEV(TVSlideshow, {\n          autoPlayInterval: 15000 // 15 seconds per slide\n          ,\n          showProgress: true,\n          children: slides\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tv-no-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '6rem',\n              marginBottom: '3rem'\n            },\n            children: \"\\uD83D\\uDCE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"No announcements or events to display\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2rem',\n              marginTop: '2rem',\n              opacity: 0.7\n            },\n            children: \"Check back later for updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        bottom: '1rem',\n        right: '2rem',\n        background: 'rgba(0, 0, 0, 0.6)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '20px',\n        fontSize: '1.4rem',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\uD83D\\uDD04\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Auto-refresh active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n      httpEquiv: \"refresh\",\n      content: \"600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n_s(TVDisplay, \"zveYiNm9IZeexjC96jG8G7nxux0=\", false, function () {\n  return [useAnnouncements, useCalendar];\n});\n_c = TVDisplay;\nexport default TVDisplay;\nvar _c;\n$RefreshReg$(_c, \"TVDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAnnouncements", "useCalendar", "tvControlService", "TVAnnouncement", "TVCalendarEvent", "TVSlideshow", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TVDisplay", "_s", "currentTime", "setCurrentTime", "Date", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "settings", "setSettings", "getSettings", "isPlaying", "setIsPlaying", "currentSlide", "setCurrentSlide", "slideshowRef", "currentDate", "announcements", "loading", "announcementsLoading", "error", "announcementsError", "refresh", "refreshAnnouncements", "status", "page", "limit", "maxAnnouncements", "sort_by", "sort_order", "events", "eventsLoading", "eventsError", "refreshEvents", "unsubscribe", "onSettingsChange", "sendHeartbeat", "localStorage", "setItem", "now", "toString", "updateStatus", "isOnline", "totalSlides", "createSlideContent", "length", "lastRefresh", "toISOString", "heartbeatInterval", "setInterval", "clearInterval", "checkCommands", "commands", "getStoredCommands", "for<PERSON>ach", "command", "handleControlCommand", "clearProcessedCommands", "commandInterval", "_slideshowRef$current", "_slideshowRef$current2", "action", "current", "nextSlide", "prevSlide", "prev", "timeInterval", "refreshInterval", "reloadInterval", "window", "location", "reload", "formatDateTime", "options", "weekday", "year", "month", "day", "hour", "minute", "toLocaleDateString", "getUpcomingEvents", "showCalendarEvents", "today", "thirtyDaysFromNow", "setDate", "getDate", "filter", "event", "eventDate", "event_date", "matchesCategory", "eventCategories", "category_id", "includes", "is_active", "sort", "a", "b", "getTime", "slice", "maxEvents", "slides", "announcement", "index", "push", "announcement_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "upcomingEvents", "calendar_id", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "className", "children", "style", "fontSize", "marginBottom", "marginTop", "opacity", "autoPlayInterval", "showProgress", "position", "bottom", "right", "background", "color", "padding", "borderRadius", "zIndex", "display", "alignItems", "gap", "httpEquiv", "content", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/tv/TVDisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAnnouncements } from '../../hooks/useAnnouncements';\nimport { useCalendar } from '../../hooks/useCalendar';\nimport { tvControlService, TVDisplaySettings, TVControlCommand } from '../../services/tvControlService';\nimport TVAnnouncement from '../../components/tv/TVAnnouncement';\nimport TVCalendarEvent from '../../components/tv/TVCalendarEvent';\nimport TVSlideshow from '../../components/tv/TVSlideshow';\nimport '../../styles/tv.css';\n\nconst TVDisplay: React.FC = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [refreshKey, setRefreshKey] = useState(0);\n  const [settings, setSettings] = useState<TVDisplaySettings>(tvControlService.getSettings());\n  const [isPlaying, setIsPlaying] = useState(true);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slideshowRef = useRef<any>(null);\n\n  // Get current date for calendar hook\n  const currentDate = new Date();\n\n  // Fetch announcements (published only, recent first)\n  const {\n    announcements,\n    loading: announcementsLoading,\n    error: announcementsError,\n    refresh: refreshAnnouncements\n  } = useAnnouncements({\n    status: 'published',\n    page: 1,\n    limit: settings.maxAnnouncements,\n    sort_by: 'created_at',\n    sort_order: 'DESC'\n  }, false); // Use student service (no auth required)\n\n  // Fetch calendar events\n  const {\n    events,\n    loading: eventsLoading,\n    error: eventsError,\n    refresh: refreshEvents\n  } = useCalendar(currentDate);\n\n  // Subscribe to settings changes\n  useEffect(() => {\n    const unsubscribe = tvControlService.onSettingsChange(setSettings);\n    return unsubscribe;\n  }, []);\n\n  // Send heartbeat to indicate TV is online\n  useEffect(() => {\n    const sendHeartbeat = () => {\n      localStorage.setItem('tv_display_heartbeat', Date.now().toString());\n      tvControlService.updateStatus({\n        isOnline: true,\n        isPlaying,\n        currentSlide,\n        totalSlides: createSlideContent().length,\n        lastRefresh: new Date().toISOString()\n      });\n    };\n\n    sendHeartbeat();\n    const heartbeatInterval = setInterval(sendHeartbeat, 5000); // Every 5 seconds\n\n    return () => clearInterval(heartbeatInterval);\n  }, [isPlaying, currentSlide]);\n\n  // Listen for control commands\n  useEffect(() => {\n    const checkCommands = () => {\n      const commands = tvControlService.getStoredCommands();\n      if (commands.length > 0) {\n        commands.forEach((command: TVControlCommand) => {\n          handleControlCommand(command);\n        });\n        tvControlService.clearProcessedCommands();\n      }\n    };\n\n    const commandInterval = setInterval(checkCommands, 1000); // Check every second\n    return () => clearInterval(commandInterval);\n  }, []);\n\n  // Handle control commands\n  const handleControlCommand = (command: TVControlCommand) => {\n    switch (command.action) {\n      case 'play':\n        setIsPlaying(true);\n        break;\n      case 'pause':\n        setIsPlaying(false);\n        break;\n      case 'next':\n        if (slideshowRef.current?.nextSlide) {\n          slideshowRef.current.nextSlide();\n        }\n        break;\n      case 'previous':\n        if (slideshowRef.current?.prevSlide) {\n          slideshowRef.current.prevSlide();\n        }\n        break;\n      case 'refresh':\n        refreshAnnouncements();\n        refreshEvents();\n        setRefreshKey(prev => prev + 1);\n        break;\n      case 'emergency':\n        // Emergency messages are handled through settings\n        break;\n    }\n  };\n\n  // Update current time every minute\n  useEffect(() => {\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(timeInterval);\n  }, []);\n\n  // Auto-refresh data every 2 minutes\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      refreshAnnouncements();\n      refreshEvents();\n      setRefreshKey(prev => prev + 1);\n    }, 120000); // Refresh every 2 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [refreshAnnouncements, refreshEvents]);\n\n  // Auto-reload page every 10 minutes as backup\n  useEffect(() => {\n    const reloadInterval = setInterval(() => {\n      window.location.reload();\n    }, 600000); // Reload every 10 minutes\n\n    return () => clearInterval(reloadInterval);\n  }, []);\n\n  // Format current date and time\n  const formatDateTime = () => {\n    const options: Intl.DateTimeFormatOptions = {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    };\n    return currentTime.toLocaleDateString('en-US', options);\n  };\n\n  // Filter upcoming events (next 30 days)\n  const getUpcomingEvents = () => {\n    if (!settings.showCalendarEvents) return [];\n\n    const today = new Date();\n    const thirtyDaysFromNow = new Date();\n    thirtyDaysFromNow.setDate(today.getDate() + 30);\n\n    return events.filter(event => {\n      const eventDate = new Date(event.event_date);\n      const matchesCategory = settings.eventCategories.length === 0 ||\n        (event.category_id && settings.eventCategories.includes(event.category_id));\n      return eventDate >= today && eventDate <= thirtyDaysFromNow && event.is_active && matchesCategory;\n    }).sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())\n      .slice(0, settings.maxEvents);\n  };\n\n  // Combine announcements and events for slideshow\n  const createSlideContent = () => {\n    const slides: React.ReactNode[] = [];\n    \n    // Add announcements\n    if (announcements && announcements.length > 0) {\n      announcements.forEach((announcement, index) => (\n        slides.push(\n          <TVAnnouncement \n            key={`announcement-${announcement.announcement_id}-${refreshKey}`} \n            announcement={announcement} \n          />\n        )\n      ));\n    }\n\n    // Add upcoming events\n    const upcomingEvents = getUpcomingEvents();\n    if (upcomingEvents.length > 0) {\n      upcomingEvents.forEach((event, index) => (\n        slides.push(\n          <TVCalendarEvent \n            key={`event-${event.calendar_id}-${refreshKey}`} \n            event={event} \n          />\n        )\n      ));\n    }\n\n    return slides;\n  };\n\n  const slides = createSlideContent();\n  const isLoading = announcementsLoading || eventsLoading;\n  const hasError = announcementsError || eventsError;\n\n  return (\n    <div className=\"tv-display\">\n      {/* Header with school branding */}\n      <header className=\"tv-header\">\n        <h1>VCBA E-Bulletin Board</h1>\n        <div className=\"subtitle\">Villamor College of Business and Arts</div>\n      </header>\n\n      {/* Current date and time */}\n      <div className=\"tv-datetime\">\n        {formatDateTime()}\n      </div>\n\n      {/* Main content area */}\n      <main className=\"tv-content\">\n        {/* Loading state */}\n        {isLoading && (\n          <div className=\"tv-loading\">\n            <div className=\"tv-loading-spinner\"></div>\n            <div>Loading latest announcements and events...</div>\n          </div>\n        )}\n\n        {/* Error state */}\n        {hasError && !isLoading && (\n          <div className=\"tv-error\">\n            <div style={{ fontSize: '4rem', marginBottom: '2rem' }}>⚠️</div>\n            <div>Unable to load content</div>\n            <div style={{ fontSize: '2rem', marginTop: '1rem', opacity: 0.8 }}>\n              Please check your internet connection\n            </div>\n          </div>\n        )}\n\n        {/* Content slideshow */}\n        {!isLoading && !hasError && (\n          <>\n            {slides.length > 0 ? (\n              <TVSlideshow \n                autoPlayInterval={15000} // 15 seconds per slide\n                showProgress={true}\n              >\n                {slides}\n              </TVSlideshow>\n            ) : (\n              <div className=\"tv-no-content\">\n                <div style={{ fontSize: '6rem', marginBottom: '3rem' }}>📢</div>\n                <div>No announcements or events to display</div>\n                <div style={{ fontSize: '2rem', marginTop: '2rem', opacity: 0.7 }}>\n                  Check back later for updates\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </main>\n\n      {/* Footer with refresh indicator */}\n      <div style={{\n        position: 'fixed',\n        bottom: '1rem',\n        right: '2rem',\n        background: 'rgba(0, 0, 0, 0.6)',\n        color: 'white',\n        padding: '0.8rem 1.5rem',\n        borderRadius: '20px',\n        fontSize: '1.4rem',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        gap: '0.5rem'\n      }}>\n        <span>🔄</span>\n        <span>Auto-refresh active</span>\n      </div>\n\n      {/* Meta refresh as backup */}\n      <meta httpEquiv=\"refresh\" content=\"600\" />\n    </div>\n  );\n};\n\nexport default TVDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAA6C,iCAAiC;AACvG,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAoBK,gBAAgB,CAACiB,WAAW,CAAC,CAAC,CAAC;EAC3F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM2B,YAAY,GAAGzB,MAAM,CAAM,IAAI,CAAC;;EAEtC;EACA,MAAM0B,WAAW,GAAG,IAAIX,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAM;IACJY,aAAa;IACbC,OAAO,EAAEC,oBAAoB;IAC7BC,KAAK,EAAEC,kBAAkB;IACzBC,OAAO,EAAEC;EACX,CAAC,GAAGhC,gBAAgB,CAAC;IACnBiC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAElB,QAAQ,CAACmB,gBAAgB;IAChCC,OAAO,EAAE,YAAY;IACrBC,UAAU,EAAE;EACd,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA,MAAM;IACJC,MAAM;IACNZ,OAAO,EAAEa,aAAa;IACtBX,KAAK,EAAEY,WAAW;IAClBV,OAAO,EAAEW;EACX,CAAC,GAAGzC,WAAW,CAACwB,WAAW,CAAC;;EAE5B;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM6C,WAAW,GAAGzC,gBAAgB,CAAC0C,gBAAgB,CAAC1B,WAAW,CAAC;IAClE,OAAOyB,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAM+C,aAAa,GAAGA,CAAA,KAAM;MAC1BC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEjC,IAAI,CAACkC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MACnE/C,gBAAgB,CAACgD,YAAY,CAAC;QAC5BC,QAAQ,EAAE,IAAI;QACd/B,SAAS;QACTE,YAAY;QACZ8B,WAAW,EAAEC,kBAAkB,CAAC,CAAC,CAACC,MAAM;QACxCC,WAAW,EAAE,IAAIzC,IAAI,CAAC,CAAC,CAAC0C,WAAW,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC;IAEDX,aAAa,CAAC,CAAC;IACf,MAAMY,iBAAiB,GAAGC,WAAW,CAACb,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;;IAE5D,OAAO,MAAMc,aAAa,CAACF,iBAAiB,CAAC;EAC/C,CAAC,EAAE,CAACrC,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAE7B;EACAxB,SAAS,CAAC,MAAM;IACd,MAAM8D,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,QAAQ,GAAG3D,gBAAgB,CAAC4D,iBAAiB,CAAC,CAAC;MACrD,IAAID,QAAQ,CAACP,MAAM,GAAG,CAAC,EAAE;QACvBO,QAAQ,CAACE,OAAO,CAAEC,OAAyB,IAAK;UAC9CC,oBAAoB,CAACD,OAAO,CAAC;QAC/B,CAAC,CAAC;QACF9D,gBAAgB,CAACgE,sBAAsB,CAAC,CAAC;MAC3C;IACF,CAAC;IAED,MAAMC,eAAe,GAAGT,WAAW,CAACE,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,OAAO,MAAMD,aAAa,CAACQ,eAAe,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMF,oBAAoB,GAAID,OAAyB,IAAK;IAAA,IAAAI,qBAAA,EAAAC,sBAAA;IAC1D,QAAQL,OAAO,CAACM,MAAM;MACpB,KAAK,MAAM;QACTjD,YAAY,CAAC,IAAI,CAAC;QAClB;MACF,KAAK,OAAO;QACVA,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,KAAK,MAAM;QACT,KAAA+C,qBAAA,GAAI5C,YAAY,CAAC+C,OAAO,cAAAH,qBAAA,eAApBA,qBAAA,CAAsBI,SAAS,EAAE;UACnChD,YAAY,CAAC+C,OAAO,CAACC,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,UAAU;QACb,KAAAH,sBAAA,GAAI7C,YAAY,CAAC+C,OAAO,cAAAF,sBAAA,eAApBA,sBAAA,CAAsBI,SAAS,EAAE;UACnCjD,YAAY,CAAC+C,OAAO,CAACE,SAAS,CAAC,CAAC;QAClC;QACA;MACF,KAAK,SAAS;QACZzC,oBAAoB,CAAC,CAAC;QACtBU,aAAa,CAAC,CAAC;QACf1B,aAAa,CAAC0D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/B;MACF,KAAK,WAAW;QACd;QACA;IACJ;EACF,CAAC;;EAED;EACA5E,SAAS,CAAC,MAAM;IACd,MAAM6E,YAAY,GAAGjB,WAAW,CAAC,MAAM;MACrC7C,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM6C,aAAa,CAACgB,YAAY,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7E,SAAS,CAAC,MAAM;IACd,MAAM8E,eAAe,GAAGlB,WAAW,CAAC,MAAM;MACxC1B,oBAAoB,CAAC,CAAC;MACtBU,aAAa,CAAC,CAAC;MACf1B,aAAa,CAAC0D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMf,aAAa,CAACiB,eAAe,CAAC;EAC7C,CAAC,EAAE,CAAC5C,oBAAoB,EAAEU,aAAa,CAAC,CAAC;;EAEzC;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM+E,cAAc,GAAGnB,WAAW,CAAC,MAAM;MACvCoB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMrB,aAAa,CAACkB,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAmC,GAAG;MAC1CC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD,OAAO5E,WAAW,CAAC6E,kBAAkB,CAAC,OAAO,EAAEP,OAAO,CAAC;EACzD,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACzE,QAAQ,CAAC0E,kBAAkB,EAAE,OAAO,EAAE;IAE3C,MAAMC,KAAK,GAAG,IAAI9E,IAAI,CAAC,CAAC;IACxB,MAAM+E,iBAAiB,GAAG,IAAI/E,IAAI,CAAC,CAAC;IACpC+E,iBAAiB,CAACC,OAAO,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;IAE/C,OAAOxD,MAAM,CAACyD,MAAM,CAACC,KAAK,IAAI;MAC5B,MAAMC,SAAS,GAAG,IAAIpF,IAAI,CAACmF,KAAK,CAACE,UAAU,CAAC;MAC5C,MAAMC,eAAe,GAAGnF,QAAQ,CAACoF,eAAe,CAAC/C,MAAM,KAAK,CAAC,IAC1D2C,KAAK,CAACK,WAAW,IAAIrF,QAAQ,CAACoF,eAAe,CAACE,QAAQ,CAACN,KAAK,CAACK,WAAW,CAAE;MAC7E,OAAOJ,SAAS,IAAIN,KAAK,IAAIM,SAAS,IAAIL,iBAAiB,IAAII,KAAK,CAACO,SAAS,IAAIJ,eAAe;IACnG,CAAC,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI7F,IAAI,CAAC4F,CAAC,CAACP,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,GAAG,IAAI9F,IAAI,CAAC6F,CAAC,CAACR,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CACnFC,KAAK,CAAC,CAAC,EAAE5F,QAAQ,CAAC6F,SAAS,CAAC;EACjC,CAAC;;EAED;EACA,MAAMzD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAM0D,MAAyB,GAAG,EAAE;;IAEpC;IACA,IAAIrF,aAAa,IAAIA,aAAa,CAAC4B,MAAM,GAAG,CAAC,EAAE;MAC7C5B,aAAa,CAACqC,OAAO,CAAC,CAACiD,YAAY,EAAEC,KAAK,KACxCF,MAAM,CAACG,IAAI,cACT3G,OAAA,CAACJ,cAAc;QAEb6G,YAAY,EAAEA;MAAa,GADtB,gBAAgBA,YAAY,CAACG,eAAe,IAAIpG,UAAU,EAAE;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElE,CACH,CACD,CAAC;IACJ;;IAEA;IACA,MAAMC,cAAc,GAAG9B,iBAAiB,CAAC,CAAC;IAC1C,IAAI8B,cAAc,CAAClE,MAAM,GAAG,CAAC,EAAE;MAC7BkE,cAAc,CAACzD,OAAO,CAAC,CAACkC,KAAK,EAAEgB,KAAK,KAClCF,MAAM,CAACG,IAAI,cACT3G,OAAA,CAACH,eAAe;QAEd6F,KAAK,EAAEA;MAAM,GADR,SAASA,KAAK,CAACwB,WAAW,IAAI1G,UAAU,EAAE;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhD,CACH,CACD,CAAC;IACJ;IAEA,OAAOR,MAAM;EACf,CAAC;EAED,MAAMA,MAAM,GAAG1D,kBAAkB,CAAC,CAAC;EACnC,MAAMqE,SAAS,GAAG9F,oBAAoB,IAAIY,aAAa;EACvD,MAAMmF,QAAQ,GAAG7F,kBAAkB,IAAIW,WAAW;EAElD,oBACElC,OAAA;IAAKqH,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzBtH,OAAA;MAAQqH,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC3BtH,OAAA;QAAAsH,QAAA,EAAI;MAAqB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BhH,OAAA;QAAKqH,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAqC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGThH,OAAA;MAAKqH,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB5C,cAAc,CAAC;IAAC;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGNhH,OAAA;MAAMqH,SAAS,EAAC,YAAY;MAAAC,QAAA,GAEzBH,SAAS,iBACRnH,OAAA;QAAKqH,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtH,OAAA;UAAKqH,SAAS,EAAC;QAAoB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1ChH,OAAA;UAAAsH,QAAA,EAAK;QAA0C;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CACN,EAGAI,QAAQ,IAAI,CAACD,SAAS,iBACrBnH,OAAA;QAAKqH,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvBtH,OAAA;UAAKuH,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAH,QAAA,EAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChEhH,OAAA;UAAAsH,QAAA,EAAK;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjChH,OAAA;UAAKuH,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEE,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAL,QAAA,EAAC;QAEnE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACG,SAAS,IAAI,CAACC,QAAQ,iBACtBpH,OAAA,CAAAE,SAAA;QAAAoH,QAAA,EACGd,MAAM,CAACzD,MAAM,GAAG,CAAC,gBAChB/C,OAAA,CAACF,WAAW;UACV8H,gBAAgB,EAAE,KAAM,CAAC;UAAA;UACzBC,YAAY,EAAE,IAAK;UAAAP,QAAA,EAElBd;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,gBAEdhH,OAAA;UAAKqH,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BtH,OAAA;YAAKuH,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAH,QAAA,EAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChEhH,OAAA;YAAAsH,QAAA,EAAK;UAAqC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAChDhH,OAAA;YAAKuH,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEE,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAAL,QAAA,EAAC;UAEnE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN,gBACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGPhH,OAAA;MAAKuH,KAAK,EAAE;QACVO,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,oBAAoB;QAChCC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,eAAe;QACxBC,YAAY,EAAE,MAAM;QACpBZ,QAAQ,EAAE,QAAQ;QAClBa,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAlB,QAAA,gBACAtH,OAAA;QAAAsH,QAAA,EAAM;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACfhH,OAAA;QAAAsH,QAAA,EAAM;MAAmB;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGNhH,OAAA;MAAMyI,SAAS,EAAC,SAAS;MAACC,OAAO,EAAC;IAAK;MAAA7B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAAC5G,EAAA,CAvRID,SAAmB;EAAA,QAiBnBV,gBAAgB,EAchBC,WAAW;AAAA;AAAAiJ,EAAA,GA/BXxI,SAAmB;AAyRzB,eAAeA,SAAS;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}