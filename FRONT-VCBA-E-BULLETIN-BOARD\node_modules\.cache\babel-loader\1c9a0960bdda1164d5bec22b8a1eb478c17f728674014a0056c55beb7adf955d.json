{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\admin\\\\tv-control\\\\TVStatusMonitor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Monitor, Wifi, WifiOff, Clock, BarChart3, ExternalLink } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVStatusMonitor = ({\n  status,\n  settings\n}) => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n  const formatUptime = uptime => {\n    // Simple uptime formatting - in a real app, this would be calculated properly\n    return uptime || '0m';\n  };\n  const formatLastRefresh = timestamp => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    if (diffMins < 1) return 'Just now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    const diffHours = Math.floor(diffMins / 60);\n    if (diffHours < 24) return `${diffHours}h ago`;\n    return date.toLocaleDateString();\n  };\n  const openTVDisplay = () => {\n    window.open('/tv-display', '_blank', 'fullscreen=yes');\n  };\n  const statusCardStyle = {\n    background: 'white',\n    border: '1px solid #e9ecef',\n    borderRadius: '12px',\n    padding: '1.5rem',\n    textAlign: 'center'\n  };\n  const metricCardStyle = {\n    background: '#f8f9fa',\n    border: '1px solid #e9ecef',\n    borderRadius: '8px',\n    padding: '1rem'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          margin: 0,\n          color: '#2c3e50',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), \"Status Monitor\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: openTVDisplay,\n        style: {\n          background: '#17a2b8',\n          color: 'white',\n          border: 'none',\n          padding: '0.75rem 1.5rem',\n          borderRadius: '6px',\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), \"View TV Display\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...statusCardStyle,\n          borderColor: status.isOnline ? '#28a745' : '#dc3545',\n          borderWidth: '2px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            marginBottom: '1rem'\n          },\n          children: status.isOnline ? /*#__PURE__*/_jsxDEV(Wifi, {\n            size: 48,\n            color: \"#28a745\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 32\n          }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n            size: 48,\n            color: \"#dc3545\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 69\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: status.isOnline ? '#28a745' : '#dc3545',\n            fontSize: '1.2rem'\n          },\n          children: status.isOnline ? 'Online' : 'Offline'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          },\n          children: \"TV Display Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: statusCardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            marginBottom: '1rem',\n            color: status.isPlaying ? '#28a745' : '#ffc107'\n          },\n          children: status.isPlaying ? '▶️' : '⏸️'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#2c3e50',\n            fontSize: '1.2rem'\n          },\n          children: status.isPlaying ? 'Playing' : 'Paused'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          },\n          children: \"Slideshow Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: statusCardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2.5rem',\n            fontWeight: 'bold',\n            marginBottom: '1rem',\n            color: '#3498db'\n          },\n          children: [status.currentSlide + 1, \"/\", status.totalSlides]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#2c3e50',\n            fontSize: '1.2rem'\n          },\n          children: \"Current Slide\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          },\n          children: \"Slide Position\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: statusCardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2.5rem',\n            fontWeight: 'bold',\n            marginBottom: '1rem',\n            color: '#17a2b8'\n          },\n          children: status.connectedDevices\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#2c3e50',\n            fontSize: '1.2rem'\n          },\n          children: \"Connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          },\n          children: \"TV Displays\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: '1fr 1fr',\n        gap: '2rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          border: '1px solid #e9ecef',\n          borderRadius: '12px',\n          padding: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Monitor, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), \"System Information\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Uptime\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#28a745',\n                  fontWeight: '600'\n                },\n                children: formatUptime(status.uptime)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Last Refresh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#17a2b8',\n                  fontWeight: '600'\n                },\n                children: formatLastRefresh(status.lastRefresh)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Auto-Play\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: settings.autoPlay ? '#28a745' : '#dc3545',\n                  fontWeight: '600'\n                },\n                children: settings.autoPlay ? 'Enabled' : 'Disabled'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Slide Interval\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#6f42c1',\n                  fontWeight: '600'\n                },\n                children: [settings.slideInterval / 1000, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          border: '1px solid #e9ecef',\n          borderRadius: '12px',\n          padding: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 1.5rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), \"Content Statistics\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Announcements\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#3498db',\n                  fontWeight: '600'\n                },\n                children: settings.showAnnouncements ? `Max ${settings.maxAnnouncements}` : 'Disabled'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Calendar Events\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#e74c3c',\n                  fontWeight: '600'\n                },\n                children: settings.showCalendarEvents ? `Max ${settings.maxEvents}` : 'Disabled'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Emergency Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: settings.emergencyActive ? '#dc3545' : '#28a745',\n                  fontWeight: '600'\n                },\n                children: settings.emergencyActive ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: metricCardStyle,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500'\n                },\n                children: \"Transition Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#6f42c1',\n                  fontWeight: '600'\n                },\n                children: settings.transitionType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#2c3e50',\n        color: 'white',\n        borderRadius: '12px',\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '1rem',\n          marginBottom: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Clock, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            fontSize: '1.2rem'\n          },\n          children: \"Current Time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '2.5rem',\n          fontWeight: 'bold',\n          fontFamily: 'monospace'\n        },\n        children: currentTime.toLocaleTimeString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '1.2rem',\n          opacity: 0.8,\n          marginTop: '0.5rem'\n        },\n        children: currentTime.toLocaleDateString('en-US', {\n          weekday: 'long',\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric'\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(TVStatusMonitor, \"f3elDUct5ap4W3FuLtGG73aMsLc=\");\n_c = TVStatusMonitor;\nexport default TVStatusMonitor;\nvar _c;\n$RefreshReg$(_c, \"TVStatusMonitor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Monitor", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "BarChart3", "ExternalLink", "jsxDEV", "_jsxDEV", "TVStatusMonitor", "status", "settings", "_s", "currentTime", "setCurrentTime", "Date", "timer", "setInterval", "clearInterval", "formatUptime", "uptime", "formatLastRefresh", "timestamp", "date", "now", "diffMs", "getTime", "diffMins", "Math", "floor", "diffHours", "toLocaleDateString", "openTVDisplay", "window", "open", "statusCardStyle", "background", "border", "borderRadius", "padding", "textAlign", "metricCardStyle", "children", "style", "display", "justifyContent", "alignItems", "marginBottom", "fontSize", "fontWeight", "margin", "color", "gap", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "cursor", "gridTemplateColumns", "borderColor", "isOnline", "borderWidth", "isPlaying", "currentSlide", "totalSlides", "connectedDevices", "flexDirection", "lastRefresh", "autoPlay", "slideInterval", "showAnnouncements", "maxAnnouncements", "showCalendarEvents", "maxEvents", "emergencyActive", "transitionType", "fontFamily", "toLocaleTimeString", "opacity", "marginTop", "weekday", "year", "month", "day", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/admin/tv-control/TVStatusMonitor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { TVDisplayStatus, TVDisplaySettings } from '../../../services/tvControlService';\nimport { Monitor, Wifi, WifiOff, Clock, BarChart3, RefreshCw, ExternalLink } from 'lucide-react';\n\ninterface TVStatusMonitorProps {\n  status: TVDisplayStatus;\n  settings: TVDisplaySettings;\n}\n\nconst TVStatusMonitor: React.FC<TVStatusMonitorProps> = ({ status, settings }) => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const formatUptime = (uptime: string) => {\n    // Simple uptime formatting - in a real app, this would be calculated properly\n    return uptime || '0m';\n  };\n\n  const formatLastRefresh = (timestamp: string) => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffMins = Math.floor(diffMs / 60000);\n    \n    if (diffMins < 1) return 'Just now';\n    if (diffMins < 60) return `${diffMins}m ago`;\n    const diffHours = Math.floor(diffMins / 60);\n    if (diffHours < 24) return `${diffHours}h ago`;\n    return date.toLocaleDateString();\n  };\n\n  const openTVDisplay = () => {\n    window.open('/tv-display', '_blank', 'fullscreen=yes');\n  };\n\n  const statusCardStyle = {\n    background: 'white',\n    border: '1px solid #e9ecef',\n    borderRadius: '12px',\n    padding: '1.5rem',\n    textAlign: 'center' as const\n  };\n\n  const metricCardStyle = {\n    background: '#f8f9fa',\n    border: '1px solid #e9ecef',\n    borderRadius: '8px',\n    padding: '1rem'\n  };\n\n  return (\n    <div>\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '2rem'\n      }}>\n        <h2 style={{\n          fontSize: '1.8rem',\n          fontWeight: '600',\n          margin: 0,\n          color: '#2c3e50',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        }}>\n          <BarChart3 size={24} />\n          Status Monitor\n        </h2>\n\n        <button\n          onClick={openTVDisplay}\n          style={{\n            background: '#17a2b8',\n            color: 'white',\n            border: 'none',\n            padding: '0.75rem 1.5rem',\n            borderRadius: '6px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}\n        >\n          <ExternalLink size={16} />\n          View TV Display\n        </button>\n      </div>\n\n      {/* Status Overview */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1.5rem',\n        marginBottom: '2rem'\n      }}>\n        {/* Connection Status */}\n        <div style={{\n          ...statusCardStyle,\n          borderColor: status.isOnline ? '#28a745' : '#dc3545',\n          borderWidth: '2px'\n        }}>\n          <div style={{\n            fontSize: '3rem',\n            marginBottom: '1rem'\n          }}>\n            {status.isOnline ? <Wifi size={48} color=\"#28a745\" /> : <WifiOff size={48} color=\"#dc3545\" />}\n          </div>\n          <h3 style={{\n            margin: '0 0 0.5rem 0',\n            color: status.isOnline ? '#28a745' : '#dc3545',\n            fontSize: '1.2rem'\n          }}>\n            {status.isOnline ? 'Online' : 'Offline'}\n          </h3>\n          <p style={{\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          }}>\n            TV Display Status\n          </p>\n        </div>\n\n        {/* Playback Status */}\n        <div style={statusCardStyle}>\n          <div style={{\n            fontSize: '3rem',\n            marginBottom: '1rem',\n            color: status.isPlaying ? '#28a745' : '#ffc107'\n          }}>\n            {status.isPlaying ? '▶️' : '⏸️'}\n          </div>\n          <h3 style={{\n            margin: '0 0 0.5rem 0',\n            color: '#2c3e50',\n            fontSize: '1.2rem'\n          }}>\n            {status.isPlaying ? 'Playing' : 'Paused'}\n          </h3>\n          <p style={{\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          }}>\n            Slideshow Status\n          </p>\n        </div>\n\n        {/* Current Slide */}\n        <div style={statusCardStyle}>\n          <div style={{\n            fontSize: '2.5rem',\n            fontWeight: 'bold',\n            marginBottom: '1rem',\n            color: '#3498db'\n          }}>\n            {status.currentSlide + 1}/{status.totalSlides}\n          </div>\n          <h3 style={{\n            margin: '0 0 0.5rem 0',\n            color: '#2c3e50',\n            fontSize: '1.2rem'\n          }}>\n            Current Slide\n          </h3>\n          <p style={{\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          }}>\n            Slide Position\n          </p>\n        </div>\n\n        {/* Connected Devices */}\n        <div style={statusCardStyle}>\n          <div style={{\n            fontSize: '2.5rem',\n            fontWeight: 'bold',\n            marginBottom: '1rem',\n            color: '#17a2b8'\n          }}>\n            {status.connectedDevices}\n          </div>\n          <h3 style={{\n            margin: '0 0 0.5rem 0',\n            color: '#2c3e50',\n            fontSize: '1.2rem'\n          }}>\n            Connected\n          </h3>\n          <p style={{\n            margin: 0,\n            color: '#6c757d',\n            fontSize: '0.9rem'\n          }}>\n            TV Displays\n          </p>\n        </div>\n      </div>\n\n      {/* Detailed Metrics */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: '1fr 1fr',\n        gap: '2rem',\n        marginBottom: '2rem'\n      }}>\n        {/* System Information */}\n        <div style={{\n          background: 'white',\n          border: '1px solid #e9ecef',\n          borderRadius: '12px',\n          padding: '1.5rem'\n        }}>\n          <h3 style={{\n            margin: '0 0 1.5rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}>\n            <Monitor size={20} />\n            System Information\n          </h3>\n\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Uptime</span>\n                <span style={{ color: '#28a745', fontWeight: '600' }}>\n                  {formatUptime(status.uptime)}\n                </span>\n              </div>\n            </div>\n\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Last Refresh</span>\n                <span style={{ color: '#17a2b8', fontWeight: '600' }}>\n                  {formatLastRefresh(status.lastRefresh)}\n                </span>\n              </div>\n            </div>\n\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Auto-Play</span>\n                <span style={{\n                  color: settings.autoPlay ? '#28a745' : '#dc3545',\n                  fontWeight: '600'\n                }}>\n                  {settings.autoPlay ? 'Enabled' : 'Disabled'}\n                </span>\n              </div>\n            </div>\n\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Slide Interval</span>\n                <span style={{ color: '#6f42c1', fontWeight: '600' }}>\n                  {settings.slideInterval / 1000}s\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Content Statistics */}\n        <div style={{\n          background: 'white',\n          border: '1px solid #e9ecef',\n          borderRadius: '12px',\n          padding: '1.5rem'\n        }}>\n          <h3 style={{\n            margin: '0 0 1.5rem 0',\n            color: '#2c3e50',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          }}>\n            <BarChart3 size={20} />\n            Content Statistics\n          </h3>\n\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Announcements</span>\n                <span style={{ color: '#3498db', fontWeight: '600' }}>\n                  {settings.showAnnouncements ? `Max ${settings.maxAnnouncements}` : 'Disabled'}\n                </span>\n              </div>\n            </div>\n\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Calendar Events</span>\n                <span style={{ color: '#e74c3c', fontWeight: '600' }}>\n                  {settings.showCalendarEvents ? `Max ${settings.maxEvents}` : 'Disabled'}\n                </span>\n              </div>\n            </div>\n\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Emergency Active</span>\n                <span style={{\n                  color: settings.emergencyActive ? '#dc3545' : '#28a745',\n                  fontWeight: '600'\n                }}>\n                  {settings.emergencyActive ? 'Yes' : 'No'}\n                </span>\n              </div>\n            </div>\n\n            <div style={metricCardStyle}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              }}>\n                <span style={{ fontWeight: '500' }}>Transition Type</span>\n                <span style={{ color: '#6f42c1', fontWeight: '600' }}>\n                  {settings.transitionType}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Real-time Clock */}\n      <div style={{\n        background: '#2c3e50',\n        color: 'white',\n        borderRadius: '12px',\n        padding: '2rem',\n        textAlign: 'center'\n      }}>\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '1rem',\n          marginBottom: '1rem'\n        }}>\n          <Clock size={24} />\n          <h3 style={{ margin: 0, fontSize: '1.2rem' }}>Current Time</h3>\n        </div>\n        <div style={{\n          fontSize: '2.5rem',\n          fontWeight: 'bold',\n          fontFamily: 'monospace'\n        }}>\n          {currentTime.toLocaleTimeString()}\n        </div>\n        <div style={{\n          fontSize: '1.2rem',\n          opacity: 0.8,\n          marginTop: '0.5rem'\n        }}>\n          {currentTime.toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n          })}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TVStatusMonitor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAaC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOjG,MAAMC,eAA+C,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,IAAIgB,IAAI,CAAC,CAAC,CAAC;EAE1Df,SAAS,CAAC,MAAM;IACd,MAAMgB,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BH,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,YAAY,GAAIC,MAAc,IAAK;IACvC;IACA,OAAOA,MAAM,IAAI,IAAI;EACvB,CAAC;EAED,MAAMC,iBAAiB,GAAIC,SAAiB,IAAK;IAC/C,MAAMC,IAAI,GAAG,IAAIR,IAAI,CAACO,SAAS,CAAC;IAChC,MAAME,GAAG,GAAG,IAAIT,IAAI,CAAC,CAAC;IACtB,MAAMU,MAAM,GAAGD,GAAG,CAACE,OAAO,CAAC,CAAC,GAAGH,IAAI,CAACG,OAAO,CAAC,CAAC;IAC7C,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,KAAK,CAAC;IAE3C,IAAIE,QAAQ,GAAG,CAAC,EAAE,OAAO,UAAU;IACnC,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGA,QAAQ,OAAO;IAC5C,MAAMG,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC;IAC3C,IAAIG,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,OAAO;IAC9C,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BC,MAAM,CAACC,IAAI,CAAC,aAAa,EAAE,QAAQ,EAAE,gBAAgB,CAAC;EACxD,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBC,UAAU,EAAE,OAAO;IACnBC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBL,UAAU,EAAE,SAAS;IACrBC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE;EACX,CAAC;EAED,oBACE/B,OAAA;IAAAkC,QAAA,gBACElC,OAAA;MAAKmC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;MAChB,CAAE;MAAAL,QAAA,gBACAlC,OAAA;QAAImC,KAAK,EAAE;UACTK,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE,SAAS;UAChBP,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBM,GAAG,EAAE;QACP,CAAE;QAAAV,QAAA,gBACAlC,OAAA,CAACH,SAAS;UAACgD,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELjD,OAAA;QACEkD,OAAO,EAAE1B,aAAc;QACvBW,KAAK,EAAE;UACLP,UAAU,EAAE,SAAS;UACrBe,KAAK,EAAE,OAAO;UACdd,MAAM,EAAE,MAAM;UACdE,OAAO,EAAE,gBAAgB;UACzBD,YAAY,EAAE,KAAK;UACnBqB,MAAM,EAAE,SAAS;UACjBf,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBM,GAAG,EAAE;QACP,CAAE;QAAAV,QAAA,gBAEFlC,OAAA,CAACF,YAAY;UAAC+C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,mBAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNjD,OAAA;MAAKmC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfgB,mBAAmB,EAAE,sCAAsC;QAC3DR,GAAG,EAAE,QAAQ;QACbL,YAAY,EAAE;MAChB,CAAE;MAAAL,QAAA,gBAEAlC,OAAA;QAAKmC,KAAK,EAAE;UACV,GAAGR,eAAe;UAClB0B,WAAW,EAAEnD,MAAM,CAACoD,QAAQ,GAAG,SAAS,GAAG,SAAS;UACpDC,WAAW,EAAE;QACf,CAAE;QAAArB,QAAA,gBACAlC,OAAA;UAAKmC,KAAK,EAAE;YACVK,QAAQ,EAAE,MAAM;YAChBD,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EACChC,MAAM,CAACoD,QAAQ,gBAAGtD,OAAA,CAACN,IAAI;YAACmD,IAAI,EAAE,EAAG;YAACF,KAAK,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGjD,OAAA,CAACL,OAAO;YAACkD,IAAI,EAAE,EAAG;YAACF,KAAK,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC,eACNjD,OAAA;UAAImC,KAAK,EAAE;YACTO,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAEzC,MAAM,CAACoD,QAAQ,GAAG,SAAS,GAAG,SAAS;YAC9Cd,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EACChC,MAAM,CAACoD,QAAQ,GAAG,QAAQ,GAAG;QAAS;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACLjD,OAAA;UAAGmC,KAAK,EAAE;YACRO,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNjD,OAAA;QAAKmC,KAAK,EAAER,eAAgB;QAAAO,QAAA,gBAC1BlC,OAAA;UAAKmC,KAAK,EAAE;YACVK,QAAQ,EAAE,MAAM;YAChBD,YAAY,EAAE,MAAM;YACpBI,KAAK,EAAEzC,MAAM,CAACsD,SAAS,GAAG,SAAS,GAAG;UACxC,CAAE;UAAAtB,QAAA,EACChC,MAAM,CAACsD,SAAS,GAAG,IAAI,GAAG;QAAI;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNjD,OAAA;UAAImC,KAAK,EAAE;YACTO,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EACChC,MAAM,CAACsD,SAAS,GAAG,SAAS,GAAG;QAAQ;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACLjD,OAAA;UAAGmC,KAAK,EAAE;YACRO,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNjD,OAAA;QAAKmC,KAAK,EAAER,eAAgB;QAAAO,QAAA,gBAC1BlC,OAAA;UAAKmC,KAAK,EAAE;YACVK,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,MAAM;YAClBF,YAAY,EAAE,MAAM;YACpBI,KAAK,EAAE;UACT,CAAE;UAAAT,QAAA,GACChC,MAAM,CAACuD,YAAY,GAAG,CAAC,EAAC,GAAC,EAACvD,MAAM,CAACwD,WAAW;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNjD,OAAA;UAAImC,KAAK,EAAE;YACTO,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAGmC,KAAK,EAAE;YACRO,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNjD,OAAA;QAAKmC,KAAK,EAAER,eAAgB;QAAAO,QAAA,gBAC1BlC,OAAA;UAAKmC,KAAK,EAAE;YACVK,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,MAAM;YAClBF,YAAY,EAAE,MAAM;YACpBI,KAAK,EAAE;UACT,CAAE;UAAAT,QAAA,EACChC,MAAM,CAACyD;QAAgB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACNjD,OAAA;UAAImC,KAAK,EAAE;YACTO,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAGmC,KAAK,EAAE;YACRO,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE,SAAS;YAChBH,QAAQ,EAAE;UACZ,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjD,OAAA;MAAKmC,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfgB,mBAAmB,EAAE,SAAS;QAC9BR,GAAG,EAAE,MAAM;QACXL,YAAY,EAAE;MAChB,CAAE;MAAAL,QAAA,gBAEAlC,OAAA;QAAKmC,KAAK,EAAE;UACVP,UAAU,EAAE,OAAO;UACnBC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE;QACX,CAAE;QAAAG,QAAA,gBACAlC,OAAA;UAAImC,KAAK,EAAE;YACTO,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBP,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBM,GAAG,EAAE;UACP,CAAE;UAAAV,QAAA,gBACAlC,OAAA,CAACP,OAAO;YAACoD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEvB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELjD,OAAA;UAAKmC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEwB,aAAa,EAAE,QAAQ;YAAEhB,GAAG,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACpElC,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAM;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDjD,OAAA;gBAAMmC,KAAK,EAAE;kBAAEQ,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAClDvB,YAAY,CAACT,MAAM,CAACU,MAAM;cAAC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAY;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDjD,OAAA;gBAAMmC,KAAK,EAAE;kBAAEQ,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAClDrB,iBAAiB,CAACX,MAAM,CAAC2D,WAAW;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpDjD,OAAA;gBAAMmC,KAAK,EAAE;kBACXQ,KAAK,EAAExC,QAAQ,CAAC2D,QAAQ,GAAG,SAAS,GAAG,SAAS;kBAChDrB,UAAU,EAAE;gBACd,CAAE;gBAAAP,QAAA,EACC/B,QAAQ,CAAC2D,QAAQ,GAAG,SAAS,GAAG;cAAU;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAc;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDjD,OAAA;gBAAMmC,KAAK,EAAE;kBAAEQ,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,GAClD/B,QAAQ,CAAC4D,aAAa,GAAG,IAAI,EAAC,GACjC;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA;QAAKmC,KAAK,EAAE;UACVP,UAAU,EAAE,OAAO;UACnBC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE;QACX,CAAE;QAAAG,QAAA,gBACAlC,OAAA;UAAImC,KAAK,EAAE;YACTO,MAAM,EAAE,cAAc;YACtBC,KAAK,EAAE,SAAS;YAChBP,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBM,GAAG,EAAE;UACP,CAAE;UAAAV,QAAA,gBACAlC,OAAA,CAACH,SAAS;YAACgD,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELjD,OAAA;UAAKmC,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEwB,aAAa,EAAE,QAAQ;YAAEhB,GAAG,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACpElC,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAa;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDjD,OAAA;gBAAMmC,KAAK,EAAE;kBAAEQ,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAClD/B,QAAQ,CAAC6D,iBAAiB,GAAG,OAAO7D,QAAQ,CAAC8D,gBAAgB,EAAE,GAAG;cAAU;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAe;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DjD,OAAA;gBAAMmC,KAAK,EAAE;kBAAEQ,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAClD/B,QAAQ,CAAC+D,kBAAkB,GAAG,OAAO/D,QAAQ,CAACgE,SAAS,EAAE,GAAG;cAAU;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAgB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DjD,OAAA;gBAAMmC,KAAK,EAAE;kBACXQ,KAAK,EAAExC,QAAQ,CAACiE,eAAe,GAAG,SAAS,GAAG,SAAS;kBACvD3B,UAAU,EAAE;gBACd,CAAE;gBAAAP,QAAA,EACC/B,QAAQ,CAACiE,eAAe,GAAG,KAAK,GAAG;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjD,OAAA;YAAKmC,KAAK,EAAEF,eAAgB;YAAAC,QAAA,eAC1BlC,OAAA;cAAKmC,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,gBACAlC,OAAA;gBAAMmC,KAAK,EAAE;kBAAEM,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAAC;cAAe;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DjD,OAAA;gBAAMmC,KAAK,EAAE;kBAAEQ,KAAK,EAAE,SAAS;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAP,QAAA,EAClD/B,QAAQ,CAACkE;cAAc;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjD,OAAA;MAAKmC,KAAK,EAAE;QACVP,UAAU,EAAE,SAAS;QACrBe,KAAK,EAAE,OAAO;QACdb,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE;MACb,CAAE;MAAAE,QAAA,gBACAlC,OAAA;QAAKmC,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBD,cAAc,EAAE,QAAQ;UACxBO,GAAG,EAAE,MAAM;UACXL,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACAlC,OAAA,CAACJ,KAAK;UAACiD,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnBjD,OAAA;UAAImC,KAAK,EAAE;YAAEO,MAAM,EAAE,CAAC;YAAEF,QAAQ,EAAE;UAAS,CAAE;UAAAN,QAAA,EAAC;QAAY;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNjD,OAAA;QAAKmC,KAAK,EAAE;UACVK,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,MAAM;UAClB6B,UAAU,EAAE;QACd,CAAE;QAAApC,QAAA,EACC7B,WAAW,CAACkE,kBAAkB,CAAC;MAAC;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNjD,OAAA;QAAKmC,KAAK,EAAE;UACVK,QAAQ,EAAE,QAAQ;UAClBgC,OAAO,EAAE,GAAG;UACZC,SAAS,EAAE;QACb,CAAE;QAAAvC,QAAA,EACC7B,WAAW,CAACkB,kBAAkB,CAAC,OAAO,EAAE;UACvCmD,OAAO,EAAE,MAAM;UACfC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,MAAM;UACbC,GAAG,EAAE;QACP,CAAC;MAAC;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAjZIH,eAA+C;AAAA6E,EAAA,GAA/C7E,eAA+C;AAmZrD,eAAeA,eAAe;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}