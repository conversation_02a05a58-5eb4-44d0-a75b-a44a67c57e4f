{"ast": null, "code": "var _jsxFileName = \"D:\\\\online-e-bulletin-reactjs\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\tv\\\\TVCalendarEvent.tsx\";\nimport React from 'react';\nimport '../../styles/tv.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TVCalendarEvent = ({\n  event\n}) => {\n  // Format the date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Default red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // Truncate description if too long\n  const truncateDescription = (description, maxLength = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get event images (placeholder for future implementation)\n  const getEventImages = () => {\n    // Note: Calendar events don't currently have image attachments in the schema\n    // This is prepared for future enhancement\n    return [];\n  };\n  const images = getEventImages();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tv-calendar-event\",\n    style: {\n      borderLeftColor: getCategoryColor(),\n      background: isUpcoming() ? 'rgba(255, 255, 255, 0.98)' : 'rgba(255, 255, 255, 0.95)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: '3rem'\n        },\n        children: getEventIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), event.is_holiday && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          background: '#f39c12',\n          color: 'white',\n          padding: '0.5rem 1rem',\n          borderRadius: '15px',\n          fontSize: '1.4rem',\n          fontWeight: '600'\n        },\n        children: \"HOLIDAY\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), event.is_alert && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          background: '#e74c3c',\n          color: 'white',\n          padding: '0.5rem 1rem',\n          borderRadius: '15px',\n          fontSize: '1.4rem',\n          fontWeight: '600'\n        },\n        children: \"IMPORTANT\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"tv-event-title\",\n      children: event.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-date\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\uD83D\\uDCC5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: formatDateRange()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: '500',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d'\n          },\n          children: getDaysUntilEvent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        display: 'grid',\n        gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '1rem',\n        maxHeight: '300px'\n      },\n      children: images.slice(0, 2).map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderRadius: '12px',\n          overflow: 'hidden',\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: image.url,\n          alt: image.alt,\n          style: {\n            width: '100%',\n            height: '200px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          onError: e => {\n            e.currentTarget.style.display = 'none';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 9\n    }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-description\",\n      children: truncateDescription(event.description)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tv-event-meta\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '2rem'\n        },\n        children: [event.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: getCategoryColor(),\n            color: 'white',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            fontWeight: '600',\n            fontSize: '1.6rem'\n          },\n          children: event.category_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), event.subcategory_name && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            background: event.subcategory_color || '#95a5a6',\n            color: 'white',\n            padding: '0.6rem 1.2rem',\n            borderRadius: '20px',\n            fontSize: '1.4rem'\n          },\n          children: event.subcategory_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), event.is_recurring && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem',\n            fontSize: '1.6rem',\n            color: '#8e44ad'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [event.recurrence_pattern === 'yearly' && 'Yearly', event.recurrence_pattern === 'monthly' && 'Monthly', event.recurrence_pattern === 'weekly' && 'Weekly']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), event.created_by_name && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          fontSize: '1.6rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            opacity: 0.7\n          },\n          children: \"Organized by:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: '600'\n          },\n          children: event.created_by_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), (event.reaction_count || event.comment_count) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1.5rem',\n        background: 'rgba(0, 0, 0, 0.05)',\n        borderRadius: '10px',\n        display: 'flex',\n        gap: '3rem',\n        fontSize: '1.8rem'\n      },\n      children: [event.reaction_count && event.reaction_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2764\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.reaction_count, \" reactions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 13\n      }, this), event.comment_count && event.comment_count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '0.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [event.comment_count, \" comments\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_c = TVCalendarEvent;\nexport default TVCalendarEvent;\nvar _c;\n$RefreshReg$(_c, \"TVCalendarEvent\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TVCalendarEvent", "event", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "formatDateRange", "startDate", "event_date", "end_date", "endDate", "startFormatted", "endFormatted", "getDaysUntilEvent", "today", "eventDate", "diffTime", "getTime", "diffDays", "Math", "ceil", "abs", "getCategoryColor", "category_color", "getEventIcon", "is_holiday", "is_recurring", "is_alert", "isUpcoming", "truncateDescription", "description", "max<PERSON><PERSON><PERSON>", "length", "substring", "trim", "getEventImages", "images", "className", "style", "borderLeftColor", "background", "children", "display", "alignItems", "gap", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "padding", "borderRadius", "fontWeight", "title", "flexDirection", "gridTemplateColumns", "maxHeight", "slice", "map", "image", "index", "overflow", "boxShadow", "src", "url", "alt", "width", "height", "objectFit", "onError", "e", "currentTarget", "category_name", "subcategory_name", "subcategory_color", "recurrence_pattern", "created_by_name", "opacity", "reaction_count", "comment_count", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/components/tv/TVCalendarEvent.tsx"], "sourcesContent": ["import React from 'react';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl } from '../../config/constants';\nimport '../../styles/tv.css';\n\ninterface TVCalendarEventProps {\n  event: CalendarEvent;\n}\n\nconst TVCalendarEvent: React.FC<TVCalendarEventProps> = ({ event }) => {\n  // Format the date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  // Format date range if end date exists\n  const formatDateRange = () => {\n    const startDate = new Date(event.event_date);\n    \n    if (event.end_date) {\n      const endDate = new Date(event.end_date);\n      const startFormatted = startDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n      const endFormatted = endDate.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n      });\n      return `${startFormatted} - ${endFormatted}`;\n    }\n    \n    return formatDate(event.event_date);\n  };\n\n  // Calculate days until event\n  const getDaysUntilEvent = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffTime = eventDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) return 'Today';\n    if (diffDays === 1) return 'Tomorrow';\n    if (diffDays > 0) return `In ${diffDays} days`;\n    if (diffDays === -1) return 'Yesterday';\n    return `${Math.abs(diffDays)} days ago`;\n  };\n\n  // Get category color or default\n  const getCategoryColor = () => {\n    if (event.category_color) {\n      return event.category_color;\n    }\n    return '#e74c3c'; // Default red for events\n  };\n\n  // Determine event type icon\n  const getEventIcon = () => {\n    if (event.is_holiday) return '🎉';\n    if (event.is_recurring) return '🔄';\n    if (event.is_alert) return '⚠️';\n    return '📅';\n  };\n\n  // Check if event is today or upcoming soon\n  const isUpcoming = () => {\n    const today = new Date();\n    const eventDate = new Date(event.event_date);\n    const diffDays = Math.ceil((eventDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n    return diffDays >= 0 && diffDays <= 7; // Next 7 days\n  };\n\n  // Truncate description if too long\n  const truncateDescription = (description: string, maxLength: number = 250) => {\n    if (!description || description.length <= maxLength) return description;\n    return description.substring(0, maxLength).trim() + '...';\n  };\n\n  // Get event images (placeholder for future implementation)\n  const getEventImages = () => {\n    // Note: Calendar events don't currently have image attachments in the schema\n    // This is prepared for future enhancement\n    return [];\n  };\n\n  const images = getEventImages();\n\n  return (\n    <div \n      className=\"tv-calendar-event\"\n      style={{\n        borderLeftColor: getCategoryColor(),\n        background: isUpcoming() ? 'rgba(255, 255, 255, 0.98)' : 'rgba(255, 255, 255, 0.95)'\n      }}\n    >\n      {/* Event type indicator */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        marginBottom: '1rem'\n      }}>\n        <span style={{ fontSize: '3rem' }}>{getEventIcon()}</span>\n        {event.is_holiday && (\n          <span style={{\n            background: '#f39c12',\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '15px',\n            fontSize: '1.4rem',\n            fontWeight: '600'\n          }}>\n            HOLIDAY\n          </span>\n        )}\n        {event.is_alert && (\n          <span style={{\n            background: '#e74c3c',\n            color: 'white',\n            padding: '0.5rem 1rem',\n            borderRadius: '15px',\n            fontSize: '1.4rem',\n            fontWeight: '600'\n          }}>\n            IMPORTANT\n          </span>\n        )}\n      </div>\n\n      {/* Event title */}\n      <h2 className=\"tv-event-title\">\n        {event.title}\n      </h2>\n\n      {/* Event date with countdown */}\n      <div className=\"tv-event-date\">\n        <span>📅</span>\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>\n          <span>{formatDateRange()}</span>\n          <span style={{ \n            fontSize: '2rem', \n            fontWeight: '500',\n            color: isUpcoming() ? '#27ae60' : '#7f8c8d'\n          }}>\n            {getDaysUntilEvent()}\n          </span>\n        </div>\n      </div>\n\n      {/* Event images (placeholder for future implementation) */}\n      {images.length > 0 && (\n        <div style={{\n          marginBottom: '2rem',\n          display: 'grid',\n          gridTemplateColumns: images.length === 1 ? '1fr' : 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem',\n          maxHeight: '300px'\n        }}>\n          {images.slice(0, 2).map((image: any, index: number) => (\n            <div\n              key={index}\n              style={{\n                borderRadius: '12px',\n                overflow: 'hidden',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}\n            >\n              <img\n                src={image.url}\n                alt={image.alt}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  objectFit: 'cover',\n                  display: 'block'\n                }}\n                onError={(e) => {\n                  e.currentTarget.style.display = 'none';\n                }}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Event description */}\n      {event.description && (\n        <div className=\"tv-event-description\">\n          {truncateDescription(event.description)}\n        </div>\n      )}\n\n      {/* Event metadata */}\n      <div className=\"tv-event-meta\">\n        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>\n          {/* Category */}\n          {event.category_name && (\n            <span \n              style={{\n                background: getCategoryColor(),\n                color: 'white',\n                padding: '0.8rem 1.5rem',\n                borderRadius: '25px',\n                fontWeight: '600',\n                fontSize: '1.6rem'\n              }}\n            >\n              {event.category_name}\n            </span>\n          )}\n\n          {/* Subcategory */}\n          {event.subcategory_name && (\n            <span style={{\n              background: event.subcategory_color || '#95a5a6',\n              color: 'white',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              fontSize: '1.4rem'\n            }}>\n              {event.subcategory_name}\n            </span>\n          )}\n\n          {/* Recurring indicator */}\n          {event.is_recurring && (\n            <div style={{ \n              display: 'flex', \n              alignItems: 'center', \n              gap: '0.5rem',\n              fontSize: '1.6rem',\n              color: '#8e44ad'\n            }}>\n              <span>🔄</span>\n              <span>\n                {event.recurrence_pattern === 'yearly' && 'Yearly'}\n                {event.recurrence_pattern === 'monthly' && 'Monthly'}\n                {event.recurrence_pattern === 'weekly' && 'Weekly'}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Created by information */}\n        {event.created_by_name && (\n          <div style={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: '1rem',\n            fontSize: '1.6rem'\n          }}>\n            <span style={{ opacity: 0.7 }}>Organized by:</span>\n            <span style={{ fontWeight: '600' }}>\n              {event.created_by_name}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Engagement indicators (if available) */}\n      {(event.reaction_count || event.comment_count) && (\n        <div style={{\n          marginTop: '2rem',\n          padding: '1.5rem',\n          background: 'rgba(0, 0, 0, 0.05)',\n          borderRadius: '10px',\n          display: 'flex',\n          gap: '3rem',\n          fontSize: '1.8rem'\n        }}>\n          {event.reaction_count && event.reaction_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>❤️</span>\n              <span>{event.reaction_count} reactions</span>\n            </div>\n          )}\n          {event.comment_count && event.comment_count > 0 && (\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <span>💬</span>\n              <span>{event.comment_count} comments</span>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TVCalendarEvent;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EACrE;EACA,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,IAAIP,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAE5C,IAAIZ,KAAK,CAACa,QAAQ,EAAE;MAClB,MAAMC,OAAO,GAAG,IAAIV,IAAI,CAACJ,KAAK,CAACa,QAAQ,CAAC;MACxC,MAAME,cAAc,GAAGJ,SAAS,CAACN,kBAAkB,CAAC,OAAO,EAAE;QAC3DG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;MACF,MAAMO,YAAY,GAAGF,OAAO,CAACT,kBAAkB,CAAC,OAAO,EAAE;QACvDG,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdF,IAAI,EAAE;MACR,CAAC,CAAC;MACF,OAAO,GAAGQ,cAAc,MAAMC,YAAY,EAAE;IAC9C;IAEA,OAAOf,UAAU,CAACD,KAAK,CAACY,UAAU,CAAC;EACrC,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAC5C,MAAMQ,QAAQ,GAAGD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,KAAK,CAAC,EAAE,OAAO,OAAO;IAClC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO,UAAU;IACrC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,MAAMA,QAAQ,OAAO;IAC9C,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW;IACvC,OAAO,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,WAAW;EACzC,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI1B,KAAK,CAAC2B,cAAc,EAAE;MACxB,OAAO3B,KAAK,CAAC2B,cAAc;IAC7B;IACA,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5B,KAAK,CAAC6B,UAAU,EAAE,OAAO,IAAI;IACjC,IAAI7B,KAAK,CAAC8B,YAAY,EAAE,OAAO,IAAI;IACnC,IAAI9B,KAAK,CAAC+B,QAAQ,EAAE,OAAO,IAAI;IAC/B,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMd,KAAK,GAAG,IAAId,IAAI,CAAC,CAAC;IACxB,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAACJ,KAAK,CAACY,UAAU,CAAC;IAC5C,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGH,KAAK,CAACG,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3F,OAAOC,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMW,mBAAmB,GAAGA,CAACC,WAAmB,EAAEC,SAAiB,GAAG,GAAG,KAAK;IAC5E,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,WAAW;IACvE,OAAOA,WAAW,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,KAAK;EAC3D,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,MAAM,GAAGD,cAAc,CAAC,CAAC;EAE/B,oBACEzC,OAAA;IACE2C,SAAS,EAAC,mBAAmB;IAC7BC,KAAK,EAAE;MACLC,eAAe,EAAEjB,gBAAgB,CAAC,CAAC;MACnCkB,UAAU,EAAEZ,UAAU,CAAC,CAAC,GAAG,2BAA2B,GAAG;IAC3D,CAAE;IAAAa,QAAA,gBAGF/C,OAAA;MAAK4C,KAAK,EAAE;QACVI,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAAJ,QAAA,gBACA/C,OAAA;QAAM4C,KAAK,EAAE;UAAEQ,QAAQ,EAAE;QAAO,CAAE;QAAAL,QAAA,EAAEjB,YAAY,CAAC;MAAC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzDtD,KAAK,CAAC6B,UAAU,iBACf/B,OAAA;QAAM4C,KAAK,EAAE;UACXE,UAAU,EAAE,SAAS;UACrBW,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,aAAa;UACtBC,YAAY,EAAE,MAAM;UACpBP,QAAQ,EAAE,QAAQ;UAClBQ,UAAU,EAAE;QACd,CAAE;QAAAb,QAAA,EAAC;MAEH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP,EACAtD,KAAK,CAAC+B,QAAQ,iBACbjC,OAAA;QAAM4C,KAAK,EAAE;UACXE,UAAU,EAAE,SAAS;UACrBW,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,aAAa;UACtBC,YAAY,EAAE,MAAM;UACpBP,QAAQ,EAAE,QAAQ;UAClBQ,UAAU,EAAE;QACd,CAAE;QAAAb,QAAA,EAAC;MAEH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNxD,OAAA;MAAI2C,SAAS,EAAC,gBAAgB;MAAAI,QAAA,EAC3B7C,KAAK,CAAC2D;IAAK;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGLxD,OAAA;MAAK2C,SAAS,EAAC,eAAe;MAAAI,QAAA,gBAC5B/C,OAAA;QAAA+C,QAAA,EAAM;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACfxD,OAAA;QAAK4C,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEc,aAAa,EAAE,QAAQ;UAAEZ,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACtE/C,OAAA;UAAA+C,QAAA,EAAOnC,eAAe,CAAC;QAAC;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChCxD,OAAA;UAAM4C,KAAK,EAAE;YACXQ,QAAQ,EAAE,MAAM;YAChBQ,UAAU,EAAE,KAAK;YACjBH,KAAK,EAAEvB,UAAU,CAAC,CAAC,GAAG,SAAS,GAAG;UACpC,CAAE;UAAAa,QAAA,EACC5B,iBAAiB,CAAC;QAAC;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLd,MAAM,CAACJ,MAAM,GAAG,CAAC,iBAChBtC,OAAA;MAAK4C,KAAK,EAAE;QACVO,YAAY,EAAE,MAAM;QACpBH,OAAO,EAAE,MAAM;QACfe,mBAAmB,EAAErB,MAAM,CAACJ,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,sCAAsC;QACzFY,GAAG,EAAE,MAAM;QACXc,SAAS,EAAE;MACb,CAAE;MAAAjB,QAAA,EACCL,MAAM,CAACuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,KAAU,EAAEC,KAAa,kBAChDpE,OAAA;QAEE4C,KAAK,EAAE;UACLe,YAAY,EAAE,MAAM;UACpBU,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE;QACb,CAAE;QAAAvB,QAAA,eAEF/C,OAAA;UACEuE,GAAG,EAAEJ,KAAK,CAACK,GAAI;UACfC,GAAG,EAAEN,KAAK,CAACM,GAAI;UACf7B,KAAK,EAAE;YACL8B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACfC,SAAS,EAAE,OAAO;YAClB5B,OAAO,EAAE;UACX,CAAE;UACF6B,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,aAAa,CAACnC,KAAK,CAACI,OAAO,GAAG,MAAM;UACxC;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAnBGY,KAAK;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAGAtD,KAAK,CAACkC,WAAW,iBAChBpC,OAAA;MAAK2C,SAAS,EAAC,sBAAsB;MAAAI,QAAA,EAClCZ,mBAAmB,CAACjC,KAAK,CAACkC,WAAW;IAAC;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACN,eAGDxD,OAAA;MAAK2C,SAAS,EAAC,eAAe;MAAAI,QAAA,gBAC5B/C,OAAA;QAAK4C,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAH,QAAA,GAEhE7C,KAAK,CAAC8E,aAAa,iBAClBhF,OAAA;UACE4C,KAAK,EAAE;YACLE,UAAU,EAAElB,gBAAgB,CAAC,CAAC;YAC9B6B,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,eAAe;YACxBC,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBR,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,EAED7C,KAAK,CAAC8E;QAAa;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACP,EAGAtD,KAAK,CAAC+E,gBAAgB,iBACrBjF,OAAA;UAAM4C,KAAK,EAAE;YACXE,UAAU,EAAE5C,KAAK,CAACgF,iBAAiB,IAAI,SAAS;YAChDzB,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,eAAe;YACxBC,YAAY,EAAE,MAAM;YACpBP,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,EACC7C,KAAK,CAAC+E;QAAgB;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP,EAGAtD,KAAK,CAAC8B,YAAY,iBACjBhC,OAAA;UAAK4C,KAAK,EAAE;YACVI,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,GAAG,EAAE,QAAQ;YACbE,QAAQ,EAAE,QAAQ;YAClBK,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,gBACA/C,OAAA;YAAA+C,QAAA,EAAM;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAA+C,QAAA,GACG7C,KAAK,CAACiF,kBAAkB,KAAK,QAAQ,IAAI,QAAQ,EACjDjF,KAAK,CAACiF,kBAAkB,KAAK,SAAS,IAAI,SAAS,EACnDjF,KAAK,CAACiF,kBAAkB,KAAK,QAAQ,IAAI,QAAQ;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLtD,KAAK,CAACkF,eAAe,iBACpBpF,OAAA;QAAK4C,KAAK,EAAE;UACVI,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXE,QAAQ,EAAE;QACZ,CAAE;QAAAL,QAAA,gBACA/C,OAAA;UAAM4C,KAAK,EAAE;YAAEyC,OAAO,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDxD,OAAA;UAAM4C,KAAK,EAAE;YAAEgB,UAAU,EAAE;UAAM,CAAE;UAAAb,QAAA,EAChC7C,KAAK,CAACkF;QAAe;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAACtD,KAAK,CAACoF,cAAc,IAAIpF,KAAK,CAACqF,aAAa,kBAC3CvF,OAAA;MAAK4C,KAAK,EAAE;QACV4C,SAAS,EAAE,MAAM;QACjB9B,OAAO,EAAE,QAAQ;QACjBZ,UAAU,EAAE,qBAAqB;QACjCa,YAAY,EAAE,MAAM;QACpBX,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,MAAM;QACXE,QAAQ,EAAE;MACZ,CAAE;MAAAL,QAAA,GACC7C,KAAK,CAACoF,cAAc,IAAIpF,KAAK,CAACoF,cAAc,GAAG,CAAC,iBAC/CtF,OAAA;QAAK4C,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACnE/C,OAAA;UAAA+C,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfxD,OAAA;UAAA+C,QAAA,GAAO7C,KAAK,CAACoF,cAAc,EAAC,YAAU;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACN,EACAtD,KAAK,CAACqF,aAAa,IAAIrF,KAAK,CAACqF,aAAa,GAAG,CAAC,iBAC7CvF,OAAA;QAAK4C,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACnE/C,OAAA;UAAA+C,QAAA,EAAM;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfxD,OAAA;UAAA+C,QAAA,GAAO7C,KAAK,CAACqF,aAAa,EAAC,WAAS;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACiC,EAAA,GA7RIxF,eAA+C;AA+RrD,eAAeA,eAAe;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}